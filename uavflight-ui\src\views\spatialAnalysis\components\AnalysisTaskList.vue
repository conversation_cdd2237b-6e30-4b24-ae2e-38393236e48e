<template>
  <div class="analysis-tasks">
    <div class="task-header">
      <h4>分析任务</h4>
      <div class="header-actions">
        <el-button type="success" size="small" :icon="Refresh" @click="$emit('refresh')">
          刷新
        </el-button>
        <el-button type="primary" size="small" :icon="Plus" @click="$emit('add-task')">
          添加任务
        </el-button>
      </div>
    </div>

    <div class="task-list">
      <el-empty v-if="tasks.length === 0" description="暂无分析任务">
        <template #image>
          <el-icon size="60" color="#c0c4cc">
            <List />
          </el-icon>
        </template>
        <template #description>
          <p>暂无分析任务</p>
          <p>点击上方按钮添加新任务</p>
        </template>
      </el-empty>

      <div v-else class="task-items">
        <div
          v-for="(task, index) in tasks"
          :key="task.task_id"
          class="task-item"
        >
          <div class="task-info">


            <div class="task-header-row">
              <div class="task-name-with-status">
                <span class="task-name">{{ task.name }}</span>
                <el-tag 
                  :type="getTaskStatusType(task.status)"
                  :class="getTaskStatusClass(task.status)"
                  size="small"
                >
                  {{ task.status }}
                </el-tag>
                <el-tooltip content="查看日志" placement="top" v-if="task.log_file">
                  <el-button
                    size="small"
                    circle
                    type="info"
                    :icon="Document"
                    @click="$emit('view-log', task)"
                  />
                </el-tooltip>
              </div>
            </div>
            
            <!-- 统计信息 -->
            <div v-if="task.spatial_statistics" class="task-statistics">
              <div class="stats-row">
                <span class="stats-label">分析类型:</span>
                <span class="stats-value">{{ getAnalysisCategoryName(task.analysis_category) }}</span>
                <span class="stats-label">对比数据:</span>
                <span class="stats-value">{{ extractFileName(task.old_data_path) }}</span>
              </div>
              <div class="stats-row">
                <span class="stats-label">流出图斑数:</span>
                <span class="stats-value">{{ task.spatial_statistics.outflow_count || 0 }}</span>
                <span class="stats-label">流入图斑数:</span>
                <span class="stats-value">{{ task.spatial_statistics.inflow_count || 0 }}</span>
                <span class="stats-label">总计图斑数:</span>
                <span class="stats-value">{{ task.spatial_statistics.total_count || 0 }}</span>
              </div>
              <div class="stats-row">
                <span class="stats-label">流出面积:</span>
                <span class="stats-value">{{ formatArea(task.spatial_statistics.outflow_area) }}㎡</span>
                <span class="stats-label">流入面积:</span>
                <span class="stats-value">{{ formatArea(task.spatial_statistics.inflow_area) }}㎡</span>
              </div>
            </div>

            <!-- 模型信息 -->
            <div class="task-model-row">
              <div class="model-info">
                <span class="model-label">模型类型:</span>
                <span class="model-value">{{ task.model_type || '-' }}</span>
              </div>
              <div class="model-info">
                <span class="model-label">模型名:</span>
                <el-tooltip :content="task.model_name || '-'" placement="top" 
                            :disabled="!task.model_name || task.model_name.length <= 20">
                  <span class="model-value model-name-truncated">{{ truncateModelName(task.model_name) }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="task-actions">
            <el-tooltip 
              :content="task.status === '完成' ? '查看任务详情' : '任务未完成，无法查看详情'" 
              placement="top"
            >
              <el-button
                size="small"
                type="primary"
                @click="$emit('view-detail', task)"
                :disabled="task.status !== '完成'"
              >
                查看
              </el-button>
            </el-tooltip>
            <el-button size="small" type="danger" @click="$emit('remove-task', index)">
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, List, Refresh, Document } from '@element-plus/icons-vue';

interface SpatialStatistics {
  outflow_count: number;
  inflow_count: number;
  total_count: number;
  outflow_area: number;
  inflow_area: number;
  area_threshold: number;
}

interface AnalysisTask {
  task_id: string;
  name: string;
  type: string;
  status: string;
  createTime: string;
  description?: string;
  analysis_category: string;
  timestamp: number;
  model_type?: string;
  model_name?: string;
  old_data_path?: string;
  spatial_statistics?: SpatialStatistics;
  log_file?: string;
}

interface Props {
  tasks: AnalysisTask[];
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  refresh: [];
  'add-task': [];
  'view-log': [task: AnalysisTask];
  'view-detail': [task: AnalysisTask];
  'remove-task': [index: number];
}>();

// 获取任务状态类型
const getTaskStatusType = (status: string) => {
  switch (status) {
    case '完成':
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '待开始':
      return 'info';
    case '失败':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取任务状态样式类
const getTaskStatusClass = (status: string) => {
  switch (status) {
    case '完成':
    case '已完成':
      return 'status-success';
    case '进行中':
      return 'status-processing';
    case '待开始':
      return 'status-info';
    case '失败':
      return 'status-error';
    default:
      return 'status-info';
  }
};

// 获取分析类型的显示名称
const getAnalysisCategoryName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'arableLand': '耕地',
    'constructionLand': '建设用地'
  };
  return categoryMap[category] || category;
};

// 截断模型名称用于显示
const truncateModelName = (modelName: string | undefined, maxLength: number = 20): string => {
  if (!modelName) return '-';
  if (modelName.length <= maxLength) return modelName;
  return modelName.substring(0, maxLength) + '...';
};

// 从文件路径中提取文件名
const extractFileName = (filePath: string | undefined): string => {
  if (!filePath) return '-';
  
  try {
    // 处理Windows和Unix路径分隔符
    const fileName = filePath.split(/[/\\]/).pop();
    return fileName || '-';
  } catch (error) {
    console.error('提取文件名失败:', error);
    return '-';
  }
};

// 安全地格式化面积数值
const formatArea = (area: number | null | undefined): string => {
  if (area === null || area === undefined || isNaN(area)) {
    return '0.00';
  }
  return area.toFixed(2);
};
</script>

<style scoped lang="scss">
.analysis-tasks {
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
      flex: 1;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .task-list {
    max-height: 400px;
    overflow-y: auto;

    .task-items {
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 16px;
        margin-bottom: 12px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        background-color: #fff;
        transition: all 0.3s ease;

        &:hover {
          border-color: #c6e2ff;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .task-info {
          flex: 1;

          .task-category-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 4px 8px;
            background-color: #f0f2f5;
            border-radius: 4px;

            .category-info {
              display: flex;
              align-items: center;
              gap: 4px;

              .category-label {
                font-size: 12px;
                color: #606266;
              }

              .category-value {
                font-size: 12px;
                font-weight: 600;
                color: #409eff;
              }
            }
          }

          .task-header-row {
            margin-bottom: 12px;

            .task-name-with-status {
              display: flex;
              align-items: center;
              gap: 8px;

              .task-name {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
              }

              .el-tag {
                font-size: 12px;
                
                &.status-success {
                  background-color: #f0f9ff;
                  border-color: #67c23a;
                  color: #67c23a;
                }

                &.status-error {
                  background-color: #fef0f0;
                  border-color: #f56c6c;
                  color: #f56c6c;
                }

                &.status-processing {
                  background-color: #f4f4f5;
                  border-color: #409eff;
                  color: #409eff;
                }

                &.status-info {
                  background-color: #f4f4f5;
                  border-color: #909399;
                  color: #909399;
                }
              }

              .el-button {
                width: 24px;
                height: 24px;
                padding: 0;
                
                .el-icon {
                  font-size: 12px;
                }
              }
            }
          }

          .task-statistics {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 12px;

            .stats-row {
              display: flex;
              gap: 8px;
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }

              .stats-label {
                color: #606266;
                min-width: 60px;
              }

              .stats-value {
                color: #409EFF;
                font-weight: 600;
                margin-right: 12px;
              }
            }
          }

          .task-model-row {
            display: flex;
            gap: 16px;
            padding: 6px 8px;
            background-color: #fafafa;
            border-radius: 4px;
            border-left: 3px solid #e6a23c;

            .model-info {
              display: flex;
              align-items: center;
              gap: 4px;

              .model-label {
                font-size: 12px;
                color: #606266;
                white-space: nowrap;
              }

              .model-value {
                font-size: 12px;
                color: #303133;
                font-weight: 500;

                &.model-name-truncated {
                  max-width: 150px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  cursor: help;
                }
              }
            }
          }
        }

        .task-actions {
          display: flex;
          gap: 8px;

          .el-button {
            &:disabled {
              background-color: #f5f7fa !important;
              border-color: #e4e7ed !important;
              color: #c0c4cc !important;
              cursor: not-allowed !important;

              &:hover {
                background-color: #f5f7fa !important;
                border-color: #e4e7ed !important;
                color: #c0c4cc !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>
