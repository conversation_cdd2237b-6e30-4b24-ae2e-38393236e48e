import{v as K,S as R,o as _,c as b,__tla as Q}from"./index.BSP3cg_z.js";import{d as I,k as h,c as L,w as W,B as M,a as U,b as p,e as G,D as c,v as T,f as m,E as n,G as o,F as P,u as r,t as X}from"./vue.CnN__PXn.js";let q,Y=Promise.all([(()=>{try{return Q}catch{}})()]).then(async()=>{let S,k,$,z,B,j;S={class:"upload-file"},k={class:"el-upload__text"},$={key:0,class:"el-upload__tip"},z={style:{color:"#f56c6c"}},B={style:{color:"#f56c6c"}},j=I({name:"upload-file"}),q=I({...j,props:{modelValue:[String,Array],limit:{type:Number,default:5},fileSize:{type:Number,default:100},fileType:{type:Array,default:()=>["png","jpg","jpeg","doc","xls","ppt","txt","pdf","docx","xlsx","pptx"]},isShowTip:{type:Boolean,default:!0},disabledUpload:{type:Boolean,default:!1},uploadFileUrl:{type:String,default:"/admin/sys-file/upload"},type:{type:String,default:"default",validator:u=>["default","simple"].includes(u)},data:{type:Object,default:{}},dir:{type:String,default:""},autoUpload:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(u,{expose:C,emit:H}){const l=u,y=H,f=h(0),i=h([]),v=h([]),g=h(),{t:w}=K.useI18n(),A=L(()=>({Authorization:"Bearer "+R.get("token"),"TENANT-ID":R.getTenant()})),V=L(()=>Object.assign(l.data,{dir:l.dir})),F=e=>{if(l.fileType.length){const a=e.name.split("."),t=a[a.length-1];if(!(l.fileType.indexOf(t)>=0))return b().error(`${w("excel.typeErrorText")} ${l.fileType.join("/")}!`),!1}return l.fileSize&&!(e.size/1024/1024<l.fileSize)?(b().error(`${w("excel.sizeErrorText")} ${l.fileSize} MB!`),!1):(f.value++,!0)};function N(e,a){e.code===0?(v.value.push({name:a.name,url:e.data.url}),E()):(f.value--,b().error(e.msg),g.value.handleRemove(a),E())}const E=()=>{f.value>0&&v.value.length===f.value&&(i.value=i.value.filter(e=>e.url!==void 0).concat(v.value),v.value=[],f.value=0,y("change",x(i.value)),y("update:modelValue",x(i.value)))},D=(e,a)=>{i.value=a.filter(t=>t!==e.url),y("change",x(i.value)),y("update:modelValue",x(i.value))},J=e=>{_.downBlobFile(e.url,{},e.name)},x=(e,a=",")=>{let t="";a=a||",";for(let s in e)e[s].url&&(t+=e[s].url+a);return t!==""?t.substr(0,t.length-1):""},O=()=>{b().error("\u4E0A\u4F20\u6587\u4EF6\u5931\u8D25")};return W(()=>l.modelValue,e=>{var a;if(!e)return i.value=[],[];{let t=1;const s=Array.isArray(e)?e:(a=l==null?void 0:l.modelValue)==null?void 0:a.split(",");i.value=s.map(d=>(typeof d=="string"&&(d={name:d,url:d}),d.uid=d.uid||new Date().getTime()+t++,d))}},{deep:!0,immediate:!0}),C({submit:()=>{g.value.submit()}}),(e,a)=>{const t=M("el-upload"),s=M("el-button");return p(),U("div",S,[l.type==="default"?(p(),G(t,{key:0,ref_key:"fileUpload",ref:g,action:e.baseURL+r(_).adaptationUrl(l.uploadFileUrl),"before-upload":F,"file-list":r(i),headers:r(A),limit:u.limit,"on-error":O,"on-remove":D,"on-preview":J,data:r(V),"auto-upload":u.autoUpload,"on-success":N,class:"upload-file-uploader",disabled:u.disabledUpload,drag:"",multiple:""},{tip:T(()=>[l.isShowTip?(p(),U("div",$,[n(o(e.$t("excel.pleaseUpload"))+" ",1),l.fileSize?(p(),U(P,{key:0},[n(o(e.$t("excel.size"))+" ",1),m("b",z,o(l.fileSize)+"MB",1)],64)):c("",!0),l.fileType?(p(),U(P,{key:1},[n(o(e.$t("excel.format"))+" ",1),m("b",B,o(l.fileType.join("/")),1)],64)):c("",!0),n(" "+o(e.$t("excel.file")),1)])):c("",!0)]),default:T(()=>[a[0]||(a[0]=m("i",{class:"el-icon-upload"},null,-1)),m("div",k,[n(o(e.$t("excel.operationNotice"))+" ",1),m("em",null,o(e.$t("excel.clickUpload")),1)])]),_:1},8,["action","file-list","headers","limit","data","auto-upload","disabled"])):c("",!0),l.type==="simple"?(p(),G(t,{key:1,ref_key:"fileUpload",ref:g,action:e.baseURL+r(_).adaptationUrl(l.uploadFileUrl),"before-upload":F,"file-list":r(i),headers:r(A),limit:u.limit,"auto-upload":u.autoUpload,"on-error":O,"on-remove":D,data:r(V),"on-success":N,class:"upload-file-uploader",multiple:""},{default:T(()=>[X(s,{type:"primary",link:""},{default:T(()=>[n(o(e.$t("excel.clickUpload")),1)]),_:1})]),_:1},8,["action","file-list","headers","limit","auto-upload","data"])):c("",!0)])}}})});export{Y as __tla,q as default};
