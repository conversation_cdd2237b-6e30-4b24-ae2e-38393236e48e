const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/list.CynFl8-6.js","assets/vue.CnN__PXn.js","assets/index.BSP3cg_z.js","assets/index.DjeikzAe.css","assets/list.Cb-2EC7S.css"])))=>i.map(i=>d[i]);
import{aq as E,a as G,__tla as N}from"./index.BSP3cg_z.js";import{y as m,d as A,k as j,A as U,c as q,o as F,w as H,B as p,a as J,b as K,t as r,v as d,f as z,G as M,u as g,j as Q}from"./vue.CnN__PXn.js";let O,X=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{let u,y,b,P,v;u={ele:()=>new Promise((c,f)=>{m(()=>{const l=E,o=[];for(const s in l)o.push(`ele-${l[s].name}`);o.length>0?c(o):f("\u672A\u83B7\u53D6\u5230\u503C\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5")})}),awe:()=>new Promise((c,f)=>{m(()=>{const l=document.styleSheets;let o=[],s=[];for(let n=0;n<l.length;n++)l[n].href&&l[n].href.indexOf("font-awesome")>-1&&o.push(l[n]);for(let n=0;n<o.length;n++)for(let e=0;e<o[n].cssRules.length;e++)o[n].cssRules[e].selectorText&&o[n].cssRules[e].selectorText.indexOf(".fa-")===0&&o[n].cssRules[e].selectorText.indexOf(",")===-1&&/::before/.test(o[n].cssRules[e].selectorText)&&s.push(`${o[n].cssRules[e].selectorText.substring(1,o[n].cssRules[e].selectorText.length).replace(/\:\:before/gi,"")}`);s.length>0?c(s.reverse()):f("\u672A\u83B7\u53D6\u5230\u503C\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5")})}),local:()=>new Promise((c,f)=>{m(()=>{let l=[];const o=document.getElementById("local-icon");o!=null&&o.dataset.iconName&&(l=(o==null?void 0:o.dataset.iconName).split(",")),l.length>0?c(l):f("No Local Icons")})})},y={class:"icon-selector w100 h100"},b={class:"icon-selector-warp"},P={class:"icon-selector-warp-title"},v=A({name:"iconSelector"}),O=A({...v,props:{prepend:{type:String,default:()=>"ele-Pointer"},placeholder:{type:String,default:()=>"\u8BF7\u8F93\u5165\u5185\u5BB9\u641C\u7D22\u56FE\u6807\u6216\u8005\u9009\u62E9\u56FE\u6807"},size:{type:String,default:()=>"default"},title:{type:String,default:()=>""},disabled:{type:Boolean,default:()=>!1},clearable:{type:Boolean,default:()=>!0},emptyDescription:{type:String,default:()=>"\u65E0\u76F8\u5173\u56FE\u6807"},modelValue:String},emits:["update:modelValue","get","clear"],setup(c,{emit:f}){const l=c,o=f,s=Q(()=>G(()=>import("./list.CynFl8-6.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([0,1,2,3,4]))),n=j(),e=U({fontIconPrefix:"",fontIconWidth:0,fontIconSearch:"",fontIconPlaceholder:"",fontIconTabActive:"ele",fontIconList:{ali:[],ele:[],awe:[],local:[]}}),R=()=>{if(!l.modelValue)return!1;e.fontIconSearch="",e.fontIconPlaceholder=l.modelValue},$=()=>{const t=V();setTimeout(()=>{t.filter(a=>a===e.fontIconSearch).length<=0&&(e.fontIconSearch="")},300)},I=q(()=>{const t=V();if(!e.fontIconSearch)return t;let a=e.fontIconSearch.trim().toLowerCase();return t.filter(i=>{if(i.toLowerCase().indexOf(a)!==-1)return i})}),V=()=>{let t=[];return e.fontIconTabActive==="ali"?t=e.fontIconList.ali:e.fontIconTabActive==="ele"?t=e.fontIconList.ele:e.fontIconTabActive==="awe"?t=e.fontIconList.awe:e.fontIconTabActive==="local"&&(t=e.fontIconList.local),t},_=()=>{if(l.modelValue==="")return e.fontIconPlaceholder=l.placeholder;e.fontIconPlaceholder=l.modelValue,e.fontIconPrefix=l.modelValue},L=()=>{let t="ele";return l.modelValue&&(l.modelValue.indexOf("iconfont")>-1?t="ali":l.modelValue.indexOf("ele-")>-1?t="ele":l.modelValue.indexOf("fa")>-1?t="awe":l.modelValue.indexOf("local")>-1&&(t="local"),e.fontIconTabActive=t),t},S=async t=>{if(t==="ali"){if(e.fontIconList.ali.length>0)return;await u.ali().then(a=>{e.fontIconList.ali=a.map(i=>`iconfont ${i}`)})}else if(t==="ele"){if(e.fontIconList.ele.length>0)return;await u.ele().then(a=>{e.fontIconList.ele=a})}else if(t==="awe"){if(e.fontIconList.awe.length>0)return;await u.awe().then(a=>{e.fontIconList.awe=a.map(i=>`fa ${i}`)})}else if(t==="local"){if(e.fontIconList.local.length>0)return;await u.local().then(a=>{e.fontIconList.local=a.map(i=>`${i}`)})}e.fontIconPlaceholder=l.placeholder,_()},B=t=>{S(t.paneName),n.value.focus()},h=t=>{e.fontIconPlaceholder=t,e.fontIconPrefix=t,o("get",e.fontIconPrefix),o("update:modelValue",e.fontIconPrefix),n.value.focus()},D=()=>{e.fontIconPrefix="",o("clear",e.fontIconPrefix),o("update:modelValue",e.fontIconPrefix)},T=()=>{m(()=>{e.fontIconWidth=n.value.$el.offsetWidth})};return F(()=>{S(L()),window.addEventListener("resize",()=>{T()}),T()}),H(()=>l.modelValue,()=>{_(),L()}),(t,a)=>{const i=p("SvgIcon"),W=p("el-input"),x=p("el-tab-pane"),k=p("el-tabs"),C=p("el-popover");return K(),J("div",y,[r(W,{modelValue:e.fontIconSearch,"onUpdate:modelValue":a[0]||(a[0]=w=>e.fontIconSearch=w),placeholder:e.fontIconPlaceholder,clearable:c.clearable,disabled:c.disabled,size:c.size,ref_key:"inputWidthRef",ref:n,onClear:D,onFocus:R,onBlur:$},{prepend:d(()=>[r(i,{name:e.fontIconPrefix===""?c.prepend:e.fontIconPrefix,class:"font14"},null,8,["name"])]),_:1},8,["modelValue","placeholder","clearable","disabled","size"]),r(C,{placement:"bottom",width:e.fontIconWidth,transition:"el-zoom-in-top","popper-class":"icon-selector-popper",trigger:"click","virtual-ref":n.value,"virtual-triggering":""},{default:d(()=>[z("div",b,[z("div",P,M(c.title),1),r(k,{modelValue:e.fontIconTabActive,"onUpdate:modelValue":a[1]||(a[1]=w=>e.fontIconTabActive=w),onTabClick:B},{default:d(()=>[r(x,{lazy:"",label:"ele",name:"ele"},{default:d(()=>[r(g(s),{list:I.value,empty:c.emptyDescription,prefix:e.fontIconPrefix,onGetIcon:h},null,8,["list","empty","prefix"])]),_:1}),r(x,{lazy:"",label:"awe",name:"awe"},{default:d(()=>[r(g(s),{list:I.value,empty:c.emptyDescription,prefix:e.fontIconPrefix,onGetIcon:h},null,8,["list","empty","prefix"])]),_:1}),r(x,{lazy:"",label:"local",name:"local"},{default:d(()=>[r(g(s),{list:I.value,empty:c.emptyDescription,prefix:e.fontIconPrefix,onGetIcon:h},null,8,["list","empty","prefix"])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["width","virtual-ref"])])}}})});export{X as __tla,O as default};
