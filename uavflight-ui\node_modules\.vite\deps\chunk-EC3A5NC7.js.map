{"version": 3, "sources": ["../../ol/geom/Circle.js"], "sourcesContent": ["/**\n * @module ol/geom/Circle\n */\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {createOrUpdate, for<PERSON>ach<PERSON>orn<PERSON>, intersects} from '../extent.js';\nimport {deflateCoordinate} from './flat/deflate.js';\nimport {rotate} from './flat/transform.js';\n\n/**\n * @classdesc\n * Circle geometry.\n *\n * @api\n */\nclass Circle extends SimpleGeometry {\n  /**\n   * @param {!import(\"../coordinate.js\").Coordinate} center Center.\n   *     For internal use, flat coordinates in combination with `layout` and no\n   *     `radius` are also accepted.\n   * @param {number} [radius] Radius in units of the projection.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(center, radius, layout) {\n    super();\n    if (layout !== undefined && radius === undefined) {\n      this.setFlatCoordinates(layout, center);\n    } else {\n      radius = radius ? radius : 0;\n      this.setCenterAndRadius(center, radius, layout);\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Circle} Clone.\n   * @api\n   */\n  clone() {\n    const circle = new Circle(\n      this.flatCoordinates.slice(),\n      undefined,\n      this.layout\n    );\n    circle.applyProperties(this);\n    return circle;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    const flatCoordinates = this.flatCoordinates;\n    const dx = x - flatCoordinates[0];\n    const dy = y - flatCoordinates[1];\n    const squaredDistance = dx * dx + dy * dy;\n    if (squaredDistance < minSquaredDistance) {\n      if (squaredDistance === 0) {\n        for (let i = 0; i < this.stride; ++i) {\n          closestPoint[i] = flatCoordinates[i];\n        }\n      } else {\n        const delta = this.getRadius() / Math.sqrt(squaredDistance);\n        closestPoint[0] = flatCoordinates[0] + delta * dx;\n        closestPoint[1] = flatCoordinates[1] + delta * dy;\n        for (let i = 2; i < this.stride; ++i) {\n          closestPoint[i] = flatCoordinates[i];\n        }\n      }\n      closestPoint.length = this.stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    const flatCoordinates = this.flatCoordinates;\n    const dx = x - flatCoordinates[0];\n    const dy = y - flatCoordinates[1];\n    return dx * dx + dy * dy <= this.getRadiusSquared_();\n  }\n\n  /**\n   * Return the center of the circle as {@link module:ol/coordinate~Coordinate coordinate}.\n   * @return {import(\"../coordinate.js\").Coordinate} Center.\n   * @api\n   */\n  getCenter() {\n    return this.flatCoordinates.slice(0, this.stride);\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    const flatCoordinates = this.flatCoordinates;\n    const radius = flatCoordinates[this.stride] - flatCoordinates[0];\n    return createOrUpdate(\n      flatCoordinates[0] - radius,\n      flatCoordinates[1] - radius,\n      flatCoordinates[0] + radius,\n      flatCoordinates[1] + radius,\n      extent\n    );\n  }\n\n  /**\n   * Return the radius of the circle.\n   * @return {number} Radius.\n   * @api\n   */\n  getRadius() {\n    return Math.sqrt(this.getRadiusSquared_());\n  }\n\n  /**\n   * @private\n   * @return {number} Radius squared.\n   */\n  getRadiusSquared_() {\n    const dx = this.flatCoordinates[this.stride] - this.flatCoordinates[0];\n    const dy = this.flatCoordinates[this.stride + 1] - this.flatCoordinates[1];\n    return dx * dx + dy * dy;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'Circle';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    const circleExtent = this.getExtent();\n    if (intersects(extent, circleExtent)) {\n      const center = this.getCenter();\n\n      if (extent[0] <= center[0] && extent[2] >= center[0]) {\n        return true;\n      }\n      if (extent[1] <= center[1] && extent[3] >= center[1]) {\n        return true;\n      }\n\n      return forEachCorner(extent, this.intersectsCoordinate.bind(this));\n    }\n    return false;\n  }\n\n  /**\n   * Set the center of the circle as {@link module:ol/coordinate~Coordinate coordinate}.\n   * @param {import(\"../coordinate.js\").Coordinate} center Center.\n   * @api\n   */\n  setCenter(center) {\n    const stride = this.stride;\n    const radius = this.flatCoordinates[stride] - this.flatCoordinates[0];\n    const flatCoordinates = center.slice();\n    flatCoordinates[stride] = flatCoordinates[0] + radius;\n    for (let i = 1; i < stride; ++i) {\n      flatCoordinates[stride + i] = center[i];\n    }\n    this.setFlatCoordinates(this.layout, flatCoordinates);\n    this.changed();\n  }\n\n  /**\n   * Set the center (as {@link module:ol/coordinate~Coordinate coordinate}) and the radius (as\n   * number) of the circle.\n   * @param {!import(\"../coordinate.js\").Coordinate} center Center.\n   * @param {number} radius Radius.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCenterAndRadius(center, radius, layout) {\n    this.setLayout(layout, center, 0);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    /** @type {Array<number>} */\n    const flatCoordinates = this.flatCoordinates;\n    let offset = deflateCoordinate(flatCoordinates, 0, center, this.stride);\n    flatCoordinates[offset++] = flatCoordinates[0] + radius;\n    for (let i = 1, ii = this.stride; i < ii; ++i) {\n      flatCoordinates[offset++] = flatCoordinates[i];\n    }\n    flatCoordinates.length = offset;\n    this.changed();\n  }\n\n  getCoordinates() {\n    return null;\n  }\n\n  setCoordinates(coordinates, layout) {}\n\n  /**\n   * Set the radius of the circle. The radius is in the units of the projection.\n   * @param {number} radius Radius.\n   * @api\n   */\n  setRadius(radius) {\n    this.flatCoordinates[this.stride] = this.flatCoordinates[0] + radius;\n    this.changed();\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in counter-clockwise radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   */\n  rotate(angle, anchor) {\n    const center = this.getCenter();\n    const stride = this.getStride();\n    this.setCenter(\n      rotate(center, 0, center.length, stride, angle, anchor, center)\n    );\n    this.changed();\n  }\n}\n\n/**\n * Transform each coordinate of the circle from one coordinate reference system\n * to another. The geometry is modified in place.\n * If you do not want the geometry modified in place, first clone() it and\n * then use this function on the clone.\n *\n * Internally a circle is currently represented by two points: the center of\n * the circle `[cx, cy]`, and the point to the right of the circle\n * `[cx + r, cy]`. This `transform` function just transforms these two points.\n * So the resulting geometry is also a circle, and that circle does not\n * correspond to the shape that would be obtained by transforming every point\n * of the original circle.\n *\n * @param {import(\"../proj.js\").ProjectionLike} source The current projection.  Can be a\n *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n * @param {import(\"../proj.js\").ProjectionLike} destination The desired projection.  Can be a\n *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n * @return {Circle} This geometry.  Note that original geometry is\n *     modified in place.\n * @function\n * @api\n */\nCircle.prototype.transform;\nexport default Circle;\n"], "mappings": ";;;;;;;;;;;;AAcA,IAAM,SAAN,MAAM,gBAAe,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,YAAY,QAAQ,QAAQ,QAAQ;AAClC,UAAM;AACN,QAAI,WAAW,UAAa,WAAW,QAAW;AAChD,WAAK,mBAAmB,QAAQ,MAAM;AAAA,IACxC,OAAO;AACL,eAAS,SAAS,SAAS;AAC3B,WAAK,mBAAmB,QAAQ,QAAQ,MAAM;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,SAAS,IAAI;AAAA,MACjB,KAAK,gBAAgB,MAAM;AAAA,MAC3B;AAAA,MACA,KAAK;AAAA,IACP;AACA,WAAO,gBAAgB,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,UAAM,kBAAkB,KAAK;AAC7B,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAM,kBAAkB,KAAK,KAAK,KAAK;AACvC,QAAI,kBAAkB,oBAAoB;AACxC,UAAI,oBAAoB,GAAG;AACzB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,uBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,QACrC;AAAA,MACF,OAAO;AACL,cAAM,QAAQ,KAAK,UAAU,IAAI,KAAK,KAAK,eAAe;AAC1D,qBAAa,CAAC,IAAI,gBAAgB,CAAC,IAAI,QAAQ;AAC/C,qBAAa,CAAC,IAAI,gBAAgB,CAAC,IAAI,QAAQ;AAC/C,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,uBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,QACrC;AAAA,MACF;AACA,mBAAa,SAAS,KAAK;AAC3B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,UAAM,kBAAkB,KAAK;AAC7B,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,WAAO,KAAK,KAAK,KAAK,MAAM,KAAK,kBAAkB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,QAAQ;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,gBAAgB,KAAK,MAAM,IAAI,gBAAgB,CAAC;AAC/D,WAAO;AAAA,MACL,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK,KAAK,KAAK,kBAAkB,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,UAAM,KAAK,KAAK,gBAAgB,KAAK,MAAM,IAAI,KAAK,gBAAgB,CAAC;AACrE,UAAM,KAAK,KAAK,gBAAgB,KAAK,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAC;AACzE,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,UAAM,eAAe,KAAK,UAAU;AACpC,QAAI,WAAW,QAAQ,YAAY,GAAG;AACpC,YAAM,SAAS,KAAK,UAAU;AAE9B,UAAI,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACpD,eAAO;AAAA,MACT;AACA,UAAI,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACpD,eAAO;AAAA,MACT;AAEA,aAAO,cAAc,QAAQ,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK,gBAAgB,MAAM,IAAI,KAAK,gBAAgB,CAAC;AACpE,UAAM,kBAAkB,OAAO,MAAM;AACrC,oBAAgB,MAAM,IAAI,gBAAgB,CAAC,IAAI;AAC/C,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,sBAAgB,SAAS,CAAC,IAAI,OAAO,CAAC;AAAA,IACxC;AACA,SAAK,mBAAmB,KAAK,QAAQ,eAAe;AACpD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,QAAQ,QAAQ,QAAQ;AACzC,SAAK,UAAU,QAAQ,QAAQ,CAAC;AAChC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,UAAM,kBAAkB,KAAK;AAC7B,QAAI,SAAS,kBAAkB,iBAAiB,GAAG,QAAQ,KAAK,MAAM;AACtE,oBAAgB,QAAQ,IAAI,gBAAgB,CAAC,IAAI;AACjD,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,sBAAgB,QAAQ,IAAI,gBAAgB,CAAC;AAAA,IAC/C;AACA,oBAAgB,SAAS;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EAEA,eAAe,aAAa,QAAQ;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrC,UAAU,QAAQ;AAChB,SAAK,gBAAgB,KAAK,MAAM,IAAI,KAAK,gBAAgB,CAAC,IAAI;AAC9D,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,OAAO,QAAQ;AACpB,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,SAAS,KAAK,UAAU;AAC9B,SAAK;AAAA,MACH,OAAO,QAAQ,GAAG,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAAA,IAChE;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAwBA,OAAO,UAAU;AACjB,IAAO,iBAAQ;", "names": []}