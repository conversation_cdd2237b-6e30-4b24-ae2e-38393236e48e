const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/form.nLpc5aHo.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/dict.DrX0Qdnc.js","assets/dict.D9OX-VAS.js","assets/job.qUTxkmHi.js","assets/job-log.BFpXWIdP.js","assets/table.CCFM44Zd.js"])))=>i.map(i=>d[i]);
import{v as ye,a as O,c as p,d as k,__tla as he}from"./index.BSP3cg_z.js";import{d as J,k as S,A as K,B as c,m as Q,a as q,b as n,f as M,t as o,q as h,x as fe,u as e,v as t,I as we,F as W,p as X,e as m,E as _,G as v,H as ve,D as Y,J as xe,j as Z}from"./vue.CnN__PXn.js";import{u as Se,__tla as ge}from"./table.CCFM44Zd.js";import{f as Ce,s as ke,b as Te,r as Ee,d as Ne,__tla as $e}from"./job.qUTxkmHi.js";import{u as qe,__tla as Ve}from"./dict.DrX0Qdnc.js";import{__tla as Fe}from"./dict.D9OX-VAS.js";let ee,Pe=Promise.all([(()=>{try{return he}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Fe}catch{}})()]).then(async()=>{let V,F,P,I;V={class:"layout-padding"},F={class:"layout-padding-auto layout-padding-view"},P={class:"mb8",style:{width:"100%"}},I=J({name:"systemSysJob"}),ee=J({...I,setup(Ie){const oe=Z(()=>O(()=>import("./form.nLpc5aHo.js").then(async s=>(await s.__tla,s)),__vite__mapDeps([0,1,2,3,4,5,6]))),le=Z(()=>O(()=>import("./job-log.BFpXWIdP.js").then(async s=>(await s.__tla,s)),__vite__mapDeps([7,8,1,2,3,4,5]))),{t:a}=ye.useI18n(),T=S(),D=S(),E=K({jobName:"",jobGroup:"",jobStatus:"",jobExecuteStatus:""}),g=S(!0),B=S([]),R=S(!0),{job_status:L,job_execute_status:z,misfire_policy:te,job_type:ae}=qe("job_status","job_execute_status","misfire_policy","job_type"),b=K({queryForm:E,pageList:Ce}),{getDataList:j,currentChangeHandle:re,sizeChangeHandle:se,downBlobFile:ue,tableStyle:G}=Se(b),ie=()=>{Object.keys(E).forEach(s=>E[s]=""),j()},ne=s=>{B.value=s,R.value=!s.length},be=()=>{ue("/job/sys-job/export",b.queryForm,"job.xlsx")},N=async s=>{if(!s)return void B.value.forEach(N);const{jobId:r,jobName:$}=s;try{await k().confirm(`${a("common.delConfirmText")}(\u4EFB\u52A1\u540D\u79F0:${$})`)}catch{return}try{await Ne(r),j(),p().success(a("common.delSuccessText"))}catch{p().error("\u5220\u9664\u5931\u8D25")}};return(s,r)=>{const $=c("el-input"),x=c("el-form-item"),H=c("el-option"),U=c("el-select"),d=c("el-button"),ce=c("el-form"),A=c("el-row"),pe=c("right-toolbar"),u=c("el-table-column"),C=c("dict-tag"),de=c("el-table"),me=c("pagination"),w=Q("auth"),_e=Q("loading");return n(),q("div",V,[M("div",F,[h(o(A,{class:"ml10"},{default:t(()=>[o(ce,{inline:!0,model:e(b).queryForm,ref:"queryRef"},{default:t(()=>[o(x,{label:s.$t("job.jobName"),prop:"jobName"},{default:t(()=>[o($,{placeholder:s.$t("job.inputjobNameTip"),onKeyup:we(e(j),["enter"]),clearable:"",modelValue:e(b).queryForm.jobName,"onUpdate:modelValue":r[0]||(r[0]=l=>e(b).queryForm.jobName=l)},null,8,["placeholder","onKeyup","modelValue"])]),_:1},8,["label"]),o(x,{label:e(a)("job.jobStatus"),prop:"jobStatus"},{default:t(()=>[o(U,{placeholder:e(a)("job.inputjobStatusTip"),modelValue:e(b).queryForm.jobStatus,"onUpdate:modelValue":r[1]||(r[1]=l=>e(b).queryForm.jobStatus=l)},{default:t(()=>[(n(!0),q(W,null,X(e(L),(l,y)=>(n(),m(H,{key:y,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["placeholder","modelValue"])]),_:1},8,["label"]),o(x,{label:e(a)("job.jobExecuteStatus"),prop:"jobExecuteStatus"},{default:t(()=>[o(U,{placeholder:e(a)("job.inputjobExecuteStatusTip"),modelValue:e(b).queryForm.jobExecuteStatus,"onUpdate:modelValue":r[2]||(r[2]=l=>e(b).queryForm.jobExecuteStatus=l)},{default:t(()=>[(n(!0),q(W,null,X(e(z),(l,y)=>(n(),m(H,{key:y,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["placeholder","modelValue"])]),_:1},8,["label"]),o(x,null,{default:t(()=>[o(d,{onClick:e(j),icon:"Search",type:"primary"},{default:t(()=>[_(v(s.$t("common.queryBtn")),1)]),_:1},8,["onClick"]),o(d,{onClick:ie,icon:"Refresh"},{default:t(()=>[_(v(s.$t("common.resetBtn")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},512),[[fe,e(g)]]),o(A,null,{default:t(()=>[M("div",P,[h((n(),m(d,{onClick:r[3]||(r[3]=l=>e(T).openDialog()),class:"ml10",icon:"folder-add",type:"primary"},{default:t(()=>[_(v(s.$t("common.addBtn")),1)]),_:1})),[[w,"job_sys_job_add"]]),h((n(),m(d,{plain:"",disabled:e(R),onClick:r[4]||(r[4]=l=>N(void 0)),class:"ml10",icon:"Delete",type:"primary"},{default:t(()=>[_(v(s.$t("common.delBtn")),1)]),_:1},8,["disabled"])),[[w,"job_sys_job_del"]]),o(pe,{export:"job_sys_job_add",onExportExcel:be,onQueryTable:e(j),class:"ml10",style:{float:"right","margin-right":"20px"},showSearch:e(g),"onUpdate:showSearch":r[5]||(r[5]=l=>ve(g)?g.value=l:null)},null,8,["onQueryTable","showSearch"])])]),_:1}),h((n(),m(de,{data:e(b).dataList,onSelectionChange:ne,style:{width:"100%"},border:"","cell-style":e(G).cellStyle,"header-cell-style":e(G).headerCellStyle},{default:t(()=>[o(u,{align:"center",type:"selection",width:"40"}),o(u,{label:e(a)("job.index"),fixed:"left",type:"index",width:"60"},null,8,["label"]),o(u,{label:e(a)("job.jobName"),fixed:"left",prop:"jobName","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.jobGroup"),prop:"jobGroup","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.jobStatus"),prop:"jobStatus","show-overflow-tooltip":"",width:"120"},{default:t(l=>[o(C,{options:e(L),value:l.row.jobStatus},null,8,["options","value"])]),_:1},8,["label"]),o(u,{label:e(a)("job.jobExecuteStatus"),prop:"jobExecuteStatus","show-overflow-tooltip":"",width:"120"},{default:t(l=>[o(C,{options:e(z),value:l.row.jobExecuteStatus},null,8,["options","value"])]),_:1},8,["label"]),o(u,{label:e(a)("job.startTime"),prop:"startTime","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.previousTime"),prop:"previousTime","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.nextTime"),prop:"nextTime","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.jobType"),prop:"jobType","show-overflow-tooltip":"",width:"120"},{default:t(l=>[o(C,{options:e(ae),value:l.row.jobType},null,8,["options","value"])]),_:1},8,["label"]),o(u,{label:e(a)("job.executePath"),prop:"executePath","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.className"),prop:"className","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.methodName"),prop:"methodName","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.methodParamsValue"),prop:"methodParamsValue","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.cronExpression"),prop:"cronExpression","show-overflow-tooltip":"",width:"120"},null,8,["label"]),o(u,{label:e(a)("job.misfirePolicy"),prop:"misfirePolicy","show-overflow-tooltip":"",width:"200"},{default:t(l=>[o(C,{options:e(te),value:l.row.misfirePolicy},null,8,["options","value"])]),_:1},8,["label"]),o(u,{label:s.$t("common.action"),fixed:"right",width:"300"},{default:t(l=>[o(d,{onClick:y=>{return i=l.row,void D.value.openDialog(i.jobId);var i},text:"",type:"primary"},{default:t(()=>r[7]||(r[7]=[_("\u65E5\u5FD7")])),_:2},1032,["onClick"]),l.row.jobStatus!=="2"?h((n(),m(d,{key:0,onClick:y=>(async i=>{const f=i.jobStatus;if(f==="1"||f==="3"){try{await k().confirm(`\u5373\u5C06\u53D1\u5E03\u6216\u542F\u52A8(\u4EFB\u52A1\u540D\u79F0: ${i.jobName}), \u662F\u5426\u7EE7\u7EED?`)}catch{return}try{await ke(i.jobId),j(),p().success(a("common.optSuccessText"))}catch(je){p().error(je.msg)}}else p().error("\u5B9A\u65F6\u4EFB\u52A1\u5DF2\u8FD0\u884C")})(l.row),text:"",type:"primary"},{default:t(()=>r[8]||(r[8]=[_("\u542F\u52A8 ")])),_:2},1032,["onClick"])),[[w,"job_sys_job_start_job"]]):Y("",!0),l.row.jobStatus==="2"?h((n(),m(d,{key:1,onClick:y=>(async i=>{if(i.jobStatus==="2"){try{await k().confirm(`\u5373\u5C06\u6682\u505C(\u4EFB\u52A1\u540D\u79F0: ${i.jobName}), \u662F\u5426\u7EE7\u7EED?`)}catch{return}try{await Te(i.jobId),j(),p().success(a("common.optSuccessText"))}catch(f){p().error(f.msg)}}else p().error("\u5DF2\u6682\u505C\uFF0C\u4E0D\u8981\u91CD\u590D\u64CD\u4F5C")})(l.row),text:"",type:"primary"},{default:t(()=>r[9]||(r[9]=[_("\u6682\u505C ")])),_:2},1032,["onClick"])),[[w,"job_sys_job_shutdown_job"]]):Y("",!0),h((n(),m(d,{onClick:y=>(i=>{const f=i.jobStatus;f==="1"||f==="3"?T.value.openDialog(i.jobId):p().error("\u8FD0\u884C\u4E2D\u5B9A\u65F6\u4EFB\u52A1\u4E0D\u53EF\u4FEE\u6539\uFF0C\u8BF7\u5148\u6682\u505C\u540E\u64CD\u4F5C")})(l.row),text:"",type:"primary"},{default:t(()=>[_(v(s.$t("common.editBtn")),1)]),_:2},1032,["onClick"])),[[w,"job_sys_job_edit"]]),h((n(),m(d,{onClick:y=>(async i=>{try{await k().confirm(`\u7ACB\u523B\u6267\u884C\u4E00\u6B21\u4EFB\u52A1(\u4EFB\u52A1\u540D\u79F0: ${i.jobName}), \u662F\u5426\u7EE7\u7EED?`)}catch{return}try{await Ee(i.jobId),j(),p().success(a("common.optSuccessText"))}catch{p().error("\u8FD0\u884C\u5931\u8D25")}})(l.row),text:"",type:"primary"},{default:t(()=>r[10]||(r[10]=[_("\u6267\u884C")])),_:2},1032,["onClick"])),[[w,"job_sys_job_start_job"]]),h((n(),m(d,{onClick:y=>N(l.row),text:"",type:"primary"},{default:t(()=>[_(v(s.$t("common.delBtn")),1)]),_:2},1032,["onClick"])),[[w,"job_sys_job_del"]])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[_e,e(b).loading]]),o(me,xe({onCurrentChange:e(re),onSizeChange:e(se)},e(b).pagination),null,16,["onCurrentChange","onSizeChange"])]),o(e(oe),{onRefresh:r[6]||(r[6]=l=>e(j)()),ref_key:"formDialogRef",ref:T},null,512),o(e(le),{ref_key:"jobLogRef",ref:D},null,512)])}}})});export{Pe as __tla,ee as default};
