import{s as p,q as L,__tla as S}from"./index.BSP3cg_z.js";import{i as b,u as k,a as C,b as E,c as B,d as O,e as R,f as q}from"./echarts.DrVj8Jfx.js";import{d as v,k as c,w as u,o as w,S as I,a as h,b as y,n as P,i as T,D as W,f as j,y as K}from"./vue.CnN__PXn.js";let D,x,_,A,z,M=Promise.all([(()=>{try{return S}catch{}})()]).then(async()=>{_=function(a){return p({url:"/admin//inspect/report/getInspectReportData",method:"get",params:a})},z=function(a){return p({url:"/admin//inspect/report/getBusinessTypeAndEventCountData",method:"get",params:a})},A=function(a){return p({url:"/admin//inspect/report/getEventTop10Data",method:"get",params:a})};let m,g;x=v({__name:"index",props:{innerData:{},outerData:{},width:{default:"100%"},height:{default:"500px"},showLegend:{type:Boolean}},setup(a){const t=a,o=c();let e=null;const n=6,d=6,l=()=>{if(!e)return;const r={tooltip:{trigger:"item",formatter:"{a}<br/>{b}: {c} ({d}%)"},legend:t.showLegend?{type:"scroll",orient:"horizontal"}:{show:!1},series:[{name:"\u4E1A\u52A1\u573A\u666F",type:"pie",radius:["0%","40%"],data:t.innerData,label:{show:!1},itemStyle:{borderRadius:n,borderWidth:2,borderColor:"#fff",opacity:1},emphasis:{itemStyle:{shadowBlur:20,shadowColor:"rgba(0, 0, 0, 0.3)"}}},{name:"\u4E8B\u4EF6\u7C7B\u578B",type:"pie",radius:["40%","75%"],data:t.outerData,itemStyle:{borderRadius:d,borderWidth:2,borderColor:"#fff",opacity:1},label:{show:!0,formatter:`{b}
{d}%`,fontSize:14},emphasis:{itemStyle:{shadowBlur:20,shadowColor:"rgba(0, 0, 0, 0.3)"}}}]};e.setOption(r)};return u(()=>[t.innerData,t.outerData],()=>{e==null||e.setOption({series:[{data:t.innerData},{data:t.outerData}]})}),w(()=>{o.value&&(e=b(o.value),l()),window.addEventListener("resize",()=>e==null?void 0:e.resize())}),I(()=>{window.removeEventListener("resize",()=>e==null?void 0:e.resize()),e==null||e.dispose()}),(r,i)=>(y(),h("div",{ref_key:"chartContainer",ref:o,style:P({width:t.width,height:t.height})},null,4))}}),m={class:"chart-wrapper"},g={key:0,class:"loading-mask"},D=L(v({__name:"index",props:{title:{type:String,default:"\u5C42\u53E0\u67F1\u72B6\u56FE"},xAxisData:{type:Array,required:!0,validator:a=>Array.isArray(a)&&a.length>0},seriesData:{type:Array,required:!0,validator:a=>Array.isArray(a)&&a.every(t=>typeof t=="object"&&"name"in t&&"data"in t)},colors:{type:Array,default:()=>["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"]},loading:{type:Boolean,default:!1}},setup(a){k([C,E,B,O,R,q]);const t=a,o=c();let e=null;const n=c(!1),d=()=>{const r=t.seriesData??[],i=t.xAxisData??[];return{title:{text:t.title,left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{top:30,data:r.map(s=>s.name)},grid:{top:80,left:"3%",right:"3%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:i},yAxis:{type:"value",axisLabel:{formatter:s=>s>=1e3?s/1e3+"K":String(s)}},series:r.map(s=>({...s,type:"bar",stack:s.stack??"total",emphasis:{focus:"series"},label:{show:!0,position:"inside",formatter:({value:f})=>f>0?f:""}})),color:t.colors}},l=()=>{if(e&&n.value)try{e.resize()}catch{}};return w(()=>{(()=>{if(o.value)try{e==null||e.dispose(),e=b(o.value),e.setOption(d()),n.value=!0}catch{n.value=!1}})(),window.addEventListener("resize",l)}),T(()=>{window.removeEventListener("resize",l),e&&(e.dispose(),e=null)}),u(()=>{var r,i;return[t.title,t.colors,(r=t.seriesData)==null?void 0:r.length,(i=t.xAxisData)==null?void 0:i.length]},()=>{K(()=>{var r,i;(r=t.seriesData)!=null&&r.length&&((i=t.xAxisData)!=null&&i.length)?((()=>{if(e&&n.value)try{e.setOption(d(),{notMerge:!0,lazyUpdate:!1})}catch{}})(),l()):e==null||e.clear()})},{deep:!0}),u(()=>t.loading,r=>{r?e==null||e.showLoading():e==null||e.hideLoading()}),(r,i)=>(y(),h("div",m,[a.loading?(y(),h("div",g,"\u6570\u636E\u52A0\u8F7D\u4E2D...")):W("",!0),j("div",{ref_key:"chartRef",ref:o,class:"chart-container"},null,512)]))}}),[["__scopeId","data-v-3bfbcdd6"]])});export{D as S,x as _,M as __tla,_ as a,A as b,z as g};
