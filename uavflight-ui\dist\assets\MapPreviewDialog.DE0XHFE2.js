import{P as X,Q as v,R as j,Z as C,T as F,U as K,V as M,W as O,E,X as Q,Y as R,$ as Y,a0 as A,a1 as H,q as J,__tla as N}from"./index.BSP3cg_z.js";import{d as ee,k as x,w as P,S as ae,B as k,e as te,b as S,v as z,f as g,a as le,D as se,t as D,u as oe}from"./vue.CnN__PXn.js";let L,ie=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{let h,_;h={class:"map-preview-container"},_={key:0,class:"loading-overlay"},L=J(ee({name:"MapPreviewDialog",props:{visible:{type:Boolean,default:!1},layerId:{type:String,default:""}},emits:["update:visible"],setup($,{emit:T}){const b=$,U=T,d=x(!1),c=x(!1),f=x(null),l=x(null);P(()=>b.visible,e=>{d.value=e,e&&b.layerId&&setTimeout(()=>{Z(),q(b.layerId)},300)}),P(()=>d.value,e=>{U("update:visible",e),!e&&l.value&&w()});const Z=()=>{if(!l.value&&f.value)try{c.value=!0;const e=v([108.3,22.8]),t=new j({units:"metric",bar:!0,steps:4,minWidth:140}),a=new C,s=new F;l.value=new K({target:f.value,layers:[],view:new O({center:e,zoom:10}),controls:M({zoom:!0,rotate:!1,attribution:!1}).extend([t,a,s])}),c.value=!1}catch{E.error("\u521D\u59CB\u5316\u5730\u56FE\u5931\u8D25"),c.value=!1}},q=e=>{if(l.value&&e)try{c.value=!0;const t=e.split(":");if(t.length!==2)throw new Error(`\u56FE\u5C42ID\u683C\u5F0F\u4E0D\u6B63\u786E: ${e}`);const a=t[0],s=t[1],i="http://192.168.43.148:8085/geoserver",o="EPSG:4326",p="image/png",r="",u=Q(o);if(!u)throw new Error(`\u65E0\u6CD5\u83B7\u53D6\u6295\u5F71\u7CFB\u7EDF: ${o}`);const n=new R({url:`${i}/gwc/service/wmts`,layer:e,matrixSet:o,format:p,projection:u,style:r,requestEncoding:"KVP",tileGrid:G(u,o),wrapX:!0,transition:0,crossOrigin:"anonymous"});n.on("tileloaderror",ne=>{const V=n.getUrls();V&&V.length}),n.on("tileloadend",()=>{});const y=new Y({source:n,visible:!0,opacity:1,zIndex:1});l.value.addLayer(y),B(a,s)}catch{E.error("\u52A0\u8F7D\u56FE\u5C42\u5931\u8D25"),c.value=!1}},B=async(e,t)=>{try{const a=await A(e,t);if(a&&a.status==="success"&&a.bbox&&a.bbox.latLon){const{minx:s,miny:i,maxx:o,maxy:p}=a.bbox.latLon;if(s===-180&&i===-90&&o===180&&p===90){if(a.bbox.native&&(a.bbox.native.minx!==-180||a.bbox.native.miny!==-90||a.bbox.native.maxx!==180||a.bbox.native.maxy!==90)){const r=a.bbox.native,u=v([r.minx,r.miny]),n=v([r.maxx,r.maxy]),y=[u[0],u[1],n[0],n[1]];return void I(y)}m()}else{const r=v([s,i]),u=v([o,p]),n=[r[0],r[1],u[0],u[1]];n.some(y=>!isFinite(y))?m():I(n)}}else m()}catch{m()}finally{c.value=!1}},I=e=>{if(l.value)try{l.value.getView().fit(e,{padding:[50,50,50,50],maxZoom:18})}catch{m()}},m=()=>{if(l.value)try{const e=v([108.2,22.7]).concat(v([108.5,23]));l.value.getView().fit(e,{padding:[50,50,50,50],maxZoom:15})}catch{}},G=(e,t)=>{let a,s,i;e.getExtent(),a=[-180,90],s=[.703125,.3515625,.17578125,.087890625,.0439453125,.02197265625,.010986328125,.0054931640625,.00274658203125,.001373291015625,.0006866455078125,.0003433227539062,.0001716613769531,858306884766e-16,429153442383e-16,214576721191e-16,107288360596e-16,53644180298e-16,26822090149e-16,13411045074e-16,6705522537e-16,3352761269e-16,16763806345e-17,8381903173e-17,4190951586e-17,2095475793e-17],i=[];for(let o=0;o<s.length;o++)i.push(`${t}:${o}`);return new H({origin:a,resolutions:s,matrixIds:i})},w=()=>{l.value&&(l.value.setTarget(void 0),l.value=null)},W=()=>{w(),d.value=!1};return ae(()=>{w()}),(e,t)=>{const a=k("el-icon"),s=k("el-dialog");return S(),te(s,{modelValue:d.value,"onUpdate:modelValue":t[0]||(t[0]=i=>d.value=i),title:`\u56FE\u5C42\u9884\u89C8: ${$.layerId||"\u672A\u77E5\u56FE\u5C42"}`,width:"80%","destroy-on-close":"","before-close":W},{default:z(()=>[g("div",h,[c.value?(S(),le("div",_,[D(a,{class:"loading-icon"},{default:z(()=>[D(oe(X))]),_:1}),t[1]||(t[1]=g("p",null,"\u6B63\u5728\u52A0\u8F7D\u56FE\u5C42...",-1))])):se("",!0),g("div",{id:"preview-map",ref_key:"mapContainer",ref:f,class:"map-container"},null,512)])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-06dca067"]])});export{ie as __tla,L as default};
