# 是否是微服务架构（重要）
VITE_IS_MICRO= false

# 前端访问前缀
VITE_PUBLIC_PATH = /

# 后端请求前缀
VITE_API_URL = /api

# OAUTH2 密码模式客户端信息
VITE_OAUTH2_PASSWORD_CLIENT='pig:pig'

# OAUTH2 短信客户端信息
VITE_OAUTH2_MOBILE_CLIENT='app:app'

# 是否开启前端验证码
VITE_VERIFY_ENABLE = true

# 前端加密密钥
VITE_PWD_ENC_KEY='ksoeidjcksjzqisd'

# 是否开启websocket 消息接受,
VITE_WEBSOCKET_ENABLE = false

# 是否开启注册
VITE_REGISTER_ENABLE  = false

# 默认地区编码 
VITE_DEFAULT_REGION = '451421,451421102'

# Geoserver 配置
VITE_GEOSERVER_HOST = localhost
VITE_GEOSERVER_PORT = 8085
VITE_GEOSERVER_PATH = /geoserver

# Geoserver 配置
VITE_GEOSERVER_HOST = localhost
VITE_GEOSERVER_PORT = 8085
VITE_GEOSERVER_PATH = /geoserver