###
 # @Author: 吴博文 <EMAIL>
 # @Date: 2025-04-30 17:04:35
 # @LastEditors: 吴博文 <EMAIL>
 # @LastEditTime: 2025-07-29 10:22:20
 # @FilePath: \uavflight-ui\.env.production
 # @Description: 
 # 
 # Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
### 
# port 端口号
VITE_PORT = 8888

#浏览器自动打开
VITE_OPEN = true

# 生产环境
ENV = 'production'



VITE_ADMIN_PROXY_PATH = http://************:9999
# 代码生成服务地址 (单体架构有效)
VITE_GEN_PROXY_PATH = http://************:5003
# 地图服务地址
VITE_MAP_SERVER_IP = **************
# 地图服务端口
VITE_MAP_SERVER_PORT_NGINX = 81
VITE_MAP_SERVER_PORT_GEOSERVER = 8085
# Geoserver 配置
VITE_GEOSERVER_HOST = **************
VITE_GEOSERVER_PORT = 8085
VITE_GEOSERVER_HD_PORT = 8091
VITE_GEOSERVER_PATH = /geoserver

#打包
# VITE_ADMIN_PROXY_PATH = http://127.0.0.1:9999
# VITE_GEN_PROXY_PATH = http://127.0.0.1:5003
# VITE_MAP_SERVER_IP = *************
# VITE_MAP_SERVER_PORT_NGINX = 81
# VITE_MAP_SERVER_PORT_GEOSERVER = 8085 
# VITE_GEOSERVER_HOST = *************
# VITE_GEOSERVER_PORT = 8085
# VITE_GEOSERVER_HD_PORT = 8091
# VITE_GEOSERVER_PATH = /geoserver 
