{"layers": [{"id": "basemap_xyz", "name": "南宁地图", "type": "raster", "initialLoad": true, "protocol": "WMTS", "url": "http://127.0.0.1:8085/geoserver/gwc/demo/drone:geotiff_coverage?gridSet=EPSG:4326&format=image/png"}, {"id": "tasks_polygons", "name": "任务区域", "type": "vector", "geometryType": "MultiPolygon", "protocol": "GeoJSON", "url": "http://127.0.0.1:8085/geoserver/drone/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=drone%3Aarea2&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": true, "event": "TT01", "defaultStyle": {"polygon": {"material": "#88888888", "outline": true, "outlineColor": "#888888", "outlineWidth": 1}}, "styleRules": [{"filter": {"property": "road_type", "operator": "=", "value": "highway"}, "style": {"polygon": {"material": "#FF000088", "outline": true, "outlineColor": "#FF0000", "outlineWidth": 3}}}, {"filter": {"property": "road_type", "operator": "=", "value": "secondary"}, "style": {"polygon": {"material": "#FFA50088", "outline": true, "outlineColor": "#FFA500", "outlineWidth": 2}}}, {"filter": {"property": "surface", "operator": "=", "value": "gravel"}, "style": {"polygon": {"material": "#66666688", "outline": true, "outlineColor": "#666666", "outlineWidth": 2}, "polyline": {"material": {"color": "#666666", "dashLength": 4, "dashPattern": 4095}, "width": 2}}}]}, {"id": "land_parcels_label", "name": "地块标注", "type": "vector", "geometryType": "MultiPoint", "protocol": "GeoJSON", "url": "https://.../labels.geojson", "initialLoad": true, "defaultStyle": {"billboard": {"image": "https://.../labels/label-default.png", "width": 48, "height": 48, "verticalOrigin": "BOTTOM", "horizontalOrigin": "CENTER"}, "point": {"show": false}}, "styleRules": [{"filter": {"property": "landType", "operator": "=", "value": "agriculture"}, "style": {"billboard": {"image": "https://.../labels/agriculture.png", "width": 48, "height": 48, "verticalOrigin": "BOTTOM", "horizontalOrigin": "CENTER"}, "label": {"text": "农田", "font": "14pt sans-serif", "style": "FILL_AND_OUTLINE", "fillColor": "#FFFFFF", "outlineColor": "#000000", "outlineWidth": 2, "pixelOffset": [0, -60]}}}, {"filter": {"property": "landType", "operator": "=", "value": "construction"}, "style": {"billboard": {"image": "https://.../labels/construction.png", "width": 48, "height": 48, "verticalOrigin": "BOTTOM", "horizontalOrigin": "CENTER"}, "label": {"text": "建设用地", "font": "14pt sans-serif", "style": "FILL_AND_OUTLINE", "fillColor": "#FFFFFF", "outlineColor": "#000000", "outlineWidth": 2, "pixelOffset": [0, -60]}}}]}]}