/**
 * @file mapLayerManager.ts
 * @description 地图图层管理器Store，负责管理OpenLayers地图的图层加载、显示和样式
 * 
 * 该文件提供了地图图层管理的核心功能，包括：
 * - 加载和解析地图配置文件
 * - 创建和管理不同类型的图层（WMS、WMTS、XYZ、GeoJSON、MVT等）
 * - 控制图层的显示/隐藏、透明度和样式
 * - 处理图层事件和交互
 * - 提供图层排序和重新排序功能
 * 
 * 该Store使用Pinia状态管理，与OpenLayers地图实例紧密集成，
 * 为应用提供统一的图层管理接口。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Description: 地图图层管理 Store (OpenLayers版本)
 */
import { defineStore } from 'pinia';
import type Map from 'ol/Map';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import XYZ from 'ol/source/XYZ';
import TileWMS from 'ol/source/TileWMS';
import VectorSource from 'ol/source/Vector';
import GeoJSON from 'ol/format/GeoJSON';
import MVT from 'ol/format/MVT';
import VectorTileLayer from 'ol/layer/VectorTile';
import VectorTileSource from 'ol/source/VectorTile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { getTopLeft } from 'ol/extent';
import { get as getProjection } from 'ol/proj';
import { Style, Fill, Stroke, Icon, Circle, Text } from 'ol/style';
import Feature from 'ol/Feature';
import { fromLonLat } from 'ol/proj';
import type OLMap from '/@/utils/map/olMap';
import { convertToWfsBboxUrl } from './styleUtils';
import { bbox as bboxStrategy } from 'ol/loadingstrategy';
import { getGeoserverWfsUrl, getGeoserverWmsUrl } from '/@/utils/geoserver';

// 定义参数类型
interface LayerParameters {
	service?: string;
	format?: string;
	transparent?: boolean;
	[key: string]: any;
}

// 定义图层样式类型
interface LayerStyle {
	color?: string;
	weight?: number;
	opacity?: number;
	fillColor?: string;
	fillOpacity?: number;
	image?: {
		src: string;
		scale?: number;
		anchor?: [number, number];
		rotation?: number;
	};
	label?: {
		text: string;
		font?: string;
		fill?: {
			color: string;
		};
		stroke?: {
			color: string;
			width: number;
		};
	};
}

// 定义样式规则
interface StyleRule {
	filter: {
		property: string;
		operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in';
		value: any;
	};
	style: LayerStyle | string;
}

// 定义地图图层接口
export interface MapLayer {
	id: string;
	name: string;
	type: 'raster' | 'vector';
	protocol?: 'XYZ' | 'WMS' | 'WMTS' | 'GeoJSON' | 'MVT' | 'WFS';
	url: string;
	initialLoad: boolean;
	// 矢量图层的几何类型
	geometryType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
	// WMS 特定属性
	layers?: string;
	parameters?: LayerParameters;
	// 其他属性
	defaultStyle?: LayerStyle | string;
	styleRules?: StyleRule[];
	active?: boolean;
	layerInstance?: any;
	// 事件标识
	event?: string;
	showLabels?: boolean;
	labelField?: string;
	labelStyle?: {
		color: string;
		fontSize: number;
	};
}

// 定义图层管理器状态
interface MapLayerManagerState {
	mapConfig: {
		layers: MapLayer[];
		styles?: Record<string, LayerStyle>;
	} | null;
	activeMap: OLMap | null;
	olMap: Map | null;  // 添加一个存储OpenLayers Map实例的属性
	loadedLayers: string[];
	styleLibrary: Record<string, LayerStyle>;
}

/**
 * 图层管理器Store
 */
export const useMapLayerManagerStore = defineStore({
	id: 'mapLayerManager',
	state: (): MapLayerManagerState => ({
		mapConfig: null,
		activeMap: null,
		olMap: null,
		loadedLayers: [],
		styleLibrary: {},
	}),
	getters: {
		/**
		 * 获取已加载图层数量
		 */
		loadedLayerCount: (state) => state.loadedLayers.length,
		
		/**
		 * 获取图层配置
		 */
		getLayerById: (state) => (layerId: string) => {
			return state.mapConfig?.layers.find(layer => layer.id === layerId);
		},
		
		/**
		 * 判断图层是否已加载
		 */
		isLayerLoaded: (state) => (layerId: string) => {
			return state.loadedLayers.includes(layerId);
		},
	},
	actions: {
		/**
		 * 加载地图配置
		 * 从服务器获取地图图层配置信息
		 * 
		 * 该方法执行以下操作：
		 * 1. 尝试从多个可能的URL路径加载配置文件
		 * 2. 验证加载的配置数据格式是否正确
		 * 3. 处理WMTS图层的URL和参数
		 * 4. 加载样式库
		 * 
		 * 配置文件应包含图层数组和可选的样式定义，格式如下：
		 * ```
		 * {
		 *   "layers": [
		 *     {
		 *       "id": "layer1",
		 *       "name": "底图",
		 *       "type": "raster",
		 *       "protocol": "XYZ",
		 *       "url": "https://example.com/tiles/{z}/{x}/{y}.png",
		 *       "initialLoad": true
		 *     },
		 *     ...
		 *   ],
		 *   "styles": {
		 *     "defaultStyle": { ... }
		 *   }
		 * }
		 * ```
		 * 
		 * @returns 是否加载成功的Promise
		 */
		async loadConfig(): Promise<boolean> {
			try {
				// 从环境变量获取地图服务IP地址和URL
				const mapServerIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
				const mapServerUrl = `http://${mapServerIp}:${import.meta.env.VITE_MAP_SERVER_PORT_NGINX}`;
				
				// 构建配置文件URL
				const mapConfigUrl = `${mapServerUrl}/baseMap2.json`;
				const styleConfigUrl = `${mapServerUrl}/baseStyle2.json`;
				console.log('正在加载地图配置，URL:', mapConfigUrl, styleConfigUrl);
				
				// 获取配置
				const [mapConfigResponse, styleConfigResponse] = await Promise.all([
					fetch(mapConfigUrl),
					fetch(styleConfigUrl)
				]);

				if (!mapConfigResponse.ok || !styleConfigResponse.ok) {
					throw new Error('配置文件加载失败');
				}

				const mapConfig = await mapConfigResponse.json();
				const styleConfig = await styleConfigResponse.json();

				// 保存配置
				this.mapConfig = mapConfig;
				this.styleLibrary = styleConfig.styles;

				console.log('地图配置已加载:', this.mapConfig);
				console.log('样式库已加载:', this.styleLibrary);

				// 处理图层协议和URL替换
				if (this.mapConfig && this.mapConfig.layers) {
					this.mapConfig.layers.forEach(layer => {
						// 替换Geoserver URL
						this.replaceGeoserverUrl(layer);
						// 处理WMTS图层的URL
						if (layer.protocol === 'WMTS' && layer.url) {
							console.log(`图层配置 ${layer.id}: 类型=${layer.protocol}, URL=${layer.url}`);
							// 尝试从URL提取图层名称
							if (!layer.layers) {
								const url = new URL(layer.url);
								const demoPathMatch = url.pathname.match(/demo\/([^?]+)/);
								if (demoPathMatch && demoPathMatch[1]) {
									layer.layers = demoPathMatch[1];
									console.log(`为图层 ${layer.id} 提取的图层名称: ${layer.layers}`);
								}
							}
						}
						// 自动检测和处理WFS协议
						else if (layer.type === 'vector') {
							this.detectAndSetWfsProtocol(layer);
						}
					});
				}
				
				return true;
			} catch (error) {
				console.error('加载地图配置失败:', error);
				return false;
			}
		},
		
		/**
		 * 替换图层URL中的Geoserver地址为环境配置的地址
		 * @param layer 图层配置
		 */
		replaceGeoserverUrl(layer: MapLayer): void {
			if (!layer.url) return;
			
			try {
				const url = new URL(layer.url);
				const currentHost = url.hostname;
				const currentPort = url.port;
				
				// 检查是否是本地Geoserver地址
				if (currentHost === '127.0.0.1' || currentHost === 'localhost') {
					// 获取环境配置的Geoserver地址
					const envHost = import.meta.env.VITE_GEOSERVER_HOST || '127.0.0.1';
					const envPort = import.meta.env.VITE_GEOSERVER_PORT || '8085';
					
					// 判断是否为本地环境
					const isLocal = window.location.hostname === 'localhost' || 
									window.location.hostname === '127.0.0.1' ||
									envHost === '127.0.0.1' || 
									envHost === 'localhost';
					
					if (isLocal) {
						// 本地环境使用完整URL
						url.hostname = envHost;
						url.port = envPort;
						layer.url = url.toString();
						console.log(`替换图层 ${layer.id} 的URL为本地环境: ${layer.url}`);
					} else {
						// 生产环境使用相对路径
						const path = import.meta.env.VITE_GEOSERVER_PATH || '/geoserver/wfs';
						layer.url = `${path}${url.pathname}${url.search}`;
						console.log(`替换图层 ${layer.id} 的URL为生产环境: ${layer.url}`);
					}
				}
			} catch (error) {
				console.warn(`替换图层 ${layer.id} URL失败:`, error);
			}
		},
		
		/**
		 * 检测并设置WFS协议
		 * 分析URL判断是否是WFS服务，如果是则将协议设置为WFS
		 * 
		 * @param layer 图层配置
		 */
		detectAndSetWfsProtocol(layer: MapLayer): void {
			// 仅处理矢量图层且未明确指定为GeoJSON或MVT协议的情况
			if (layer.type !== 'vector' || 
				(layer.protocol && ['GeoJSON', 'MVT'].includes(layer.protocol as string))) {
				return;
			}
			
			try {
				// 尝试解析URL
				const url = new URL(layer.url);
				const params = url.searchParams;
				
				// 检查是否包含WFS服务的典型参数
				const service = params.get('service');
				const request = params.get('request');
				const typeName = params.get('typeName');
				const outputFormat = params.get('outputFormat');
				
				// 判断是否是WFS请求
				const isWfs = service?.toUpperCase() === 'WFS' || 
					request?.toUpperCase() === 'GETFEATURE' ||
					(typeName && outputFormat && outputFormat.includes('json'));
				
				if (isWfs) {
					console.log(`检测到WFS服务: ${layer.id}, 将协议设置为WFS`);
					layer.protocol = 'WFS';
				}
			} catch (error) {
				console.warn(`解析图层 ${layer.id} URL失败，将使用默认协议:`, error);
			}
		},
		
		/**
		 * 初始化地图
		 * 将OLMap实例与图层管理器关联，并加载初始图层
		 * 
		 * 该方法是图层管理器的核心初始化方法，它执行以下操作：
		 * 1. 验证传入的OLMap实例是否有效
		 * 2. 存储OLMap实例和其内部的OpenLayers Map对象
		 * 3. 检查Map对象的关键方法是否可用
		 * 4. 如果地图配置已加载，则调用loadInitialLayers加载初始图层
		 * 
		 * @param map OLMap实例，包含OpenLayers的Map对象
		 */
		initializeMap(map: OLMap) {
			if (!map) {
				console.error('初始化地图失败：传入的OLMap实例为空');
				return;
			}
			
			if (!map.map) {
				console.error('初始化地图失败：OLMap实例中的map对象为空');
				return;
			}
			
			console.log('初始化地图管理器...');
			
			this.activeMap = map;
			this.olMap = map.map; // 存储OpenLayers的Map实例
			
			console.log('OpenLayers Map对象状态检查:', {
				map实例: !!this.olMap,
				map类型: typeof this.olMap,
				addLayer方法: typeof this.olMap.addLayer,
				getView方法: typeof this.olMap.getView
			});
			
			if (this.mapConfig) {
				this.loadInitialLayers();
			} else {
				console.warn('地图配置尚未加载，暂不加载初始图层');
			}
		},
		
		/**
		 * 加载配置并初始化地图
		 * @param map OLMap实例
		 * @returns 是否成功
		 */
		async loadMapConfigAndInitialize(map: OLMap): Promise<boolean> {
			const success = await this.loadConfig();
			if (success) {
				this.initializeMap(map);
				// 输出图层信息帮助调试
				this.printMapLayersInfo();
				return true;
			}
			return false;
		},
		
		/**
		 * 打印地图图层信息，帮助调试
		 */
		printMapLayersInfo() {
			if (!this.mapConfig || !this.mapConfig.layers) {
				console.warn('没有加载地图配置或配置中没有图层');
				return;
			}
			
			console.log('%c当前地图配置信息', 'color: blue; font-weight: bold');
			console.log(`总共 ${this.mapConfig.layers.length} 个图层配置：`);
			
			this.mapConfig.layers.forEach((layer, index) => {
				console.log(`%c图层 ${index + 1}: ${layer.name} (${layer.id})`, 'color: green; font-weight: bold');
				console.log(`- 类型: ${layer.type}`);
				console.log(`- 协议: ${layer.protocol}`);
				console.log(`- URL: ${layer.url}`);
				console.log(`- 初始加载: ${layer.initialLoad}`);
				if (layer.layers) {
					console.log(`- 图层名称: ${layer.layers}`);
				}
			});
			
			console.log(`%c已加载的图层: ${this.loadedLayers.join(', ')}`, 'color: blue');
		},
		
		/**
		 * 加载初始图层
		 * 根据配置文件中的initialLoad属性，加载标记为初始加载的图层
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查地图配置和OpenLayers Map实例是否已初始化
		 * 2. 筛选出配置中标记为initialLoad=true的图层
		 * 3. 使用Promise.all并行加载所有初始图层
		 * 4. 统计加载成功的图层数量并输出日志
		 * 
		 * 初始图层通常是基础底图或必须在应用启动时显示的图层
		 */
		loadInitialLayers() {
			if (!this.mapConfig) {
				console.warn('图层配置未加载，无法加载初始图层');
				return;
			}
			
			if (!this.olMap) {
				console.error('OpenLayers地图未初始化，无法加载初始图层');
				return;
			}
			
			console.log('加载初始图层...');
			console.log(`OpenLayers Map实例状态:`, {
				已初始化: !!this.olMap,
				类型: this.olMap ? typeof this.olMap : 'null',
				有无addLayer方法: this.olMap && typeof this.olMap.addLayer === 'function'
			});
			
			// 使用Promise.all等待所有图层加载完成
			const promises = this.mapConfig.layers
				.filter(layer => layer.initialLoad)
				.map(layer => this.addLayer(layer.id));
			
			Promise.all(promises)
				.then(results => {
					const successCount = results.filter(Boolean).length;
					console.log(`初始图层加载完成，共${results.length}个图层，成功加载${successCount}个`);
				})
				.catch(error => {
					console.error('加载初始图层过程中发生错误:', error);
				});
		},
		
		/**
		 * 创建WMS图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		createWmsLayer(layerConfig: MapLayer): any {
			const url = new URL(layerConfig.url);
			const baseUrl = `${url.protocol}//${url.host}${url.pathname}`;
			const params: Record<string, any> = {};
			
			// 获取URL查询参数
			for (const [key, value] of url.searchParams.entries()) {
				params[key] = value;
			}
			
			// 确保必要参数存在
			if (!params.layers && layerConfig.layers) {
				params.layers = layerConfig.layers;
			}
			
			// 创建WMS源
			const source = new TileWMS({
				url: baseUrl,
				params: {
					...params,
					LAYERS: params.layers || params.typeName || '',
					FORMAT: params.format || 'image/png',
					TRANSPARENT: true,
					VERSION: params.version || '1.1.1',
					SRS: params.srs || 'EPSG:3857'
				},
				serverType: 'geoserver'
			});
			
			return new TileLayer({
				source,
				visible: true,
				zIndex: 1
			});
		},
		
		/**
		 * 创建WMTS图层
		 * 解析WMTS配置并创建OpenLayers WMTS图层
		 * 
		 * 该方法执行以下操作：
		 * 1. 解析WMTS服务URL，提取基础URL、图层名称和其他参数
		 * 2. 确定投影系统和网格集（gridSet）
		 * 3. 构建标准的GeoServer WMTS端点URL
		 * 4. 创建适合该投影的瓦片网格（tileGrid）
		 * 5. 配置WMTS数据源，使用KVP请求编码以确保兼容性
		 * 6. 添加图层加载事件处理以便调试
		 * 7. 创建并返回TileLayer实例
		 * 
		 * @param layerConfig 图层配置对象
		 * @returns 创建的OpenLayers TileLayer实例或null（如果创建失败）
		 */
		createWmtsLayer(layerConfig: MapLayer): any {
			console.log(`创建WMTS图层: ${layerConfig.name}, URL: ${layerConfig.url}`);
			
			try {
				// 解析URL
				const url = new URL(layerConfig.url);
				const baseUrl = `${url.protocol}//${url.host}`;
				
				// 从URL中提取参数
				const urlParams = new URLSearchParams(url.search);
				
				// 优先使用配置中的layers属性，如果没有则从URL中提取
				let layer = layerConfig.layers || '';
				
				if (!layer) {
					// 从URL路径中提取图层名称
					// 处理形如 /geoserver/gwc/demo/workspace:layername 的路径
					const demoPathMatch = url.pathname.match(/demo\/([^?]+)/);
					if (demoPathMatch && demoPathMatch[1]) {
						layer = demoPathMatch[1];
						console.log(`从URL路径提取的图层名称: ${layer}`);
					}
				}
				
				// 获取其他参数
				const format = urlParams.get('format') || 'image/png';
				// 从URL中获取gridSet，默认为EPSG:4326
				const gridSet = urlParams.get('gridSet') || 'EPSG:4326';
				
				// 打印解析结果
				console.log(`WMTS解析结果: 图层=${layer}, 格式=${format}, 网格集=${gridSet}`);
				
				// 获取投影
				const projection = getProjection(gridSet);
				if (!projection) {
					throw new Error(`不支持的投影系统: ${gridSet}`);
				}
				
				// 创建GeoServer WMTS服务的标准端点URL
				const geoserverPath = url.pathname.includes('/geoserver') ? '/geoserver' : '';
				const wmtsEndpoint = `${baseUrl}${geoserverPath}/gwc/service/wmts`;
				
				// 通过GetCapabilities请求获取完整的图层信息
				const getCapabilitiesUrl = `${wmtsEndpoint}?SERVICE=WMTS&REQUEST=GetCapabilities&VERSION=1.0.0`;
				console.log(`获取WMTS能力文档: ${getCapabilitiesUrl}`);
				
				// 创建WMTS源，使用GeoServer需要的参数格式
				const source = new WMTS({
					// 不使用URL模板，而是使用KVP requestEncoding
					url: wmtsEndpoint,
					layer: layer,
					matrixSet: gridSet,
					format: format,
					projection: projection,
					style: '',
					// 关键参数：使用KVP编码而非REST，这样能正确处理参数
					requestEncoding: 'KVP',
					// 自定义TileGrid，与EPSG:4326兼容
					tileGrid: this.createWmtsTileGrid(projection, gridSet),
					// 其他设置
					wrapX: true,
					transition: 0
				});
				
				// 添加事件处理
				source.on('tileloaderror', (event: any) => {
					const tile = event.tile;
					const url = tile.src_ || '';
					console.warn('WMTS图层加载失败:', url);
				});
				
				source.on('tileloadend', () => {
					console.log('WMTS图层部分加载成功');
				});
				
				// 返回图层
				return new TileLayer({
					source: source,
					visible: true,
					zIndex: 0
				});
			} catch (error) {
				console.error(`创建WMTS图层失败:`, error);
				return null;
			}
		},
		
		/**
		 * 创建WMTS图层的瓦片网格
		 * @param projection 投影
		 * @param gridSetId 网格集ID
		 * @returns 瓦片网格
		 */
		createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
			const projectionExtent = projection.getExtent();
			
			// 根据不同的坐标系创建合适的参数
			let origin, resolutions, matrixIds;
			
			if (gridSetId === 'EPSG:4326') {
				// EPSG:4326 特殊处理
				origin = [-180, 90]; // 正确的原点
				
				// 标准的EPSG:4326分辨率
				resolutions = [
					0.703125,              // 层级 0
					0.3515625,             // 层级 1
					0.17578125,            // 层级 2
					0.087890625,           // 层级 3
					0.0439453125,          // 层级 4
					0.02197265625,         // 层级 5
					0.010986328125,        // 层级 6
					0.0054931640625,       // 层级 7
					0.00274658203125,      // 层级 8
					0.001373291015625,     // 层级 9
					0.0006866455078125,    // 层级 10
					0.0003433227539062,    // 层级 11
					0.0001716613769531,    // 层级 12
					0.0000858306884766,    // 层级 13
					0.0000429153442383,    // 层级 14
					0.0000214576721191,    // 层级 15
					0.0000107288360596,    // 层级 16
					0.0000053644180298,    // 层级 17
					0.0000026822090149,    // 层级 18
					0.0000013411045074,    // 层级 19
					0.0000006705522537,    // 层级 20
					0.0000003352761269,    // 层级 21
					0.00000016763806345,   // 层级 22
					0.00000008381903173,   // 层级 23
					0.00000004190951586,   // 层级 24
					0.00000002095475793    // 层级 25
				];
				
				
				// 标准的EPSG:4326 GeoServer矩阵ID
				matrixIds = [];
				for (let i = 0; i < resolutions.length; i++) {
					matrixIds.push(`${gridSetId}:${i}`);
				}
			} else {
				// 默认情况下，使用自动计算的值
				origin = getTopLeft(projectionExtent);
				const size = Math.max(
					projectionExtent[2] - projectionExtent[0],
					projectionExtent[3] - projectionExtent[1]
				);
				const maxResolution = size / 256;
				
				resolutions = [];
				matrixIds = [];
				for (let i = 0; i < 20; i++) {
					resolutions.push(maxResolution / Math.pow(2, i));
					// 使用标准GeoServer格式矩阵ID
					matrixIds.push(`${gridSetId}:${i}`);
				}
			}
			
			return new WMTSTileGrid({
				origin: origin,
				resolutions: resolutions,
				matrixIds: matrixIds
			});
		},
		
		/**
		 * 创建XYZ图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		createXyzLayer(layerConfig: MapLayer): any {
			return new TileLayer({
				source: new XYZ({ url: layerConfig.url }),
				visible: true,
				zIndex: 0
			});
		},
		
		/**
		 * 创建矢量图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		async createVectorLayer(layerConfig: MapLayer): Promise<any> {
			try {
				console.log('创建矢量图层:', layerConfig);
				
				// 根据协议不同创建不同类型的矢量图层
				if (layerConfig.protocol === 'WFS') {
					return this.createWfsVectorLayer(layerConfig);
				}
				
				// 创建标准GeoJSON矢量数据源
				const vectorSource = new VectorSource({
					format: new GeoJSON(),
					url: layerConfig.url
				});
				
				// 创建矢量图层
				const vectorLayer = new VectorLayer({
					source: vectorSource,
					properties: {
						id: layerConfig.id,
						name: layerConfig.name,
						type: 'vector', // 明确设置类型
						geometryType: layerConfig.geometryType,
						originalLayer: layerConfig
					}
				});
				
				// 设置图层样式
				const style = await this.createFeatureStyle(layerConfig);
				if (style) {
					vectorLayer.setStyle(style);
				}
				
				console.log('矢量图层创建完成:', vectorLayer.getProperties());
				return vectorLayer;
			} catch (error) {
				console.error('创建矢量图层失败:', error);
				throw error;
			}
		},
		
		/**
		 * 创建WFS矢量图层
		 * 使用bbox策略按需加载图层要素，提高性能
		 * 
		 * @param layerConfig 图层配置
		 * @returns 创建的WFS图层
		 */
		async createWfsVectorLayer(layerConfig: MapLayer): Promise<any> {
			try {
				console.log('创建WFS矢量图层:', layerConfig);
				
				// 将静态URL转换为函数式URL，支持bbox策略
				const urlFunction = convertToWfsBboxUrl(layerConfig.url);
				
				// 创建使用bbox策略的数据源
				const vectorSource = new VectorSource({
					format: new GeoJSON(),
					url: urlFunction,
					strategy: bboxStrategy
				});
				
				// 创建矢量图层
				const vectorLayer = new VectorLayer({
					source: vectorSource,
					properties: {
						id: layerConfig.id,
						name: layerConfig.name,
						type: 'vector',
						protocol: 'WFS', // 明确标记为WFS协议
						geometryType: layerConfig.geometryType,
						originalLayer: layerConfig
					}
				});
				
				// 设置图层样式
				const style = await this.createFeatureStyle(layerConfig);
				if (style) {
					vectorLayer.setStyle(style);
				}
				
				console.log('WFS矢量图层创建完成:', vectorLayer.getProperties());
				return vectorLayer;
			} catch (error) {
				console.error('创建WFS矢量图层失败:', error);
				throw error;
			}
		},
		
		/**
		 * 创建MVT图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		createMvtLayer(layerConfig: MapLayer): any {
			const source = new VectorTileSource({
				format: new MVT(),
				url: layerConfig.url
			});
			
			return new VectorTileLayer({
				source,
				style: this.createFeatureStyle(layerConfig),
				visible: true,
				zIndex: 3
			});
		},
		
		/**
		 * 检测特征的几何类型
		 * @param feature 要素
		 * @returns 几何类型
		 */
		detectGeometryType(feature: Feature): string {
			const geometry = feature.getGeometry();
			if (!geometry) return 'Unknown';
			
			// 获取几何类型
			const geometryType = geometry.getType();
			return geometryType;
		},
		
		/**
		 * 创建要素样式
		 * @param layerConfig 图层配置
		 * @returns 样式函数
		 */
		createFeatureStyle(layerConfig: MapLayer): any {
			// 获取默认样式
			let defaultStyle: LayerStyle = {
				color: '#3388ff',
				weight: 2,
				opacity: 1,
				fillColor: '#3388ff',
				fillOpacity: 0.2
			};
			
			// 处理默认样式
			if (layerConfig.defaultStyle) {
				if (typeof layerConfig.defaultStyle === 'string' && this.styleLibrary[layerConfig.defaultStyle]) {
					defaultStyle = this.styleLibrary[layerConfig.defaultStyle];
				} else if (typeof layerConfig.defaultStyle !== 'string') {
					defaultStyle = layerConfig.defaultStyle;
				}
			}
			
			// 获取图层的几何类型
			const geometryType = layerConfig.geometryType;
			console.log(`图层 ${layerConfig.id} 的几何类型是: ${geometryType || '未指定'}`);
			
			// 创建样式函数
			return (feature: Feature, resolution: number) => {
				let styleToUse = defaultStyle;
				
				// 首先使用配置中的几何类型，如果没有则自动检测
				const detectedGeometryType = this.detectGeometryType(feature);
				const effectiveGeometryType = geometryType || detectedGeometryType;
				console.log(`特征的实际几何类型: ${detectedGeometryType}, 使用类型: ${effectiveGeometryType}`);
				
				// 如果存在样式规则，优先应用样式规则
				if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
					// 寻找匹配的样式规则
					for (const rule of layerConfig.styleRules) {
						if (this.evaluateFilter(feature, rule.filter)) {
							if (typeof rule.style === 'string' && this.styleLibrary[rule.style]) {
								styleToUse = this.styleLibrary[rule.style];
								console.log(`应用样式库规则样式: ${rule.style}`, styleToUse);
							} else if (typeof rule.style !== 'string') {
								styleToUse = rule.style;
								console.log(`应用内联规则样式`, styleToUse);
							}
							break; // 找到第一个匹配的规则后停止
						}
					}
				}
				
				// 处理填充颜色 - 可能是带透明度的十六进制色值
				let fillColor = styleToUse.fillColor || 'rgba(51, 136, 255, 0.2)';
				let fillOpacity = styleToUse.fillOpacity;
				
				if (fillColor.startsWith('#') && fillColor.length === 9) {
					// 处理带透明度的十六进制色值 #RRGGBBAA
					fillOpacity = parseInt(fillColor.slice(7, 9), 16) / 255;
					fillColor = fillColor.slice(0, 7);
				}
				
				// 处理线条颜色
				let strokeColor = styleToUse.color || '#3388ff';
				let strokeOpacity = styleToUse.opacity || 1;
				
				if (strokeColor.startsWith('#') && strokeColor.length === 9) {
					// 处理带透明度的十六进制色值 #RRGGBBAA
					strokeOpacity = parseInt(strokeColor.slice(7, 9), 16) / 255;
					strokeColor = strokeColor.slice(0, 7);
				}
				
				// 准备描边样式
				const stroke = new Stroke({
					color: strokeOpacity < 1 ? 
						`rgba(${parseInt(strokeColor.slice(1, 3), 16)}, ${parseInt(strokeColor.slice(3, 5), 16)}, ${parseInt(strokeColor.slice(5, 7), 16)}, ${strokeOpacity})` : 
						strokeColor,
					width: styleToUse.weight || 2,
					lineCap: 'round',
					lineJoin: 'round'
				});

				// 准备填充样式
				const fill = new Fill({
					color: fillOpacity !== undefined ? 
						`rgba(${parseInt(fillColor.slice(1, 3), 16)}, ${parseInt(fillColor.slice(3, 5), 16)}, ${parseInt(fillColor.slice(5, 7), 16)}, ${fillOpacity})` : 
						fillColor
				});
				
				// 处理标签文本
				let labelText = '';
				let labelStyle = null;
				
				// 定义标签显示的最大分辨率，超过此分辨率标签将不显示
				// 数值越小，需要放大的越多才能看到标签
				const maxLabelResolution = 100;
				console.log("maxLabelResolution2",maxLabelResolution)
				// 只有当分辨率小于最大标签分辨率时才处理标签
				if (resolution <= maxLabelResolution) {
					// 检查是否配置了显示标签以及标签字段
					if (layerConfig.showLabels && layerConfig.labelField) {
						// 从要素属性中获取标签文本
						const properties = feature.getProperties();
						if (properties[layerConfig.labelField]) {
							labelText = String(properties[layerConfig.labelField]);
							
							// 创建标签样式
							if (labelText) {
								// 使用配置的标签样式或默认样式
								const labelStyleConfig = layerConfig.labelStyle || {
									color: '#000000',
									fontSize: 12
								};
								
								labelStyle = new Style({
									text: new Text({
										text: labelText,
										font: `${labelStyleConfig.fontSize || 12}px sans-serif`,
										fill: new Fill({
											color: labelStyleConfig.color || '#000000'
										}),
										stroke: new Stroke({
											color: '#FFFFFF',
											width: 3
										}),
										offsetY: 15 // 将文本向下偏移
									})
								});
							}
						}
					}
					// 如果没有启用标签，尝试使用样式中的label配置
					else if (styleToUse.label) {
						// 如果标签是字符串，直接使用
						if (typeof styleToUse.label === 'string') {
							labelText = styleToUse.label;
						} 
						// 如果标签是对象，按类型处理
						else if (typeof styleToUse.label === 'object' && styleToUse.label.text) {
							// 可以是属性名，例如 ${name}
							if (styleToUse.label.text.includes('${')) {
								const propRegex = /\${([^}]+)}/g;
								labelText = styleToUse.label.text.replace(propRegex, (match, propName) => {
									return feature.get(propName) || '';
								});
							} else {
								labelText = styleToUse.label.text;
							}
							
							// 创建标签样式
							if (labelText) {
								labelStyle = new Style({
									text: new Text({
										text: labelText,
										font: styleToUse.label.font || '12px sans-serif',
										fill: new Fill({
											color: styleToUse.label.fill?.color || '#000000'
										}),
										stroke: styleToUse.label.stroke ? new Stroke({
											color: styleToUse.label.stroke.color || '#FFFFFF',
											width: styleToUse.label.stroke.width || 3
										}) : undefined,
										offsetY: 15 // 将文本向下偏移
									})
								});
							}
						}
					} else {
						// 尝试使用点的name属性作为标签
						const properties = feature.getProperties();
						if (effectiveGeometryType.includes('Point') && (properties.name || properties.NAME)) {
							labelText = properties.name || properties.NAME;
							
							labelStyle = new Style({
								text: new Text({
									text: labelText,
									font: '12px sans-serif',
									fill: new Fill({
										color: '#000000'
									}),
									stroke: new Stroke({
										color: '#FFFFFF',
										width: 3
									}),
									offsetY: 15
								})
							});
						}
					}
				}
				
				// 定义基本样式
				let mainStyle;
				
				if (effectiveGeometryType === 'Point' || effectiveGeometryType === 'MultiPoint') {
					// 点样式
					const pointRadius = styleToUse.weight ? styleToUse.weight * 2 : 6;
					
					if (styleToUse.image && styleToUse.image.src) {
						// 图标样式
						mainStyle = new Style({
							image: new Icon({
								src: styleToUse.image.src,
								scale: styleToUse.image.scale || 1,
								anchor: styleToUse.image.anchor || [0.5, 0.5],
								rotation: styleToUse.image.rotation || 0
							})
						});
					} else {
						// 圆圈样式
						mainStyle = new Style({
							image: new Circle({
								radius: pointRadius,
								fill: fill,
								stroke: stroke
							})
						});
					}
				} else if (effectiveGeometryType === 'LineString' || effectiveGeometryType === 'MultiLineString') {
					// 线样式
					mainStyle = new Style({
						stroke: stroke
					});
				} else {
					// 面或默认样式
					mainStyle = new Style({
						stroke: stroke,
						fill: fill
					});
				}
				
				// 如果有标签，返回样式数组
				if (labelStyle) {
					return [mainStyle, labelStyle];
				}
				
				// 否则返回单个样式
				return mainStyle;
			};
		},
		
		/**
		 * 评估过滤条件
		 * @param feature 要素
		 * @param filter 过滤条件
		 * @returns 是否满足条件
		 */
		evaluateFilter(feature: Feature, filter: any): boolean {
			const properties = feature.getProperties();
			const value = properties[filter.property];
			
			// 在Debug模式下记录属性和比较过程
			console.log(`评估过滤条件: 属性=${filter.property}, 操作符=${filter.operator}, 值=${filter.value}, 特征值=${value}`);
			
			// 处理值类型不匹配的情况
			let filterValue = filter.value;
			let propValue = value;
			
			// 尝试转换数字
			if (!isNaN(Number(filterValue)) && typeof propValue === 'string') {
				propValue = Number(propValue);
			} else if (!isNaN(Number(propValue)) && typeof filterValue === 'string') {
				filterValue = Number(filterValue);
			}
			
			let result = false;
			
			switch (filter.operator) {
				case '=': 
					result = propValue === filterValue;
					break;
				case '!=': 
					result = propValue !== filterValue;
					break;
				case '>': 
					result = propValue > filterValue;
					break;
				case '<': 
					result = propValue < filterValue;
					break;
				case '>=': 
					result = propValue >= filterValue;
					break;
				case '<=': 
					result = propValue <= filterValue;
					break;
				case 'in': 
					result = Array.isArray(filterValue) && filterValue.includes(propValue);
					break;
				default: 
					result = false;
			}
			
			console.log(`过滤结果: ${result}`);
			return result;
		},
		
		/**
		 * 添加图层到地图
		 * 根据图层ID创建相应类型的图层并添加到地图中
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查地图实例和图层配置是否有效
		 * 2. 验证图层是否已加载（避免重复加载）
		 * 3. 根据图层类型和协议调用相应的创建方法
		 *    - 栅格图层: WMS, WMTS, XYZ
		 *    - 矢量图层: GeoJSON, MVT
		 * 4. 将创建的图层添加到OpenLayers地图中
		 * 5. 更新图层状态和加载记录
		 * 
		 * @param layerId 要添加的图层ID
		 * @returns 是否成功添加图层的Promise
		 */
		async addLayer(layerId: string): Promise<boolean> {
			if (!this.olMap) {
				console.error('地图未初始化，无法添加图层');
				return false;
			}
			
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}
			
			if (this.isLayerLoaded(layerId)) {
				return true;
			}
			
			try {
				let olLayer = null;
				
				if (layerConfig.type === 'raster') {
					if (layerConfig.protocol === 'WMS') {
						olLayer = this.createWmsLayer(layerConfig);
					} else if (layerConfig.protocol === 'WMTS') {
						olLayer = this.createWmtsLayer(layerConfig);
					} else if (layerConfig.protocol === 'XYZ') {
						olLayer = this.createXyzLayer(layerConfig);
					}
				} else if (layerConfig.type === 'vector') {
					if (layerConfig.protocol === 'GeoJSON') {
						olLayer = await this.createVectorLayer(layerConfig);
					} else if (layerConfig.protocol === 'MVT') {
						olLayer = this.createMvtLayer(layerConfig);
					} else if (layerConfig.protocol === 'WFS') {
						// WFS协议也使用createVectorLayer方法处理，因为WFS返回的是GeoJSON格式的数据
						console.log(`处理WFS协议图层 ${layerConfig.id}，URL: ${layerConfig.url}`);
						olLayer = await this.createVectorLayer(layerConfig);
					}
				}
				
				if (olLayer) {
					this.olMap.addLayer(olLayer);
					layerConfig.layerInstance = olLayer;
					layerConfig.active = true;
					this.loadedLayers.push(layerId);
					
					// 如果图层配置了显示标签，则自动启用标签显示
					if (layerConfig.showLabels && layerConfig.labelField) {
						console.log(`图层 ${layerId} 配置了自动显示标签，字段: ${layerConfig.labelField}`);
						// 这里不需要调用toggleLabel，因为createFeatureStyle已经处理了showLabels
					}
					
					return true;
				}
				
				return false;
			} catch (error) {
				console.error(`添加图层 ${layerId} 失败:`, error);
				return false;
			}
		},
		
		/**
		 * 移除图层
		 * @param layerId 图层ID
		 * @returns 是否成功移除
		 */
		removeLayer(layerId: string): boolean {
			if (!this.olMap) {
				console.error('地图未初始化，无法移除图层');
				return false;
			}
			
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}
			
			if (!this.isLayerLoaded(layerId)) {
				return true;
			}
			
			try {
				if (layerConfig.layerInstance) {
					this.olMap.removeLayer(layerConfig.layerInstance);
					layerConfig.active = false;
					
					const index = this.loadedLayers.indexOf(layerId);
					if (index > -1) {
						this.loadedLayers.splice(index, 1);
					}
					
					return true;
				}
				
				return false;
			} catch (error) {
				console.error(`移除图层 ${layerId} 失败:`, error);
				return false;
			}
		},
		
		/**
		 * 切换图层可见性
		 * 控制地图图层的显示或隐藏状态
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查图层配置和图层实例是否存在
		 * 2. 获取当前图层的可见状态
		 * 3. 将可见状态切换为相反值
		 * 4. 更新图层的active属性以反映新状态
		 * 5. 记录日志并返回新的可见状态
		 * 
		 * @param layerId 图层ID
		 * @returns 切换后的可见状态（true表示可见，false表示隐藏），如果图层不存在则返回null
		 */
		toggleLayerVisibility(layerId: string): boolean | null {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				return null;
			}
			
			try {
				const isVisible = layerConfig.layerInstance.getVisible();
				layerConfig.layerInstance.setVisible(!isVisible);
				layerConfig.active = !isVisible;
				console.log(`图层 ${layerId} 的可见性已切换为 ${!isVisible ? '显示' : '隐藏'}`);
				return !isVisible;
			} catch (error) {
				console.error(`切换图层 ${layerId} 可见性失败:`, error);
				return null;
			}
		},
		
		/**
		 * 设置图层透明度
		 * 调整地图图层的透明度级别
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查图层配置和图层实例是否存在
		 * 2. 将传入的透明度值限制在0-1范围内
		 * 3. 应用透明度值到图层实例
		 * 4. 记录日志并返回操作结果
		 * 
		 * 透明度值范围从0（完全透明）到1（完全不透明）
		 * 
		 * @param layerId 图层ID
		 * @param opacity 透明度值(0-1)
		 * @returns 是否设置成功
		 */
		setLayerOpacity(layerId: string, opacity: number): boolean {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				return false;
			}
			
			try {
				layerConfig.layerInstance.setOpacity(Math.max(0, Math.min(1, opacity)));
				console.log(`图层 ${layerId} 的透明度已设置为 ${opacity}`);
				return true;
			} catch (error) {
				console.error(`设置图层 ${layerId} 透明度失败:`, error);
				return false;
			}
		},
		
		/**
		 * 重新排序图层
		 * 根据传入的图层ID数组调整图层的叠加顺序
		 * 
		 * 该方法执行以下操作：
		 * 1. 验证图层ID数组的有效性
		 * 2. 计算每个图层的新zIndex值
		 * 3. 应用新的zIndex到图层实例
		 * 4. 返回成功更新的图层数量
		 * 
		 * 数组中的第一个图层ID将被置于最顶层（最高zIndex）
		 * 
		 * @param layerIds 图层ID数组，按显示顺序排列（第一个为最上层）
		 * @returns 成功更新的图层数量
		 */
		reorderLayers(layerIds: string[]): number {
			if (!this.olMap || !layerIds || layerIds.length === 0) {
				return 0;
			}
			
			let successCount = 0;
			const baseZIndex = 10; // 基础zIndex值
			
			// 从高到低分配zIndex（数组第一个元素获取最高的zIndex）
			layerIds.forEach((layerId, index) => {
				const layerConfig = this.getLayerById(layerId);
				if (layerConfig && layerConfig.layerInstance) {
					try {
						// 计算新的zIndex：基础值 + 反向索引值（确保第一个元素zIndex最大）
						const newZIndex = baseZIndex + (layerIds.length - index);
						layerConfig.layerInstance.setZIndex(newZIndex);
						successCount++;
						console.log(`图层 ${layerId} 的zIndex已设置为 ${newZIndex}`);
					} catch (error) {
						console.error(`设置图层 ${layerId} 的zIndex失败:`, error);
					}
				}
			});
			
			return successCount;
		},
		
		/**
		 * 切换图层标注显示
		 * 控制地图图层的标注显示或隐藏
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查图层配置和图层实例是否存在
		 * 2. 更新图层配置中的showLabels属性
		 * 3. 重新应用图层样式以显示或隐藏标注
		 * 
		 * @param layerId 图层ID
		 * @param fieldName 要显示的字段名，如不提供则使用配置中的labelField
		 * @param show 是否显示标注，如不提供则切换当前状态
		 * @returns 操作后的标注显示状态
		 */
		toggleLabel(layerId: string, fieldName?: string, show?: boolean): boolean | null {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				console.error(`图层 ${layerId} 不存在或未加载`);
				return null;
			}
			
			try {
				// 如果指定了字段名，更新labelField
				if (fieldName) {
					layerConfig.labelField = fieldName;
				}
				
				// 确保labelField存在
				if (!layerConfig.labelField) {
					console.warn(`图层 ${layerId} 未指定标注字段`);
					return false;
				}
				
				// 确定是显示还是隐藏
				const newShowLabels = show !== undefined ? show : !layerConfig.showLabels;
				layerConfig.showLabels = newShowLabels;
				
				// 设置默认标签样式（如果没有）
				if (!layerConfig.labelStyle) {
					layerConfig.labelStyle = {
						color: '#000000',
						fontSize: 12
					};
				}
				
				// 使用统一的createFeatureStyle方法创建样式函数
				const styleFunc = this.createFeatureStyle(layerConfig);
				
				// 应用到图层
				layerConfig.layerInstance.setStyle(styleFunc);
				
				console.log(`图层 ${layerId} 的标注已${newShowLabels ? '显示' : '隐藏'}，字段：${layerConfig.labelField}`);
				return newShowLabels;
			} catch (error) {
				console.error(`切换图层 ${layerId} 标注显示失败:`, error);
				return null;
			}
		},
	}
});