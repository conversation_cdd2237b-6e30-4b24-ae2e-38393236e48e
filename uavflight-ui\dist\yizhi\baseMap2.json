{"mapConfig": {"initialView": {"center": [110.877367, 23.78737], "zoom": 10, "duration": 1000}}, "layers": [{"id": "招商引资片区", "name": "招商引资片区", "type": "vector", "protocol": "WFS", "url": "http://127.0.0.1:8085/geoserver/yz/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=yz%3A%E6%8B%9B%E5%95%86%E5%BC%95%E6%99%BA%E7%89%87%E5%8C%BA&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": true, "defaultStyle": "blue_mvt", "geometryType": "MultiPolygon", "labelField": "XZMC", "showLabels": true, "labelStyle": {"color": "#000000", "fontSize": 14}}, {"id": "设施农用地潜力", "name": "设施农用地潜力", "type": "vector", "protocol": "WFS", "url": "http://127.0.0.1:8085/geoserver/yz/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=yz%3A%E8%AE%BE%E6%96%BD%E5%86%9C%E7%94%A8%E5%9C%B0%E6%BD%9C%E5%8A%9B&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": false, "defaultStyle": "red_mvt", "geometryType": "MultiPolygon"}, {"id": "招商引资片区交种植区", "name": "招商引资片区交种植区", "type": "vector", "protocol": "WFS", "url": "http://127.0.0.1:8085/geoserver/yz/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=yz%3A%E6%8B%9B%E5%95%86%E5%BC%95%E6%99%BA%E7%89%87%E5%8C%BA%E4%BA%A4%E7%A7%8D%E6%A4%8D%E5%8C%BA&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": false, "defaultStyle": "green_mvt", "geometryType": "MultiPolygon", "styleRules": [{"filter": {"property": "MJFL", "operator": "=", "value": "小于5亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#8B0000", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "5亩-10亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF0000", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "10亩-20亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF4500", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "20亩-30亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF8C00", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "30亩-40亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FFD700", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "40亩-50亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FFFF00", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "50亩-100亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#ADFF2F", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "大于100亩"}, "style": {"color": "rgb(0,255,0)", "weight": 1.5, "opacity": 1, "fillColor": "#90EE90", "fillOpacity": 0.6}}]}, {"id": "招商引资片区交设施农用地", "name": "招商引资片区交设施农用地", "type": "vector", "protocol": "WFS", "url": "http://127.0.0.1:8085/geoserver/yz/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=yz%3A%E6%8B%9B%E5%95%86%E5%BC%95%E6%99%BA%E7%89%87%E5%8C%BA%E4%BA%A4%E8%AE%BE%E6%96%BD%E5%86%9C%E7%94%A8%E5%9C%B0&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": false, "defaultStyle": "red_mvt", "geometryType": "MultiPolygon", "styleRules": [{"filter": {"property": "MJFL", "operator": "=", "value": "小于5亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#8B0000", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "5亩-10亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF0000", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "10亩-20亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF4500", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "20亩-30亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FF8C00", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "30亩-40亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FFD700", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "40亩-50亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#FFFF00", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "50亩-100亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#ADFF2F", "fillOpacity": 0.6}}, {"filter": {"property": "MJFL", "operator": "=", "value": "大于100亩"}, "style": {"color": "rgb(255,0,0)", "weight": 1.5, "opacity": 1, "fillColor": "#90EE90", "fillOpacity": 0.6}}]}, {"id": "种植区", "name": "种植区", "type": "vector", "protocol": "WFS", "url": "http://127.0.0.1:8085/geoserver/yz/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=yz%3A%E7%A7%8D%E6%A4%8D%E5%8C%BA&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": false, "defaultStyle": "purple_mvt", "geometryType": "MultiPolygon"}]}