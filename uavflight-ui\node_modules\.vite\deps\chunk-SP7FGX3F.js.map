{"version": 3, "sources": ["../../ol/reproj/Image.js", "../../ol/source/Image.js", "../../ol/layer/BaseImage.js", "../../ol/renderer/canvas/ImageLayer.js", "../../ol/layer/Image.js", "../../ol/ImageCanvas.js", "../../ol/source/Raster.js", "../../ol/net.js", "../../ol/source/TileJSON.js", "../../ol/DataTile.js", "../../ol/reproj/DataTile.js"], "sourcesContent": ["/**\n * @module ol/reproj/Image\n */\nimport {ERROR_THRESHOLD} from './common.js';\n\nimport EventType from '../events/EventType.js';\nimport ImageBase from '../ImageBase.js';\nimport ImageState from '../ImageState.js';\nimport Triangulation from './Triangulation.js';\nimport {\n  calculateSourceResolution,\n  render as renderReprojected,\n} from '../reproj.js';\nimport {\n  getCenter,\n  getHeight,\n  getIntersection,\n  getWidth,\n  isEmpty,\n} from '../extent.js';\nimport {listen, unlistenByKey} from '../events.js';\n\n/**\n * @typedef {function(import(\"../extent.js\").Extent, number, number) : import(\"../ImageBase.js\").default} FunctionType\n */\n\n/**\n * @classdesc\n * Class encapsulating single reprojected image.\n * See {@link module:ol/source/Image~ImageSource}.\n */\nclass ReprojImage extends ImageBase {\n  /**\n   * @param {import(\"../proj/Projection.js\").default} sourceProj Source projection (of the data).\n   * @param {import(\"../proj/Projection.js\").default} targetProj Target projection.\n   * @param {import(\"../extent.js\").Extent} targetExtent Target extent.\n   * @param {number} targetResolution Target resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {FunctionType} getImageFunction\n   *     Function returning source images (extent, resolution, pixelRatio).\n   * @param {boolean} interpolate Use linear interpolation when resampling.\n   */\n  constructor(\n    sourceProj,\n    targetProj,\n    targetExtent,\n    targetResolution,\n    pixelRatio,\n    getImageFunction,\n    interpolate\n  ) {\n    let maxSourceExtent = sourceProj.getExtent();\n    if (maxSourceExtent && sourceProj.canWrapX()) {\n      maxSourceExtent = maxSourceExtent.slice();\n      maxSourceExtent[0] = -Infinity;\n      maxSourceExtent[2] = Infinity;\n    }\n    let maxTargetExtent = targetProj.getExtent();\n    if (maxTargetExtent && targetProj.canWrapX()) {\n      maxTargetExtent = maxTargetExtent.slice();\n      maxTargetExtent[0] = -Infinity;\n      maxTargetExtent[2] = Infinity;\n    }\n\n    const limitedTargetExtent = maxTargetExtent\n      ? getIntersection(targetExtent, maxTargetExtent)\n      : targetExtent;\n\n    const targetCenter = getCenter(limitedTargetExtent);\n    const sourceResolution = calculateSourceResolution(\n      sourceProj,\n      targetProj,\n      targetCenter,\n      targetResolution\n    );\n\n    const errorThresholdInPixels = ERROR_THRESHOLD;\n\n    const triangulation = new Triangulation(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      maxSourceExtent,\n      sourceResolution * errorThresholdInPixels,\n      targetResolution\n    );\n\n    const sourceExtent = triangulation.calculateSourceExtent();\n    const sourceImage = isEmpty(sourceExtent)\n      ? null\n      : getImageFunction(sourceExtent, sourceResolution, pixelRatio);\n    const state = sourceImage ? ImageState.IDLE : ImageState.EMPTY;\n    const sourcePixelRatio = sourceImage ? sourceImage.getPixelRatio() : 1;\n\n    super(targetExtent, targetResolution, sourcePixelRatio, state);\n\n    /**\n     * @private\n     * @type {import(\"../proj/Projection.js\").default}\n     */\n    this.targetProj_ = targetProj;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.maxSourceExtent_ = maxSourceExtent;\n\n    /**\n     * @private\n     * @type {!import(\"./Triangulation.js\").default}\n     */\n    this.triangulation_ = triangulation;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.targetResolution_ = targetResolution;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.targetExtent_ = targetExtent;\n\n    /**\n     * @private\n     * @type {import(\"../ImageBase.js\").default}\n     */\n    this.sourceImage_ = sourceImage;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.sourcePixelRatio_ = sourcePixelRatio;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.interpolate_ = interpolate;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement}\n     */\n    this.canvas_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../events.js\").EventsKey}\n     */\n    this.sourceListenerKey_ = null;\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    if (this.state == ImageState.LOADING) {\n      this.unlistenSource_();\n    }\n    super.disposeInternal();\n  }\n\n  /**\n   * @return {HTMLCanvasElement} Image.\n   */\n  getImage() {\n    return this.canvas_;\n  }\n\n  /**\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   */\n  getProjection() {\n    return this.targetProj_;\n  }\n\n  /**\n   * @private\n   */\n  reproject_() {\n    const sourceState = this.sourceImage_.getState();\n    if (sourceState == ImageState.LOADED) {\n      const width = getWidth(this.targetExtent_) / this.targetResolution_;\n      const height = getHeight(this.targetExtent_) / this.targetResolution_;\n\n      this.canvas_ = renderReprojected(\n        width,\n        height,\n        this.sourcePixelRatio_,\n        this.sourceImage_.getResolution(),\n        this.maxSourceExtent_,\n        this.targetResolution_,\n        this.targetExtent_,\n        this.triangulation_,\n        [\n          {\n            extent: this.sourceImage_.getExtent(),\n            image: this.sourceImage_.getImage(),\n          },\n        ],\n        0,\n        undefined,\n        this.interpolate_\n      );\n    }\n    this.state = sourceState;\n    this.changed();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state == ImageState.IDLE) {\n      this.state = ImageState.LOADING;\n      this.changed();\n\n      const sourceState = this.sourceImage_.getState();\n      if (sourceState == ImageState.LOADED || sourceState == ImageState.ERROR) {\n        this.reproject_();\n      } else {\n        this.sourceListenerKey_ = listen(\n          this.sourceImage_,\n          EventType.CHANGE,\n          function (e) {\n            const sourceState = this.sourceImage_.getState();\n            if (\n              sourceState == ImageState.LOADED ||\n              sourceState == ImageState.ERROR\n            ) {\n              this.unlistenSource_();\n              this.reproject_();\n            }\n          },\n          this\n        );\n        this.sourceImage_.load();\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  unlistenSource_() {\n    unlistenByKey(\n      /** @type {!import(\"../events.js\").EventsKey} */ (this.sourceListenerKey_)\n    );\n    this.sourceListenerKey_ = null;\n  }\n}\n\nexport default ReprojImage;\n", "/**\n * @module ol/source/Image\n */\nimport Event from '../events/Event.js';\nimport ImageState from '../ImageState.js';\nimport ReprojImage from '../reproj/Image.js';\nimport Source from './Source.js';\nimport {abstract} from '../util.js';\nimport {equals} from '../extent.js';\nimport {equivalent} from '../proj.js';\nimport {linearFindNearest} from '../array.js';\n\n/**\n * @enum {string}\n */\nexport const ImageSourceEventType = {\n  /**\n   * Triggered when an image starts loading.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloadstart\n   * @api\n   */\n  IMAGELOADSTART: 'imageloadstart',\n\n  /**\n   * Triggered when an image finishes loading.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloadend\n   * @api\n   */\n  IMAGELOADEND: 'imageloadend',\n\n  /**\n   * Triggered if image loading results in an error.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloaderror\n   * @api\n   */\n  IMAGELOADERROR: 'imageloaderror',\n};\n\n/**\n * @typedef {'imageloadend'|'imageloaderror'|'imageloadstart'} ImageSourceEventTypes\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Image~ImageSource} instances are instances of this\n * type.\n */\nexport class ImageSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../Image.js\").default} image The image.\n   */\n  constructor(type, image) {\n    super(type);\n\n    /**\n     * The image related to the event.\n     * @type {import(\"../Image.js\").default}\n     * @api\n     */\n    this.image = image;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<ImageSourceEventTypes, ImageSourceEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types\n *     |ImageSourceEventTypes, Return>} ImageSourceOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection.\n * @property {Array<number>} [resolutions] Resolutions.\n * @property {import(\"./Source.js\").State} [state] State.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for sources providing a single image.\n * @abstract\n * @fires module:ol/source/Image.ImageSourceEvent\n * @api\n */\nclass ImageSource extends Source {\n  /**\n   * @param {Options} options Single image source options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      projection: options.projection,\n      state: options.state,\n      interpolate:\n        options.interpolate !== undefined ? options.interpolate : true,\n    });\n\n    /***\n     * @type {ImageSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ImageSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ImageSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.resolutions_ =\n      options.resolutions !== undefined ? options.resolutions : null;\n\n    /**\n     * @private\n     * @type {import(\"../reproj/Image.js\").default}\n     */\n    this.reprojectedImage_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.reprojectedRevision_ = 0;\n  }\n\n  /**\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions() {\n    return this.resolutions_;\n  }\n\n  /**\n   * @param {Array<number>|null} resolutions Resolutions.\n   */\n  setResolutions(resolutions) {\n    this.resolutions_ = resolutions;\n  }\n\n  /**\n   * @protected\n   * @param {number} resolution Resolution.\n   * @return {number} Resolution.\n   */\n  findNearestResolution(resolution) {\n    const resolutions = this.getResolutions();\n    if (resolutions) {\n      const idx = linearFindNearest(resolutions, resolution, 0);\n      resolution = resolutions[idx];\n    }\n    return resolution;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../ImageBase.js\").default} Single image.\n   */\n  getImage(extent, resolution, pixelRatio, projection) {\n    const sourceProjection = this.getProjection();\n    if (\n      !sourceProjection ||\n      !projection ||\n      equivalent(sourceProjection, projection)\n    ) {\n      if (sourceProjection) {\n        projection = sourceProjection;\n      }\n      return this.getImageInternal(extent, resolution, pixelRatio, projection);\n    }\n    if (this.reprojectedImage_) {\n      if (\n        this.reprojectedRevision_ == this.getRevision() &&\n        equivalent(this.reprojectedImage_.getProjection(), projection) &&\n        this.reprojectedImage_.getResolution() == resolution &&\n        equals(this.reprojectedImage_.getExtent(), extent)\n      ) {\n        return this.reprojectedImage_;\n      }\n      this.reprojectedImage_.dispose();\n      this.reprojectedImage_ = null;\n    }\n\n    this.reprojectedImage_ = new ReprojImage(\n      sourceProjection,\n      projection,\n      extent,\n      resolution,\n      pixelRatio,\n      (extent, resolution, pixelRatio) =>\n        this.getImageInternal(extent, resolution, pixelRatio, sourceProjection),\n      this.getInterpolate()\n    );\n    this.reprojectedRevision_ = this.getRevision();\n\n    return this.reprojectedImage_;\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../ImageBase.js\").default} Single image.\n   * @protected\n   */\n  getImageInternal(extent, resolution, pixelRatio, projection) {\n    return abstract();\n  }\n\n  /**\n   * Handle image change events.\n   * @param {import(\"../events/Event.js\").default} event Event.\n   * @protected\n   */\n  handleImageChange(event) {\n    const image = /** @type {import(\"../Image.js\").default} */ (event.target);\n    let type;\n    switch (image.getState()) {\n      case ImageState.LOADING:\n        this.loading = true;\n        type = ImageSourceEventType.IMAGELOADSTART;\n        break;\n      case ImageState.LOADED:\n        this.loading = false;\n        type = ImageSourceEventType.IMAGELOADEND;\n        break;\n      case ImageState.ERROR:\n        this.loading = false;\n        type = ImageSourceEventType.IMAGELOADERROR;\n        break;\n      default:\n        return;\n    }\n    if (this.hasListener(type)) {\n      this.dispatchEvent(new ImageSourceEvent(type, image));\n    }\n  }\n}\n\n/**\n * Default image load function for image sources that use import(\"../Image.js\").Image image\n * instances.\n * @param {import(\"../Image.js\").default} image Image.\n * @param {string} src Source.\n */\nexport function defaultImageLoadFunction(image, src) {\n  /** @type {HTMLImageElement|HTMLVideoElement} */ (image.getImage()).src = src;\n}\n\nexport default ImageSource;\n", "/**\n * @module ol/layer/BaseImage\n */\nimport Layer from './Layer.js';\n\n/**\n * @template {import(\"../source/Image.js\").default} ImageSourceType\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {import(\"../Map.js\").default} [map] Sets the layer as overlay on a map. The map will not manage\n * this layer in its layers collection, and the layer will be rendered on top. This is useful for\n * temporary layers. The standard way to add a layer to a map and have it managed by the map is to\n * use {@link import(\"../Map.js\").default#addLayer map.addLayer()}.\n * @property {ImageSourceType} [source] Source for this layer.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @classdesc\n * Server-rendered images that are available for arbitrary extents and\n * resolutions.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Image.js\").default} ImageSourceType\n * @template {import(\"../renderer/Layer.js\").default} RendererType\n * @extends {Layer<ImageSourceType, RendererType>}\n * @api\n */\nclass BaseImageLayer extends Layer {\n  /**\n   * @param {Options<ImageSourceType>} [options] Layer options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n    super(options);\n  }\n}\n\nexport default BaseImageLayer;\n", "/**\n * @module ol/renderer/canvas/ImageLayer\n */\nimport CanvasLayerRenderer from './Layer.js';\nimport ImageState from '../../ImageState.js';\nimport ViewHint from '../../ViewHint.js';\nimport {\n  apply as applyTransform,\n  compose as composeTransform,\n  makeInverse,\n  toString as toTransformString,\n} from '../../transform.js';\nimport {\n  containsCoordinate,\n  containsExtent,\n  getHeight,\n  getIntersection,\n  getWidth,\n  intersects as intersectsExtent,\n  isEmpty,\n} from '../../extent.js';\nimport {fromUserExtent} from '../../proj.js';\n\n/**\n * @classdesc\n * Canvas renderer for image layers.\n * @api\n */\nclass CanvasImageLayerRenderer extends CanvasLayerRenderer {\n  /**\n   * @param {import(\"../../layer/Image.js\").default} imageLayer Image layer.\n   */\n  constructor(imageLayer) {\n    super(imageLayer);\n\n    /**\n     * @protected\n     * @type {?import(\"../../ImageBase.js\").default}\n     */\n    this.image_ = null;\n  }\n\n  /**\n   * @return {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} Image.\n   */\n  getImage() {\n    return this.image_ ? this.image_.getImage() : null;\n  }\n\n  /**\n   * Determine whether render should be called.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {boolean} Layer is ready to be rendered.\n   */\n  prepareFrame(frameState) {\n    const layerState = frameState.layerStatesArray[frameState.layerIndex];\n    const pixelRatio = frameState.pixelRatio;\n    const viewState = frameState.viewState;\n    const viewResolution = viewState.resolution;\n\n    const imageSource = this.getLayer().getSource();\n\n    const hints = frameState.viewHints;\n\n    let renderedExtent = frameState.extent;\n    if (layerState.extent !== undefined) {\n      renderedExtent = getIntersection(\n        renderedExtent,\n        fromUserExtent(layerState.extent, viewState.projection)\n      );\n    }\n\n    if (\n      !hints[ViewHint.ANIMATING] &&\n      !hints[ViewHint.INTERACTING] &&\n      !isEmpty(renderedExtent)\n    ) {\n      if (imageSource) {\n        const projection = viewState.projection;\n        const image = imageSource.getImage(\n          renderedExtent,\n          viewResolution,\n          pixelRatio,\n          projection\n        );\n        if (image) {\n          if (this.loadImage(image)) {\n            this.image_ = image;\n          } else if (image.getState() === ImageState.EMPTY) {\n            this.image_ = null;\n          }\n        }\n      } else {\n        this.image_ = null;\n      }\n    }\n\n    return !!this.image_;\n  }\n\n  /**\n   * @param {import(\"../../pixel.js\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray} Data at the pixel location.\n   */\n  getData(pixel) {\n    const frameState = this.frameState;\n    if (!frameState) {\n      return null;\n    }\n\n    const layer = this.getLayer();\n    const coordinate = applyTransform(\n      frameState.pixelToCoordinateTransform,\n      pixel.slice()\n    );\n\n    const layerExtent = layer.getExtent();\n    if (layerExtent) {\n      if (!containsCoordinate(layerExtent, coordinate)) {\n        return null;\n      }\n    }\n\n    const imageExtent = this.image_.getExtent();\n    const img = this.getImage();\n\n    const imageMapWidth = getWidth(imageExtent);\n    const col = Math.floor(\n      img.width * ((coordinate[0] - imageExtent[0]) / imageMapWidth)\n    );\n    if (col < 0 || col >= img.width) {\n      return null;\n    }\n\n    const imageMapHeight = getHeight(imageExtent);\n    const row = Math.floor(\n      img.height * ((imageExtent[3] - coordinate[1]) / imageMapHeight)\n    );\n    if (row < 0 || row >= img.height) {\n      return null;\n    }\n\n    return this.getImageData(img, col, row);\n  }\n\n  /**\n   * Render the layer.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {HTMLElement} target Target that may be used to render content to.\n   * @return {HTMLElement} The rendered element.\n   */\n  renderFrame(frameState, target) {\n    const image = this.image_;\n    const imageExtent = image.getExtent();\n    const imageResolution = image.getResolution();\n    const imagePixelRatio = image.getPixelRatio();\n    const layerState = frameState.layerStatesArray[frameState.layerIndex];\n    const pixelRatio = frameState.pixelRatio;\n    const viewState = frameState.viewState;\n    const viewCenter = viewState.center;\n    const viewResolution = viewState.resolution;\n    const scale =\n      (pixelRatio * imageResolution) / (viewResolution * imagePixelRatio);\n\n    const extent = frameState.extent;\n    const resolution = viewState.resolution;\n    const rotation = viewState.rotation;\n    // desired dimensions of the canvas in pixels\n    const width = Math.round((getWidth(extent) / resolution) * pixelRatio);\n    const height = Math.round((getHeight(extent) / resolution) * pixelRatio);\n\n    // set forward and inverse pixel transforms\n    composeTransform(\n      this.pixelTransform,\n      frameState.size[0] / 2,\n      frameState.size[1] / 2,\n      1 / pixelRatio,\n      1 / pixelRatio,\n      rotation,\n      -width / 2,\n      -height / 2\n    );\n    makeInverse(this.inversePixelTransform, this.pixelTransform);\n\n    const canvasTransform = toTransformString(this.pixelTransform);\n\n    this.useContainer(target, canvasTransform, this.getBackground(frameState));\n\n    const context = this.context;\n    const canvas = context.canvas;\n\n    if (canvas.width != width || canvas.height != height) {\n      canvas.width = width;\n      canvas.height = height;\n    } else if (!this.containerReused) {\n      context.clearRect(0, 0, width, height);\n    }\n\n    // clipped rendering if layer extent is set\n    let clipped = false;\n    let render = true;\n    if (layerState.extent) {\n      const layerExtent = fromUserExtent(\n        layerState.extent,\n        viewState.projection\n      );\n      render = intersectsExtent(layerExtent, frameState.extent);\n      clipped = render && !containsExtent(layerExtent, frameState.extent);\n      if (clipped) {\n        this.clipUnrotated(context, frameState, layerExtent);\n      }\n    }\n\n    const img = this.getImage();\n\n    const transform = composeTransform(\n      this.tempTransform,\n      width / 2,\n      height / 2,\n      scale,\n      scale,\n      0,\n      (imagePixelRatio * (imageExtent[0] - viewCenter[0])) / imageResolution,\n      (imagePixelRatio * (viewCenter[1] - imageExtent[3])) / imageResolution\n    );\n\n    this.renderedResolution = (imageResolution * pixelRatio) / imagePixelRatio;\n\n    const dw = img.width * transform[0];\n    const dh = img.height * transform[3];\n\n    if (!this.getLayer().getSource().getInterpolate()) {\n      context.imageSmoothingEnabled = false;\n    }\n\n    this.preRender(context, frameState);\n    if (render && dw >= 0.5 && dh >= 0.5) {\n      const dx = transform[4];\n      const dy = transform[5];\n      const opacity = layerState.opacity;\n      let previousAlpha;\n      if (opacity !== 1) {\n        previousAlpha = context.globalAlpha;\n        context.globalAlpha = opacity;\n      }\n      context.drawImage(img, 0, 0, +img.width, +img.height, dx, dy, dw, dh);\n      if (opacity !== 1) {\n        context.globalAlpha = previousAlpha;\n      }\n    }\n    this.postRender(context, frameState);\n\n    if (clipped) {\n      context.restore();\n    }\n    context.imageSmoothingEnabled = true;\n\n    if (canvasTransform !== canvas.style.transform) {\n      canvas.style.transform = canvasTransform;\n    }\n\n    return this.container;\n  }\n}\n\nexport default CanvasImageLayerRenderer;\n", "/**\n * @module ol/layer/Image\n */\nimport BaseImageLayer from './BaseImage.js';\nimport CanvasImageLayerRenderer from '../renderer/canvas/ImageLayer.js';\n\n/**\n * @classdesc\n * Server-rendered images that are available for arbitrary extents and\n * resolutions.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Image.js\").default} ImageSourceType\n * @extends {BaseImageLayer<ImageSourceType, CanvasImageLayerRenderer>}\n * @api\n */\nclass ImageLayer extends BaseImageLayer {\n  /**\n   * @param {import(\"./BaseImage.js\").Options<ImageSourceType>} [options] Layer options.\n   */\n  constructor(options) {\n    super(options);\n  }\n\n  createRenderer() {\n    return new CanvasImageLayerRenderer(this);\n  }\n\n  /**\n   * Get data for a pixel location.  A four element RGBA array will be returned.  For requests outside the\n   * layer extent, `null` will be returned.  Data for an image can only be retrieved if the\n   * source's `crossOrigin` property is set.\n   *\n   * ```js\n   * // display layer data on every pointer move\n   * map.on('pointermove', (event) => {\n   *   console.log(layer.getData(event.pixel));\n   * });\n   * ```\n   * @param {import(\"../pixel\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray|Uint8Array|Float32Array|DataView|null} Pixel data.\n   * @api\n   */\n  getData(pixel) {\n    return super.getData(pixel);\n  }\n}\n\nexport default ImageLayer;\n", "/**\n * @module ol/ImageCanvas\n */\nimport ImageBase from './ImageBase.js';\nimport ImageState from './ImageState.js';\n\n/**\n * A function that is called to trigger asynchronous canvas drawing.  It is\n * called with a \"done\" callback that should be called when drawing is done.\n * If any error occurs during drawing, the \"done\" callback should be called with\n * that error.\n *\n * @typedef {function(function(Error=): void): void} Loader\n */\n\nclass ImageCanvas extends ImageBase {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {HTMLCanvasElement} canvas Canvas.\n   * @param {Loader} [loader] Optional loader function to\n   *     support asynchronous canvas drawing.\n   */\n  constructor(extent, resolution, pixelRatio, canvas, loader) {\n    const state = loader !== undefined ? ImageState.IDLE : ImageState.LOADED;\n\n    super(extent, resolution, pixelRatio, state);\n\n    /**\n     * Optional canvas loader function.\n     * @type {?Loader}\n     * @private\n     */\n    this.loader_ = loader !== undefined ? loader : null;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement}\n     */\n    this.canvas_ = canvas;\n\n    /**\n     * @private\n     * @type {?Error}\n     */\n    this.error_ = null;\n  }\n\n  /**\n   * Get any error associated with asynchronous rendering.\n   * @return {?Error} Any error that occurred during rendering.\n   */\n  getError() {\n    return this.error_;\n  }\n\n  /**\n   * Handle async drawing complete.\n   * @param {Error} [err] Any error during drawing.\n   * @private\n   */\n  handleLoad_(err) {\n    if (err) {\n      this.error_ = err;\n      this.state = ImageState.ERROR;\n    } else {\n      this.state = ImageState.LOADED;\n    }\n    this.changed();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state == ImageState.IDLE) {\n      this.state = ImageState.LOADING;\n      this.changed();\n      this.loader_(this.handleLoad_.bind(this));\n    }\n  }\n\n  /**\n   * @return {HTMLCanvasElement} Canvas element.\n   */\n  getImage() {\n    return this.canvas_;\n  }\n}\n\nexport default ImageCanvas;\n", "/**\n * @module ol/source/Raster\n */\nimport Disposable from '../Disposable.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport ImageCanvas from '../ImageCanvas.js';\nimport ImageLayer from '../layer/Image.js';\nimport ImageSource from './Image.js';\nimport Source from './Source.js';\nimport TileLayer from '../layer/Tile.js';\nimport TileQueue from '../TileQueue.js';\nimport TileSource from './Tile.js';\nimport {createCanvasContext2D} from '../dom.js';\nimport {create as createTransform} from '../transform.js';\nimport {equals, getCenter, getHeight, getWidth} from '../extent.js';\nimport {getUid} from '../util.js';\n\nlet hasImageData = true;\ntry {\n  new ImageData(10, 10);\n} catch (_) {\n  hasImageData = false;\n}\n\n/** @type {CanvasRenderingContext2D} */\nlet context;\n\n/**\n * @param {Uint8ClampedArray} data Image data.\n * @param {number} width Number of columns.\n * @param {number} height Number of rows.\n * @return {ImageData} Image data.\n */\nexport function newImageData(data, width, height) {\n  if (hasImageData) {\n    return new ImageData(data, width, height);\n  }\n\n  if (!context) {\n    context = document.createElement('canvas').getContext('2d');\n  }\n  const imageData = context.createImageData(width, height);\n  imageData.data.set(data);\n  return imageData;\n}\n\n/**\n * @typedef {Object} MinionData\n * @property {Array<ArrayBuffer>} buffers Array of buffers.\n * @property {Object} meta Operation metadata.\n * @property {boolean} imageOps The operation is an image operation.\n * @property {number} width The width of the image.\n * @property {number} height The height of the image.\n */\n\n/* istanbul ignore next */\n/**\n * Create a function for running operations.  This function is serialized for\n * use in a worker.\n * @param {function(Array, Object):*} operation The operation.\n * @return {function(MinionData):ArrayBuffer} A function that takes an object with\n * buffers, meta, imageOps, width, and height properties and returns an array\n * buffer.\n */\nfunction createMinion(operation) {\n  let workerHasImageData = true;\n  try {\n    new ImageData(10, 10);\n  } catch (_) {\n    workerHasImageData = false;\n  }\n\n  function newWorkerImageData(data, width, height) {\n    if (workerHasImageData) {\n      return new ImageData(data, width, height);\n    }\n    return {data: data, width: width, height: height};\n  }\n\n  return function (data) {\n    // bracket notation for minification support\n    const buffers = data['buffers'];\n    const meta = data['meta'];\n    const imageOps = data['imageOps'];\n    const width = data['width'];\n    const height = data['height'];\n\n    const numBuffers = buffers.length;\n    const numBytes = buffers[0].byteLength;\n\n    if (imageOps) {\n      const images = new Array(numBuffers);\n      for (let b = 0; b < numBuffers; ++b) {\n        images[b] = newWorkerImageData(\n          new Uint8ClampedArray(buffers[b]),\n          width,\n          height\n        );\n      }\n      const output = operation(images, meta).data;\n      return output.buffer;\n    }\n\n    const output = new Uint8ClampedArray(numBytes);\n    const arrays = new Array(numBuffers);\n    const pixels = new Array(numBuffers);\n    for (let b = 0; b < numBuffers; ++b) {\n      arrays[b] = new Uint8ClampedArray(buffers[b]);\n      pixels[b] = [0, 0, 0, 0];\n    }\n    for (let i = 0; i < numBytes; i += 4) {\n      for (let j = 0; j < numBuffers; ++j) {\n        const array = arrays[j];\n        pixels[j][0] = array[i];\n        pixels[j][1] = array[i + 1];\n        pixels[j][2] = array[i + 2];\n        pixels[j][3] = array[i + 3];\n      }\n      const pixel = operation(pixels, meta);\n      output[i] = pixel[0];\n      output[i + 1] = pixel[1];\n      output[i + 2] = pixel[2];\n      output[i + 3] = pixel[3];\n    }\n    return output.buffer;\n  };\n}\n\n/**\n * Create a worker for running operations.\n * @param {ProcessorOptions} config Processor options.\n * @param {function(MessageEvent): void} onMessage Called with a message event.\n * @return {Worker} The worker.\n */\nfunction createWorker(config, onMessage) {\n  const lib = Object.keys(config.lib || {}).map(function (name) {\n    return 'const ' + name + ' = ' + config.lib[name].toString() + ';';\n  });\n\n  const lines = lib.concat([\n    'const __minion__ = (' + createMinion.toString() + ')(',\n    config.operation.toString(),\n    ');',\n    'self.addEventListener(\"message\", function(event) {',\n    '  const buffer = __minion__(event.data);',\n    '  self.postMessage({buffer: buffer, meta: event.data.meta}, [buffer]);',\n    '});',\n  ]);\n\n  const worker = new Worker(\n    typeof Blob === 'undefined'\n      ? 'data:text/javascript;base64,' +\n        Buffer.from(lines.join('\\n'), 'binary').toString('base64')\n      : URL.createObjectURL(new Blob(lines, {type: 'text/javascript'}))\n  );\n  worker.addEventListener('message', onMessage);\n  return worker;\n}\n\n/**\n * @typedef {Object} FauxMessageEvent\n * @property {Object} data Message data.\n */\n\n/**\n * Create a faux worker for running operations.\n * @param {ProcessorOptions} config Configuration.\n * @param {function(FauxMessageEvent): void} onMessage Called with a message event.\n * @return {Object} The faux worker.\n */\nfunction createFauxWorker(config, onMessage) {\n  const minion = createMinion(config.operation);\n  let terminated = false;\n  return {\n    postMessage: function (data) {\n      setTimeout(function () {\n        if (terminated) {\n          return;\n        }\n        onMessage({data: {buffer: minion(data), meta: data['meta']}});\n      }, 0);\n    },\n    terminate: function () {\n      terminated = true;\n    },\n  };\n}\n\n/**\n * @typedef {function(Error, ImageData, (Object|Array<Object>)): void} JobCallback\n */\n\n/**\n * @typedef {Object} Job\n * @property {Object} meta Job metadata.\n * @property {Array<ImageData>} inputs Array of input data.\n * @property {JobCallback} callback Called when the job is complete.\n */\n\n/**\n * @typedef {Object} ProcessorOptions\n * @property {number} threads Number of workers to spawn.\n * @property {Operation} operation The operation.\n * @property {Object<string, Function>} [lib] Functions that will be made available to operations run in a worker.\n * @property {number} queue The number of queued jobs to allow.\n * @property {boolean} [imageOps=false] Pass all the image data to the operation instead of a single pixel.\n */\n\n/**\n * @classdesc\n * A processor runs pixel or image operations in workers.\n */\nexport class Processor extends Disposable {\n  /**\n   * @param {ProcessorOptions} config Configuration.\n   */\n  constructor(config) {\n    super();\n\n    this._imageOps = !!config.imageOps;\n    let threads;\n    if (config.threads === 0) {\n      threads = 0;\n    } else if (this._imageOps) {\n      threads = 1;\n    } else {\n      threads = config.threads || 1;\n    }\n\n    /**\n     * @type {Array<Worker>}\n     */\n    const workers = new Array(threads);\n    if (threads) {\n      for (let i = 0; i < threads; ++i) {\n        workers[i] = createWorker(config, this._onWorkerMessage.bind(this, i));\n      }\n    } else {\n      workers[0] = createFauxWorker(\n        config,\n        this._onWorkerMessage.bind(this, 0)\n      );\n    }\n    this._workers = workers;\n\n    /**\n     * @type {Array<Job>}\n     * @private\n     */\n    this._queue = [];\n\n    this._maxQueueLength = config.queue || Infinity;\n    this._running = 0;\n\n    /**\n     * @type {Object<number, any>}\n     * @private\n     */\n    this._dataLookup = {};\n\n    /**\n     * @type {Job}\n     * @private\n     */\n    this._job = null;\n  }\n\n  /**\n   * Run operation on input data.\n   * @param {Array<ImageData>} inputs Array of image data.\n   * @param {Object} meta A user data object.  This is passed to all operations\n   *     and must be serializable.\n   * @param {function(Error, ImageData, Object): void} callback Called when work\n   *     completes.  The first argument is any error.  The second is the ImageData\n   *     generated by operations.  The third is the user data object.\n   */\n  process(inputs, meta, callback) {\n    this._enqueue({\n      inputs: inputs,\n      meta: meta,\n      callback: callback,\n    });\n    this._dispatch();\n  }\n\n  /**\n   * Add a job to the queue.\n   * @param {Job} job The job.\n   */\n  _enqueue(job) {\n    this._queue.push(job);\n    while (this._queue.length > this._maxQueueLength) {\n      this._queue.shift().callback(null, null);\n    }\n  }\n\n  /**\n   * Dispatch a job.\n   */\n  _dispatch() {\n    if (this._running || this._queue.length === 0) {\n      return;\n    }\n\n    const job = this._queue.shift();\n    this._job = job;\n    const width = job.inputs[0].width;\n    const height = job.inputs[0].height;\n    const buffers = job.inputs.map(function (input) {\n      return input.data.buffer;\n    });\n    const threads = this._workers.length;\n    this._running = threads;\n    if (threads === 1) {\n      this._workers[0].postMessage(\n        {\n          buffers: buffers,\n          meta: job.meta,\n          imageOps: this._imageOps,\n          width: width,\n          height: height,\n        },\n        buffers\n      );\n      return;\n    }\n\n    const length = job.inputs[0].data.length;\n    const segmentLength = 4 * Math.ceil(length / 4 / threads);\n    for (let i = 0; i < threads; ++i) {\n      const offset = i * segmentLength;\n      const slices = [];\n      for (let j = 0, jj = buffers.length; j < jj; ++j) {\n        slices.push(buffers[j].slice(offset, offset + segmentLength));\n      }\n      this._workers[i].postMessage(\n        {\n          buffers: slices,\n          meta: job.meta,\n          imageOps: this._imageOps,\n          width: width,\n          height: height,\n        },\n        slices\n      );\n    }\n  }\n\n  /**\n   * Handle messages from the worker.\n   * @param {number} index The worker index.\n   * @param {MessageEvent} event The message event.\n   */\n  _onWorkerMessage(index, event) {\n    if (this.disposed) {\n      return;\n    }\n    this._dataLookup[index] = event.data;\n    --this._running;\n    if (this._running === 0) {\n      this._resolveJob();\n    }\n  }\n\n  /**\n   * Resolve a job.  If there are no more worker threads, the processor callback\n   * will be called.\n   */\n  _resolveJob() {\n    const job = this._job;\n    const threads = this._workers.length;\n    let data, meta;\n    if (threads === 1) {\n      data = new Uint8ClampedArray(this._dataLookup[0]['buffer']);\n      meta = this._dataLookup[0]['meta'];\n    } else {\n      const length = job.inputs[0].data.length;\n      data = new Uint8ClampedArray(length);\n      meta = new Array(threads);\n      const segmentLength = 4 * Math.ceil(length / 4 / threads);\n      for (let i = 0; i < threads; ++i) {\n        const buffer = this._dataLookup[i]['buffer'];\n        const offset = i * segmentLength;\n        data.set(new Uint8ClampedArray(buffer), offset);\n        meta[i] = this._dataLookup[i]['meta'];\n      }\n    }\n    this._job = null;\n    this._dataLookup = {};\n    job.callback(\n      null,\n      newImageData(data, job.inputs[0].width, job.inputs[0].height),\n      meta\n    );\n    this._dispatch();\n  }\n\n  /**\n   * Terminate all workers associated with the processor.\n   */\n  disposeInternal() {\n    for (let i = 0; i < this._workers.length; ++i) {\n      this._workers[i].terminate();\n    }\n    this._workers.length = 0;\n  }\n}\n\n/**\n * A function that takes an array of input data, performs some operation, and\n * returns an array of output data.\n * For `pixel` type operations, the function will be called with an array of\n * pixels, where each pixel is an array of four numbers (`[r, g, b, a]`) in the\n * range of 0 - 255. It should return a single pixel array.\n * For `'image'` type operations, functions will be called with an array of\n * [ImageData](https://developer.mozilla.org/en-US/docs/Web/API/ImageData)\n * and should return a single\n * [ImageData](https://developer.mozilla.org/en-US/docs/Web/API/ImageData).\n * The operations\n * are called with a second \"data\" argument, which can be used for storage.  The\n * data object is accessible from raster events, where it can be initialized in\n * \"beforeoperations\" and accessed again in \"afteroperations\".\n *\n * @typedef {function((Array<Array<number>>|Array<ImageData>), Object):\n *     (Array<number>|ImageData)} Operation\n */\n\n/**\n * @enum {string}\n */\nconst RasterEventType = {\n  /**\n   * Triggered before operations are run.  Listeners will receive an event object with\n   * a `data` property that can be used to make data available to operations.\n   * @event module:ol/source/Raster.RasterSourceEvent#beforeoperations\n   * @api\n   */\n  BEFOREOPERATIONS: 'beforeoperations',\n\n  /**\n   * Triggered after operations are run.  Listeners will receive an event object with\n   * a `data` property.  If more than one thread is used, `data` will be an array of\n   * objects.  If a single thread is used, `data` will be a single object.\n   * @event module:ol/source/Raster.RasterSourceEvent#afteroperations\n   * @api\n   */\n  AFTEROPERATIONS: 'afteroperations',\n};\n\n/**\n * @typedef {'pixel' | 'image'} RasterOperationType\n * Raster operation type. Supported values are `'pixel'` and `'image'`.\n */\n\n/**\n * @typedef {import(\"./Image.js\").ImageSourceEventTypes|'beforeoperations'|'afteroperations'} RasterSourceEventTypes\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Raster~RasterSource} instances are instances of this\n * type.\n */\nexport class RasterSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../Map.js\").FrameState} frameState The frame state.\n   * @param {Object|Array<Object>} data An object made available to operations.  For \"afteroperations\" evenets\n   * this will be an array of objects if more than one thread is used.\n   */\n  constructor(type, frameState, data) {\n    super(type);\n\n    /**\n     * The raster extent.\n     * @type {import(\"../extent.js\").Extent}\n     * @api\n     */\n    this.extent = frameState.extent;\n\n    /**\n     * The pixel resolution (map units per pixel).\n     * @type {number}\n     * @api\n     */\n    this.resolution = frameState.viewState.resolution / frameState.pixelRatio;\n\n    /**\n     * An object made available to all operations.  This can be used by operations\n     * as a storage object (e.g. for calculating statistics).\n     * @type {Object}\n     * @api\n     */\n    this.data = data;\n  }\n}\n\n/**\n * @typedef {Object} Options\n * @property {Array<import(\"./Source.js\").default|import(\"../layer/Layer.js\").default>} sources Input\n * sources or layers.  For vector data, use an VectorImage layer.\n * @property {Operation} [operation] Raster operation.\n * The operation will be called with data from input sources\n * and the output will be assigned to the raster source.\n * @property {Object} [lib] Functions that will be made available to operations run in a worker.\n * @property {number} [threads] By default, operations will be run in a single worker thread.\n * To avoid using workers altogether, set `threads: 0`.  For pixel operations, operations can\n * be run in multiple worker threads.  Note that there is additional overhead in\n * transferring data to multiple workers, and that depending on the user's\n * system, it may not be possible to parallelize the work.\n * @property {RasterOperationType} [operationType='pixel'] Operation type.\n * Supported values are `'pixel'` and `'image'`.  By default,\n * `'pixel'` operations are assumed, and operations will be called with an\n * array of pixels from input sources.  If set to `'image'`, operations will\n * be called with an array of ImageData objects from input sources.\n * @property {Array<number>|null} [resolutions] Resolutions. If specified, raster operations will only\n * be run at the given resolutions.  By default, the resolutions of the first source with resolutions\n * specified will be used, if any. Set to `null` to use any view resolution instead.\n */\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./Image.js\").ImageSourceEventTypes, import(\"./Image.js\").ImageSourceEvent, Return> &\n *   import(\"../Observable\").OnSignature<RasterSourceEventTypes, RasterSourceEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types\n *     |RasterSourceEventTypes, Return>} RasterSourceOnSignature\n */\n\n/**\n * @classdesc\n * A source that transforms data from any number of input sources using an\n * {@link module:ol/source/Raster~Operation} function to transform input pixel values into\n * output pixel values.\n *\n * @fires module:ol/source/Raster.RasterSourceEvent\n * @api\n */\nclass RasterSource extends ImageSource {\n  /**\n   * @param {Options} options Options.\n   */\n  constructor(options) {\n    super({\n      projection: null,\n    });\n\n    /***\n     * @type {RasterSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {RasterSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {RasterSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {Processor}\n     */\n    this.processor_ = null;\n\n    /**\n     * @private\n     * @type {RasterOperationType}\n     */\n    this.operationType_ =\n      options.operationType !== undefined ? options.operationType : 'pixel';\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.threads_ = options.threads !== undefined ? options.threads : 1;\n\n    /**\n     * @private\n     * @type {Array<import(\"../layer/Layer.js\").default>}\n     */\n    this.layers_ = createLayers(options.sources);\n\n    const changed = this.changed.bind(this);\n    for (let i = 0, ii = this.layers_.length; i < ii; ++i) {\n      this.layers_[i].addEventListener(EventType.CHANGE, changed);\n    }\n\n    /** @type {boolean} */\n    this.useResolutions_ = options.resolutions !== null;\n\n    /**\n     * @private\n     * @type {import(\"../TileQueue.js\").default}\n     */\n    this.tileQueue_ = new TileQueue(function () {\n      return 1;\n    }, this.processSources_.bind(this));\n\n    /**\n     * The most recently requested frame state.\n     * @type {import(\"../Map.js\").FrameState}\n     * @private\n     */\n    this.requestedFrameState_;\n\n    /**\n     * The most recently rendered image canvas.\n     * @type {import(\"../ImageCanvas.js\").default}\n     * @private\n     */\n    this.renderedImageCanvas_ = null;\n\n    /**\n     * The most recently rendered revision.\n     * @type {number}\n     */\n    this.renderedRevision_;\n\n    /**\n     * @private\n     * @type {import(\"../Map.js\").FrameState}\n     */\n    this.frameState_ = {\n      animate: false,\n      coordinateToPixelTransform: createTransform(),\n      declutterTree: null,\n      extent: null,\n      index: 0,\n      layerIndex: 0,\n      layerStatesArray: getLayerStatesArray(this.layers_),\n      pixelRatio: 1,\n      pixelToCoordinateTransform: createTransform(),\n      postRenderFunctions: [],\n      size: [0, 0],\n      tileQueue: this.tileQueue_,\n      time: Date.now(),\n      usedTiles: {},\n      viewState: /** @type {import(\"../View.js\").State} */ ({\n        rotation: 0,\n      }),\n      viewHints: [],\n      wantedTiles: {},\n      mapId: getUid(this),\n      renderTargets: {},\n    };\n\n    this.setAttributions(function (frameState) {\n      const attributions = [];\n      for (\n        let index = 0, iMax = options.sources.length;\n        index < iMax;\n        ++index\n      ) {\n        const sourceOrLayer = options.sources[index];\n        const source =\n          sourceOrLayer instanceof Source\n            ? sourceOrLayer\n            : sourceOrLayer.getSource();\n        if (!source) {\n          continue;\n        }\n        const attributionGetter = source.getAttributions();\n        if (typeof attributionGetter === 'function') {\n          const sourceAttribution = attributionGetter(frameState);\n          attributions.push.apply(attributions, sourceAttribution);\n        }\n      }\n      return attributions.length !== 0 ? attributions : null;\n    });\n\n    if (options.operation !== undefined) {\n      this.setOperation(options.operation, options.lib);\n    }\n  }\n\n  /**\n   * Set the operation.\n   * @param {Operation} operation New operation.\n   * @param {Object} [lib] Functions that will be available to operations run\n   *     in a worker.\n   * @api\n   */\n  setOperation(operation, lib) {\n    if (this.processor_) {\n      this.processor_.dispose();\n    }\n\n    this.processor_ = new Processor({\n      operation: operation,\n      imageOps: this.operationType_ === 'image',\n      queue: 1,\n      lib: lib,\n      threads: this.threads_,\n    });\n    this.changed();\n  }\n\n  /**\n   * Update the stored frame state.\n   * @param {import(\"../extent.js\").Extent} extent The view extent (in map units).\n   * @param {number} resolution The view resolution.\n   * @param {import(\"../proj/Projection.js\").default} projection The view projection.\n   * @return {import(\"../Map.js\").FrameState} The updated frame state.\n   * @private\n   */\n  updateFrameState_(extent, resolution, projection) {\n    const frameState = /** @type {import(\"../Map.js\").FrameState} */ (\n      Object.assign({}, this.frameState_)\n    );\n\n    frameState.viewState = /** @type {import(\"../View.js\").State} */ (\n      Object.assign({}, frameState.viewState)\n    );\n\n    const center = getCenter(extent);\n\n    frameState.size[0] = Math.ceil(getWidth(extent) / resolution);\n    frameState.size[1] = Math.ceil(getHeight(extent) / resolution);\n    frameState.extent = [\n      center[0] - (frameState.size[0] * resolution) / 2,\n      center[1] - (frameState.size[1] * resolution) / 2,\n      center[0] + (frameState.size[0] * resolution) / 2,\n      center[1] + (frameState.size[1] * resolution) / 2,\n    ];\n    frameState.time = Date.now();\n\n    const viewState = frameState.viewState;\n    viewState.center = center;\n    viewState.projection = projection;\n    viewState.resolution = resolution;\n    return frameState;\n  }\n\n  /**\n   * Determine if all sources are ready.\n   * @return {boolean} All sources are ready.\n   * @private\n   */\n  allSourcesReady_() {\n    let ready = true;\n    let source;\n    for (let i = 0, ii = this.layers_.length; i < ii; ++i) {\n      source = this.layers_[i].getSource();\n      if (!source || source.getState() !== 'ready') {\n        ready = false;\n        break;\n      }\n    }\n    return ready;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../ImageCanvas.js\").default} Single image.\n   */\n  getImage(extent, resolution, pixelRatio, projection) {\n    if (!this.allSourcesReady_()) {\n      return null;\n    }\n\n    this.tileQueue_.loadMoreTiles(16, 16);\n\n    resolution = this.findNearestResolution(resolution);\n    const frameState = this.updateFrameState_(extent, resolution, projection);\n    this.requestedFrameState_ = frameState;\n\n    // check if we can't reuse the existing ol/ImageCanvas\n    if (this.renderedImageCanvas_) {\n      const renderedResolution = this.renderedImageCanvas_.getResolution();\n      const renderedExtent = this.renderedImageCanvas_.getExtent();\n      if (\n        resolution !== renderedResolution ||\n        !equals(frameState.extent, renderedExtent)\n      ) {\n        this.renderedImageCanvas_ = null;\n      }\n    }\n\n    if (\n      !this.renderedImageCanvas_ ||\n      this.getRevision() !== this.renderedRevision_\n    ) {\n      this.processSources_();\n    }\n\n    if (frameState.animate) {\n      requestAnimationFrame(this.changed.bind(this));\n    }\n\n    return this.renderedImageCanvas_;\n  }\n\n  /**\n   * Start processing source data.\n   * @private\n   */\n  processSources_() {\n    const frameState = this.requestedFrameState_;\n    const len = this.layers_.length;\n    const imageDatas = new Array(len);\n    for (let i = 0; i < len; ++i) {\n      frameState.layerIndex = i;\n      frameState.renderTargets = {};\n      const imageData = getImageData(this.layers_[i], frameState);\n      if (imageData) {\n        imageDatas[i] = imageData;\n      } else {\n        return;\n      }\n    }\n\n    const data = {};\n    this.dispatchEvent(\n      new RasterSourceEvent(RasterEventType.BEFOREOPERATIONS, frameState, data)\n    );\n    this.processor_.process(\n      imageDatas,\n      data,\n      this.onWorkerComplete_.bind(this, frameState)\n    );\n  }\n\n  /**\n   * Called when pixel processing is complete.\n   * @param {import(\"../Map.js\").FrameState} frameState The frame state.\n   * @param {Error} err Any error during processing.\n   * @param {ImageData} output The output image data.\n   * @param {Object|Array<Object>} data The user data (or an array if more than one thread).\n   * @private\n   */\n  onWorkerComplete_(frameState, err, output, data) {\n    if (err || !output) {\n      return;\n    }\n\n    // do nothing if extent or resolution changed\n    const extent = frameState.extent;\n    const resolution = frameState.viewState.resolution;\n    if (\n      resolution !== this.requestedFrameState_.viewState.resolution ||\n      !equals(extent, this.requestedFrameState_.extent)\n    ) {\n      return;\n    }\n\n    let context;\n    if (this.renderedImageCanvas_) {\n      context = this.renderedImageCanvas_.getImage().getContext('2d');\n    } else {\n      const width = Math.round(getWidth(extent) / resolution);\n      const height = Math.round(getHeight(extent) / resolution);\n      context = createCanvasContext2D(width, height);\n      this.renderedImageCanvas_ = new ImageCanvas(\n        extent,\n        resolution,\n        1,\n        context.canvas\n      );\n    }\n    context.putImageData(output, 0, 0);\n\n    if (frameState.animate) {\n      requestAnimationFrame(this.changed.bind(this));\n    } else {\n      this.changed();\n    }\n    this.renderedRevision_ = this.getRevision();\n\n    this.dispatchEvent(\n      new RasterSourceEvent(RasterEventType.AFTEROPERATIONS, frameState, data)\n    );\n  }\n\n  /**\n   * @param {import(\"../proj/Projection\").default} [projection] Projection.\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions(projection) {\n    if (!this.useResolutions_) {\n      return null;\n    }\n    let resolutions = super.getResolutions();\n    if (!resolutions) {\n      for (let i = 0, ii = this.layers_.length; i < ii; ++i) {\n        const source = this.layers_[i].getSource();\n        resolutions = source.getResolutions(projection);\n        if (resolutions) {\n          break;\n        }\n      }\n    }\n    return resolutions;\n  }\n\n  disposeInternal() {\n    if (this.processor_) {\n      this.processor_.dispose();\n    }\n    super.disposeInternal();\n  }\n}\n\n/**\n * Clean up and unregister the worker.\n * @function\n * @api\n */\nRasterSource.prototype.dispose;\n\n/**\n * A reusable canvas context.\n * @type {CanvasRenderingContext2D}\n * @private\n */\nlet sharedContext = null;\n\n/**\n * Get image data from a layer.\n * @param {import(\"../layer/Layer.js\").default} layer Layer to render.\n * @param {import(\"../Map.js\").FrameState} frameState The frame state.\n * @return {ImageData} The image data.\n */\nfunction getImageData(layer, frameState) {\n  const renderer = layer.getRenderer();\n  if (!renderer) {\n    throw new Error('Unsupported layer type: ' + layer);\n  }\n\n  if (!renderer.prepareFrame(frameState)) {\n    return null;\n  }\n  const width = frameState.size[0];\n  const height = frameState.size[1];\n  if (width === 0 || height === 0) {\n    return null;\n  }\n  const container = renderer.renderFrame(frameState, null);\n  let element;\n  if (container instanceof HTMLCanvasElement) {\n    element = container;\n  } else {\n    if (container) {\n      element = container.firstElementChild;\n    }\n    if (!(element instanceof HTMLCanvasElement)) {\n      throw new Error('Unsupported rendered element: ' + element);\n    }\n    if (element.width === width && element.height === height) {\n      const context = element.getContext('2d');\n      return context.getImageData(0, 0, width, height);\n    }\n  }\n\n  if (!sharedContext) {\n    sharedContext = createCanvasContext2D(width, height, undefined, {\n      willReadFrequently: true,\n    });\n  } else {\n    const canvas = sharedContext.canvas;\n    if (canvas.width !== width || canvas.height !== height) {\n      sharedContext = createCanvasContext2D(width, height, undefined, {\n        willReadFrequently: true,\n      });\n    } else {\n      sharedContext.clearRect(0, 0, width, height);\n    }\n  }\n  sharedContext.drawImage(element, 0, 0, width, height);\n  return sharedContext.getImageData(0, 0, width, height);\n}\n\n/**\n * Get a list of layer states from a list of layers.\n * @param {Array<import(\"../layer/Layer.js\").default>} layers Layers.\n * @return {Array<import(\"../layer/Layer.js\").State>} The layer states.\n */\nfunction getLayerStatesArray(layers) {\n  return layers.map(function (layer) {\n    return layer.getLayerState();\n  });\n}\n\n/**\n * Create layers for all sources.\n * @param {Array<import(\"./Source.js\").default|import(\"../layer/Layer.js\").default>} sources The sources.\n * @return {Array<import(\"../layer/Layer.js\").default>} Array of layers.\n */\nfunction createLayers(sources) {\n  const len = sources.length;\n  const layers = new Array(len);\n  for (let i = 0; i < len; ++i) {\n    layers[i] = createLayer(sources[i]);\n  }\n  return layers;\n}\n\n/**\n * Create a layer for the provided source.\n * @param {import(\"./Source.js\").default|import(\"../layer/Layer.js\").default} layerOrSource The layer or source.\n * @return {import(\"../layer/Layer.js\").default} The layer.\n */\nfunction createLayer(layerOrSource) {\n  // @type {import(\"../layer/Layer.js\").default}\n  let layer;\n  if (layerOrSource instanceof Source) {\n    if (layerOrSource instanceof TileSource) {\n      layer = new TileLayer({source: layerOrSource});\n    } else if (layerOrSource instanceof ImageSource) {\n      layer = new ImageLayer({source: layerOrSource});\n    }\n  } else {\n    layer = layerOrSource;\n  }\n  return layer;\n}\n\nexport default RasterSource;\n", "/**\n * @module ol/net\n */\nimport {getUid} from './util.js';\n\n/**\n * Simple JSONP helper. Supports error callbacks and a custom callback param.\n * The error callback will be called when no JSONP is executed after 10 seconds.\n *\n * @param {string} url Request url. A 'callback' query parameter will be\n *     appended.\n * @param {Function} callback Callback on success.\n * @param {Function} [errback] Callback on error.\n * @param {string} [callbackParam] Custom query parameter for the JSONP\n *     callback. Default is 'callback'.\n */\nexport function jsonp(url, callback, errback, callbackParam) {\n  const script = document.createElement('script');\n  const key = 'olc_' + getUid(callback);\n  function cleanup() {\n    delete window[key];\n    script.parentNode.removeChild(script);\n  }\n  script.async = true;\n  script.src =\n    url +\n    (url.includes('?') ? '&' : '?') +\n    (callbackParam || 'callback') +\n    '=' +\n    key;\n  const timer = setTimeout(function () {\n    cleanup();\n    if (errback) {\n      errback();\n    }\n  }, 10000);\n  window[key] = function (data) {\n    clearTimeout(timer);\n    cleanup();\n    callback(data);\n  };\n  document.head.appendChild(script);\n}\n\nexport class ResponseError extends Error {\n  /**\n   * @param {XMLHttpRequest} response The XHR object.\n   */\n  constructor(response) {\n    const message = 'Unexpected response status: ' + response.status;\n    super(message);\n\n    /**\n     * @type {string}\n     */\n    this.name = 'ResponseError';\n\n    /**\n     * @type {XMLHttpRequest}\n     */\n    this.response = response;\n  }\n}\n\nexport class ClientError extends Error {\n  /**\n   * @param {XMLHttpRequest} client The XHR object.\n   */\n  constructor(client) {\n    super('Failed to issue request');\n\n    /**\n     * @type {string}\n     */\n    this.name = 'ClientError';\n\n    /**\n     * @type {XMLHttpRequest}\n     */\n    this.client = client;\n  }\n}\n\n/**\n * @param {string} url The URL.\n * @return {Promise<Object>} A promise that resolves to the JSON response.\n */\nexport function getJSON(url) {\n  return new Promise(function (resolve, reject) {\n    /**\n     * @param {ProgressEvent<XMLHttpRequest>} event The load event.\n     */\n    function onLoad(event) {\n      const client = event.target;\n      // status will be 0 for file:// urls\n      if (!client.status || (client.status >= 200 && client.status < 300)) {\n        let data;\n        try {\n          data = JSON.parse(client.responseText);\n        } catch (err) {\n          const message = 'Error parsing response text as JSON: ' + err.message;\n          reject(new Error(message));\n          return;\n        }\n        resolve(data);\n        return;\n      }\n\n      reject(new ResponseError(client));\n    }\n\n    /**\n     * @param {ProgressEvent<XMLHttpRequest>} event The error event.\n     */\n    function onError(event) {\n      reject(new ClientError(event.target));\n    }\n\n    const client = new XMLHttpRequest();\n    client.addEventListener('load', onLoad);\n    client.addEventListener('error', onError);\n    client.open('GET', url);\n    client.setRequestHeader('Accept', 'application/json');\n    client.send();\n  });\n}\n\n/**\n * @param {string} base The base URL.\n * @param {string} url The potentially relative URL.\n * @return {string} The full URL.\n */\nexport function resolveUrl(base, url) {\n  if (url.includes('://')) {\n    return url;\n  }\n  return new URL(url, base).href;\n}\n\nlet originalXHR;\nexport function overrideXHR(xhr) {\n  if (typeof XMLHttpRequest !== 'undefined') {\n    originalXHR = XMLHttpRequest;\n  }\n  global.XMLHttpRequest = xhr;\n}\n\nexport function restoreXHR() {\n  global.XMLHttpRequest = originalXHR;\n}\n", "/**\n * @module ol/source/TileJSON\n */\n// FIXME check order of async callbacks\n\n/**\n * See https://mapbox.com/developers/api/.\n */\n\nimport TileImage from './TileImage.js';\nimport {applyTransform, intersects} from '../extent.js';\nimport {assert} from '../asserts.js';\nimport {createFromTemplates} from '../tileurlfunction.js';\nimport {createXYZ, extentFromProjection} from '../tilegrid.js';\nimport {get as getProjection, getTransformFromProjections} from '../proj.js';\nimport {jsonp as requestJSONP} from '../net.js';\n\n/**\n * @typedef {Object} Config\n * @property {string} [name] The name.\n * @property {string} [description] The description.\n * @property {string} [version] The version.\n * @property {string} [attribution] The attribution.\n * @property {string} [template] The template.\n * @property {string} [legend] The legend.\n * @property {string} [scheme] The scheme.\n * @property {Array<string>} tiles The tile URL templates.\n * @property {Array<string>} [grids] Optional grids.\n * @property {number} [minzoom] Minimum zoom level.\n * @property {number} [maxzoom] Maximum zoom level.\n * @property {Array<number>} [bounds] Optional bounds.\n * @property {Array<number>} [center] Optional center.\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least the number of tiles in the viewport.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {boolean} [jsonp=false] Use JSONP with callback to load the TileJSON.\n * Useful when the server does not support CORS..\n * @property {number} [reprojectionErrorThreshold=0.5] Maximum allowed reprojection error (in pixels).\n * Higher values can increase reprojection performance, but decrease precision.\n * @property {Config} [tileJSON] TileJSON configuration for this source.\n * If not provided, `url` must be configured.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction] Optional function to load a tile given a URL. The default is\n * ```js\n * function(imageTile, src) {\n *   imageTile.getImage().src = src;\n * };\n * ```\n * @property {number|import(\"../size.js\").Size} [tileSize=[256, 256]] The tile size used by the tile service.\n * Note: `tileSize` and other non-standard TileJSON properties are currently ignored.\n * @property {string} [url] URL to the TileJSON file. If not provided, `tileJSON` must be configured.\n * @property {boolean} [wrapX=true] Whether to wrap the world horizontally.\n * @property {number} [transition] Duration of the opacity transition for rendering.\n * To disable the opacity transition, pass `transition: 0`.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Layer source for tile data in TileJSON format.\n * @api\n */\nclass TileJSON extends TileImage {\n  /**\n   * @param {Options} options TileJSON options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      cacheSize: options.cacheSize,\n      crossOrigin: options.crossOrigin,\n      interpolate: options.interpolate,\n      projection: getProjection('EPSG:3857'),\n      reprojectionErrorThreshold: options.reprojectionErrorThreshold,\n      state: 'loading',\n      tileLoadFunction: options.tileLoadFunction,\n      wrapX: options.wrapX !== undefined ? options.wrapX : true,\n      transition: options.transition,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @type {Config}\n     * @private\n     */\n    this.tileJSON_ = null;\n\n    /**\n     * @type {number|import(\"../size.js\").Size}\n     * @private\n     */\n    this.tileSize_ = options.tileSize;\n\n    if (options.url) {\n      if (options.jsonp) {\n        requestJSONP(\n          options.url,\n          this.handleTileJSONResponse.bind(this),\n          this.handleTileJSONError.bind(this)\n        );\n      } else {\n        const client = new XMLHttpRequest();\n        client.addEventListener('load', this.onXHRLoad_.bind(this));\n        client.addEventListener('error', this.onXHRError_.bind(this));\n        client.open('GET', options.url);\n        client.send();\n      }\n    } else if (options.tileJSON) {\n      this.handleTileJSONResponse(options.tileJSON);\n    } else {\n      assert(false, 51); // Either `url` or `tileJSON` options must be provided\n    }\n  }\n\n  /**\n   * @private\n   * @param {Event} event The load event.\n   */\n  onXHRLoad_(event) {\n    const client = /** @type {XMLHttpRequest} */ (event.target);\n    // status will be 0 for file:// urls\n    if (!client.status || (client.status >= 200 && client.status < 300)) {\n      let response;\n      try {\n        response = /** @type {Config} */ (JSON.parse(client.responseText));\n      } catch (err) {\n        this.handleTileJSONError();\n        return;\n      }\n      this.handleTileJSONResponse(response);\n    } else {\n      this.handleTileJSONError();\n    }\n  }\n\n  /**\n   * @private\n   * @param {Event} event The error event.\n   */\n  onXHRError_(event) {\n    this.handleTileJSONError();\n  }\n\n  /**\n   * @return {Config} The tilejson object.\n   * @api\n   */\n  getTileJSON() {\n    return this.tileJSON_;\n  }\n\n  /**\n   * @protected\n   * @param {Config} tileJSON Tile JSON.\n   */\n  handleTileJSONResponse(tileJSON) {\n    const epsg4326Projection = getProjection('EPSG:4326');\n\n    const sourceProjection = this.getProjection();\n    let extent;\n    if (tileJSON['bounds'] !== undefined) {\n      const transform = getTransformFromProjections(\n        epsg4326Projection,\n        sourceProjection\n      );\n      extent = applyTransform(tileJSON['bounds'], transform);\n    }\n\n    const gridExtent = extentFromProjection(sourceProjection);\n    const minZoom = tileJSON['minzoom'] || 0;\n    const maxZoom = tileJSON['maxzoom'] || 22;\n    const tileGrid = createXYZ({\n      extent: gridExtent,\n      maxZoom: maxZoom,\n      minZoom: minZoom,\n      tileSize: this.tileSize_,\n    });\n    this.tileGrid = tileGrid;\n\n    this.tileUrlFunction = createFromTemplates(tileJSON['tiles'], tileGrid);\n\n    if (tileJSON['attribution'] && !this.getAttributions()) {\n      const attributionExtent = extent !== undefined ? extent : gridExtent;\n      this.setAttributions(function (frameState) {\n        if (intersects(attributionExtent, frameState.extent)) {\n          return [tileJSON['attribution']];\n        }\n        return null;\n      });\n    }\n    this.tileJSON_ = tileJSON;\n    this.setState('ready');\n  }\n\n  /**\n   * @protected\n   */\n  handleTileJSONError() {\n    this.setState('error');\n  }\n}\n\nexport default TileJSON;\n", "/**\n * @module ol/DataTile\n */\nimport Tile from './Tile.js';\nimport TileState from './TileState.js';\nimport {createCanvasContext2D} from './dom.js';\n\n/**\n * @typedef {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement} ImageLike\n */\n\n/**\n * @typedef {Uint8Array|Uint8ClampedArray|Float32Array|DataView} ArrayLike\n */\n\n/**\n * Data that can be used with a DataTile.\n * @typedef {ArrayLike|ImageLike} Data\n */\n\n/**\n * @param {Data} data Tile data.\n * @return {ImageLike|null} The image-like data.\n */\nexport function asImageLike(data) {\n  return data instanceof Image ||\n    data instanceof HTMLCanvasElement ||\n    data instanceof HTMLVideoElement\n    ? data\n    : null;\n}\n\n/**\n * @param {Data} data Tile data.\n * @return {ArrayLike|null} The array-like data.\n */\nexport function asArrayLike(data) {\n  return data instanceof Uint8Array ||\n    data instanceof Uint8ClampedArray ||\n    data instanceof Float32Array ||\n    data instanceof DataView\n    ? data\n    : null;\n}\n\n/**\n * @type {CanvasRenderingContext2D|null}\n */\nlet sharedContext = null;\n\n/**\n * @param {ImageLike} image The image.\n * @return {Uint8ClampedArray} The data.\n */\nexport function toArray(image) {\n  if (!sharedContext) {\n    sharedContext = createCanvasContext2D(\n      image.width,\n      image.height,\n      undefined,\n      {willReadFrequently: true}\n    );\n  }\n  const canvas = sharedContext.canvas;\n  const width = image.width;\n  if (canvas.width !== width) {\n    canvas.width = width;\n  }\n  const height = image.height;\n  if (canvas.height !== height) {\n    canvas.height = height;\n  }\n  sharedContext.clearRect(0, 0, width, height);\n  sharedContext.drawImage(image, 0, 0);\n  return sharedContext.getImageData(0, 0, width, height).data;\n}\n\n/**\n * @type {import('./size.js').Size}\n */\nconst defaultSize = [256, 256];\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n * @property {function(): Promise<Data>} loader Data loader.  For loaders that generate images,\n * the promise should not resolve until the image is loaded.\n * @property {number} [transition=250] A duration for tile opacity\n * transitions in milliseconds. A duration of 0 disables the opacity transition.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n * @property {import('./size.js').Size} [size=[256, 256]] Tile size.\n * @api\n */\n\nclass DataTile extends Tile {\n  /**\n   * @param {Options} options Tile options.\n   */\n  constructor(options) {\n    const state = TileState.IDLE;\n\n    super(options.tileCoord, state, {\n      transition: options.transition,\n      interpolate: options.interpolate,\n    });\n\n    /**\n     * @type {function(): Promise<Data>}\n     * @private\n     */\n    this.loader_ = options.loader;\n\n    /**\n     * @type {Data}\n     * @private\n     */\n    this.data_ = null;\n\n    /**\n     * @type {Error}\n     * @private\n     */\n    this.error_ = null;\n\n    /**\n     * @type {import('./size.js').Size|null}\n     * @private\n     */\n    this.size_ = options.size || null;\n  }\n\n  /**\n   * Get the tile size.\n   * @return {import('./size.js').Size} Tile size.\n   */\n  getSize() {\n    if (this.size_) {\n      return this.size_;\n    }\n    const imageData = asImageLike(this.data_);\n    if (imageData) {\n      return [imageData.width, imageData.height];\n    }\n    return defaultSize;\n  }\n\n  /**\n   * Get the data for the tile.\n   * @return {Data} Tile data.\n   * @api\n   */\n  getData() {\n    return this.data_;\n  }\n\n  /**\n   * Get any loading error.\n   * @return {Error} Loading error.\n   * @api\n   */\n  getError() {\n    return this.error_;\n  }\n\n  /**\n   * Load not yet loaded URI.\n   * @api\n   */\n  load() {\n    if (this.state !== TileState.IDLE && this.state !== TileState.ERROR) {\n      return;\n    }\n    this.state = TileState.LOADING;\n    this.changed();\n\n    const self = this;\n    this.loader_()\n      .then(function (data) {\n        self.data_ = data;\n        self.state = TileState.LOADED;\n        self.changed();\n      })\n      .catch(function (error) {\n        self.error_ = error;\n        self.state = TileState.ERROR;\n        self.changed();\n      });\n  }\n}\n\nexport default DataTile;\n", "/**\n * @module ol/reproj/DataTile\n */\nimport {ERROR_THRESHOLD} from './common.js';\n\nimport DataTile, {asArrayLike, asImageLike, toArray} from '../DataTile.js';\nimport EventType from '../events/EventType.js';\nimport TileState from '../TileState.js';\nimport Triangulation from './Triangulation.js';\nimport {\n  calculateSourceExtentResolution,\n  canvasPool,\n  render as renderReprojected,\n} from '../reproj.js';\nimport {clamp} from '../math.js';\nimport {createCanvasContext2D, releaseCanvas} from '../dom.js';\nimport {getArea, getIntersection} from '../extent.js';\nimport {listen, unlistenByKey} from '../events.js';\n\n/**\n * @typedef {function(number, number, number, number) : import(\"../DataTile.js\").default} TileGetter\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../proj/Projection.js\").default} sourceProj Source projection.\n * @property {import(\"../tilegrid/TileGrid.js\").default} sourceTileGrid Source tile grid.\n * @property {import(\"../proj/Projection.js\").default} targetProj Target projection.\n * @property {import(\"../tilegrid/TileGrid.js\").default} targetTileGrid Target tile grid.\n * @property {import(\"../tilecoord.js\").TileCoord} tileCoord Coordinate of the tile.\n * @property {import(\"../tilecoord.js\").TileCoord} [wrappedTileCoord] Coordinate of the tile wrapped in X.\n * @property {number} pixelRatio Pixel ratio.\n * @property {number} gutter Gutter of the source tiles.\n * @property {TileGetter} getTileFunction Function returning source tiles (z, x, y, pixelRatio).\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n * @property {number} [errorThreshold] Acceptable reprojection error (in px).\n * @property {number} [transition=250] A duration for tile opacity\n * transitions in milliseconds. A duration of 0 disables the opacity transition.\n */\n\n/**\n * @classdesc\n * Class encapsulating single reprojected data tile.\n * See {@link module:ol/source/DataTile~DataTileSource}.\n *\n */\nclass ReprojDataTile extends DataTile {\n  /**\n   * @param {Options} options Tile options.\n   */\n  constructor(options) {\n    super({\n      tileCoord: options.tileCoord,\n      loader: () => Promise.resolve(new Uint8Array(4)),\n      interpolate: options.interpolate,\n      transition: options.transition,\n    });\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = options.pixelRatio;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.gutter_ = options.gutter;\n\n    /**\n     * @type {import(\"../DataTile.js\").Data}\n     * @private\n     */\n    this.reprojData_ = null;\n\n    /**\n     * @type {Error}\n     * @private\n     */\n    this.reprojError_ = null;\n\n    /**\n     * @type {import('../size.js').Size}\n     * @private\n     */\n    this.reprojSize_ = undefined;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.sourceTileGrid_ = options.sourceTileGrid;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.targetTileGrid_ = options.targetTileGrid;\n\n    /**\n     * @private\n     * @type {import(\"../tilecoord.js\").TileCoord}\n     */\n    this.wrappedTileCoord_ = options.wrappedTileCoord || options.tileCoord;\n\n    /**\n     * @private\n     * @type {!Array<DataTile>}\n     */\n    this.sourceTiles_ = [];\n\n    /**\n     * @private\n     * @type {?Array<import(\"../events.js\").EventsKey>}\n     */\n    this.sourcesListenerKeys_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.sourceZ_ = 0;\n\n    const targetExtent = this.targetTileGrid_.getTileCoordExtent(\n      this.wrappedTileCoord_\n    );\n    const maxTargetExtent = this.targetTileGrid_.getExtent();\n    let maxSourceExtent = this.sourceTileGrid_.getExtent();\n\n    const limitedTargetExtent = maxTargetExtent\n      ? getIntersection(targetExtent, maxTargetExtent)\n      : targetExtent;\n\n    if (getArea(limitedTargetExtent) === 0) {\n      // Tile is completely outside range -> EMPTY\n      // TODO: is it actually correct that the source even creates the tile ?\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    const sourceProj = options.sourceProj;\n    const sourceProjExtent = sourceProj.getExtent();\n    if (sourceProjExtent) {\n      if (!maxSourceExtent) {\n        maxSourceExtent = sourceProjExtent;\n      } else {\n        maxSourceExtent = getIntersection(maxSourceExtent, sourceProjExtent);\n      }\n    }\n\n    const targetResolution = this.targetTileGrid_.getResolution(\n      this.wrappedTileCoord_[0]\n    );\n\n    const targetProj = options.targetProj;\n    const sourceResolution = calculateSourceExtentResolution(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      targetResolution\n    );\n\n    if (!isFinite(sourceResolution) || sourceResolution <= 0) {\n      // invalid sourceResolution -> EMPTY\n      // probably edges of the projections when no extent is defined\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    const errorThresholdInPixels =\n      options.errorThreshold !== undefined\n        ? options.errorThreshold\n        : ERROR_THRESHOLD;\n\n    /**\n     * @private\n     * @type {!import(\"./Triangulation.js\").default}\n     */\n    this.triangulation_ = new Triangulation(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      maxSourceExtent,\n      sourceResolution * errorThresholdInPixels,\n      targetResolution\n    );\n\n    if (this.triangulation_.getTriangles().length === 0) {\n      // no valid triangles -> EMPTY\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    this.sourceZ_ = this.sourceTileGrid_.getZForResolution(sourceResolution);\n    let sourceExtent = this.triangulation_.calculateSourceExtent();\n\n    if (maxSourceExtent) {\n      if (sourceProj.canWrapX()) {\n        sourceExtent[1] = clamp(\n          sourceExtent[1],\n          maxSourceExtent[1],\n          maxSourceExtent[3]\n        );\n        sourceExtent[3] = clamp(\n          sourceExtent[3],\n          maxSourceExtent[1],\n          maxSourceExtent[3]\n        );\n      } else {\n        sourceExtent = getIntersection(sourceExtent, maxSourceExtent);\n      }\n    }\n\n    if (!getArea(sourceExtent)) {\n      this.state = TileState.EMPTY;\n    } else {\n      const sourceRange = this.sourceTileGrid_.getTileRangeForExtentAndZ(\n        sourceExtent,\n        this.sourceZ_\n      );\n      const getTile = options.getTileFunction;\n      for (let srcX = sourceRange.minX; srcX <= sourceRange.maxX; srcX++) {\n        for (let srcY = sourceRange.minY; srcY <= sourceRange.maxY; srcY++) {\n          const tile = getTile(this.sourceZ_, srcX, srcY, this.pixelRatio_);\n          if (tile) {\n            this.sourceTiles_.push(tile);\n          }\n        }\n      }\n\n      if (this.sourceTiles_.length === 0) {\n        this.state = TileState.EMPTY;\n      }\n    }\n  }\n\n  /**\n   * Get the tile size.\n   * @return {import('../size.js').Size} Tile size.\n   */\n  getSize() {\n    return this.reprojSize_;\n  }\n\n  /**\n   * Get the data for the tile.\n   * @return {import(\"../DataTile.js\").Data} Tile data.\n   */\n  getData() {\n    return this.reprojData_;\n  }\n\n  /**\n   * Get any loading error.\n   * @return {Error} Loading error.\n   */\n  getError() {\n    return this.reprojError_;\n  }\n\n  /**\n   * @private\n   */\n  reproject_() {\n    const dataSources = [];\n    this.sourceTiles_.forEach((tile) => {\n      if (!tile || tile.getState() !== TileState.LOADED) {\n        return;\n      }\n      const size = tile.getSize();\n      const gutter = this.gutter_;\n      /**\n       * @type {import(\"../DataTile.js\").ArrayLike}\n       */\n      let tileData;\n      const arrayData = asArrayLike(tile.getData());\n      if (arrayData) {\n        tileData = arrayData;\n      } else {\n        tileData = toArray(asImageLike(tile.getData()));\n      }\n      const pixelSize = [size[0] + 2 * gutter, size[1] + 2 * gutter];\n      const isFloat = tileData instanceof Float32Array;\n      const pixelCount = pixelSize[0] * pixelSize[1];\n      const DataType = isFloat ? Float32Array : Uint8Array;\n      const tileDataR = new DataType(tileData.buffer);\n      const bytesPerElement = DataType.BYTES_PER_ELEMENT;\n      const bytesPerPixel = (bytesPerElement * tileDataR.length) / pixelCount;\n      const bytesPerRow = tileDataR.byteLength / pixelSize[1];\n      const bandCount = Math.floor(\n        bytesPerRow / bytesPerElement / pixelSize[0]\n      );\n      const packedLength = pixelCount * bandCount;\n      let packedData = tileDataR;\n      if (tileDataR.length !== packedLength) {\n        packedData = new DataType(packedLength);\n        let dataIndex = 0;\n        let rowOffset = 0;\n        const colCount = pixelSize[0] * bandCount;\n        for (let rowIndex = 0; rowIndex < pixelSize[1]; ++rowIndex) {\n          for (let colIndex = 0; colIndex < colCount; ++colIndex) {\n            packedData[dataIndex++] = tileDataR[rowOffset + colIndex];\n          }\n          rowOffset += bytesPerRow / bytesPerElement;\n        }\n      }\n      dataSources.push({\n        extent: this.sourceTileGrid_.getTileCoordExtent(tile.tileCoord),\n        data: new Uint8Array(packedData.buffer),\n        dataType: DataType,\n        bytesPerPixel: bytesPerPixel,\n        pixelSize: pixelSize,\n      });\n    });\n    this.sourceTiles_.length = 0;\n\n    if (dataSources.length === 0) {\n      this.state = TileState.ERROR;\n    } else {\n      const z = this.wrappedTileCoord_[0];\n      const size = this.targetTileGrid_.getTileSize(z);\n      const targetWidth = typeof size === 'number' ? size : size[0];\n      const targetHeight = typeof size === 'number' ? size : size[1];\n      const targetResolution = this.targetTileGrid_.getResolution(z);\n      const sourceResolution = this.sourceTileGrid_.getResolution(\n        this.sourceZ_\n      );\n\n      const targetExtent = this.targetTileGrid_.getTileCoordExtent(\n        this.wrappedTileCoord_\n      );\n\n      let dataR, dataU;\n\n      const bytesPerPixel = dataSources[0].bytesPerPixel;\n\n      const reprojs = Math.ceil(bytesPerPixel / 3);\n      for (let reproj = reprojs - 1; reproj >= 0; --reproj) {\n        const sources = [];\n        for (let i = 0, len = dataSources.length; i < len; ++i) {\n          const dataSource = dataSources[i];\n          const buffer = dataSource.data;\n          const pixelSize = dataSource.pixelSize;\n          const width = pixelSize[0];\n          const height = pixelSize[1];\n          const context = createCanvasContext2D(width, height, canvasPool);\n          const imageData = context.createImageData(width, height);\n          const data = imageData.data;\n          let offset = reproj * 3;\n          for (let j = 0, len = data.length; j < len; j += 4) {\n            data[j] = buffer[offset];\n            data[j + 1] = buffer[offset + 1];\n            data[j + 2] = buffer[offset + 2];\n            data[j + 3] = 255;\n            offset += bytesPerPixel;\n          }\n          context.putImageData(imageData, 0, 0);\n          sources.push({\n            extent: dataSource.extent,\n            image: context.canvas,\n          });\n        }\n\n        const canvas = renderReprojected(\n          targetWidth,\n          targetHeight,\n          this.pixelRatio_,\n          sourceResolution,\n          this.sourceTileGrid_.getExtent(),\n          targetResolution,\n          targetExtent,\n          this.triangulation_,\n          sources,\n          this.gutter_,\n          false,\n          false\n        );\n\n        for (let i = 0, len = sources.length; i < len; ++i) {\n          const canvas = sources[i].image;\n          const context = canvas.getContext('2d');\n          releaseCanvas(context);\n          canvasPool.push(context.canvas);\n        }\n\n        const context = canvas.getContext('2d');\n        const imageData = context.getImageData(\n          0,\n          0,\n          canvas.width,\n          canvas.height\n        );\n\n        releaseCanvas(context);\n        canvasPool.push(canvas);\n\n        if (!dataR) {\n          dataU = new Uint8Array(\n            bytesPerPixel * imageData.width * imageData.height\n          );\n          dataR = new dataSources[0].dataType(dataU.buffer);\n        }\n\n        const data = imageData.data;\n        let offset = reproj * 3;\n        for (let i = 0, len = data.length; i < len; i += 4) {\n          if (data[i + 3] === 255) {\n            dataU[offset] = data[i];\n            dataU[offset + 1] = data[i + 1];\n            dataU[offset + 2] = data[i + 2];\n          } else {\n            dataU[offset] = 0;\n            dataU[offset + 1] = 0;\n            dataU[offset + 2] = 0;\n          }\n          offset += bytesPerPixel;\n        }\n      }\n\n      this.reprojData_ = dataR;\n      this.reprojSize_ = [\n        Math.round(targetWidth * this.pixelRatio_),\n        Math.round(targetHeight * this.pixelRatio_),\n      ];\n      this.state = TileState.LOADED;\n    }\n    this.changed();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state !== TileState.IDLE && this.state !== TileState.ERROR) {\n      return;\n    }\n    this.state = TileState.LOADING;\n    this.changed();\n\n    let leftToLoad = 0;\n\n    this.sourcesListenerKeys_ = [];\n    this.sourceTiles_.forEach((tile) => {\n      const state = tile.getState();\n      if (state !== TileState.IDLE && state !== TileState.LOADING) {\n        return;\n      }\n      leftToLoad++;\n\n      const sourceListenKey = listen(\n        tile,\n        EventType.CHANGE,\n        function () {\n          const state = tile.getState();\n          if (\n            state == TileState.LOADED ||\n            state == TileState.ERROR ||\n            state == TileState.EMPTY\n          ) {\n            unlistenByKey(sourceListenKey);\n            leftToLoad--;\n            if (leftToLoad === 0) {\n              this.unlistenSources_();\n              this.reproject_();\n            }\n          }\n        },\n        this\n      );\n      this.sourcesListenerKeys_.push(sourceListenKey);\n    });\n\n    if (leftToLoad === 0) {\n      setTimeout(this.reproject_.bind(this), 0);\n    } else {\n      this.sourceTiles_.forEach(function (tile) {\n        const state = tile.getState();\n        if (state == TileState.IDLE) {\n          tile.load();\n        }\n      });\n    }\n  }\n\n  /**\n   * @private\n   */\n  unlistenSources_() {\n    this.sourcesListenerKeys_.forEach(unlistenByKey);\n    this.sourcesListenerKeys_ = null;\n  }\n}\n\nexport default ReprojDataTile;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAM,cAAN,cAA0B,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlC,YACE,YACA,YACA,cACA,kBACA,YACA,kBACA,aACA;AACA,QAAI,kBAAkB,WAAW,UAAU;AAC3C,QAAI,mBAAmB,WAAW,SAAS,GAAG;AAC5C,wBAAkB,gBAAgB,MAAM;AACxC,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AAAA,IACvB;AACA,QAAI,kBAAkB,WAAW,UAAU;AAC3C,QAAI,mBAAmB,WAAW,SAAS,GAAG;AAC5C,wBAAkB,gBAAgB,MAAM;AACxC,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AAAA,IACvB;AAEA,UAAM,sBAAsB,kBACxB,gBAAgB,cAAc,eAAe,IAC7C;AAEJ,UAAM,eAAe,UAAU,mBAAmB;AAClD,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,yBAAyB;AAE/B,UAAM,gBAAgB,IAAI;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,IACF;AAEA,UAAM,eAAe,cAAc,sBAAsB;AACzD,UAAM,cAAc,QAAQ,YAAY,IACpC,OACA,iBAAiB,cAAc,kBAAkB,UAAU;AAC/D,UAAM,QAAQ,cAAc,mBAAW,OAAO,mBAAW;AACzD,UAAM,mBAAmB,cAAc,YAAY,cAAc,IAAI;AAErE,UAAM,cAAc,kBAAkB,kBAAkB,KAAK;AAM7D,SAAK,cAAc;AAMnB,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB;AAMtB,SAAK,oBAAoB;AAMzB,SAAK,gBAAgB;AAMrB,SAAK,eAAe;AAMpB,SAAK,oBAAoB;AAMzB,SAAK,eAAe;AAMpB,SAAK,UAAU;AAMf,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,SAAS,mBAAW,SAAS;AACpC,WAAK,gBAAgB;AAAA,IACvB;AACA,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,cAAc,KAAK,aAAa,SAAS;AAC/C,QAAI,eAAe,mBAAW,QAAQ;AACpC,YAAM,QAAQ,SAAS,KAAK,aAAa,IAAI,KAAK;AAClD,YAAM,SAAS,UAAU,KAAK,aAAa,IAAI,KAAK;AAEpD,WAAK,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK,aAAa,cAAc;AAAA,QAChC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,UACE;AAAA,YACE,QAAQ,KAAK,aAAa,UAAU;AAAA,YACpC,OAAO,KAAK,aAAa,SAAS;AAAA,UACpC;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,SAAS,mBAAW,MAAM;AACjC,WAAK,QAAQ,mBAAW;AACxB,WAAK,QAAQ;AAEb,YAAM,cAAc,KAAK,aAAa,SAAS;AAC/C,UAAI,eAAe,mBAAW,UAAU,eAAe,mBAAW,OAAO;AACvE,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,qBAAqB;AAAA,UACxB,KAAK;AAAA,UACL,kBAAU;AAAA,UACV,SAAU,GAAG;AACX,kBAAMA,eAAc,KAAK,aAAa,SAAS;AAC/C,gBACEA,gBAAe,mBAAW,UAC1BA,gBAAe,mBAAW,OAC1B;AACA,mBAAK,gBAAgB;AACrB,mBAAK,WAAW;AAAA,YAClB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB;AAAA;AAAA,MACoD,KAAK;AAAA,IACzD;AACA,SAAK,qBAAqB;AAAA,EAC5B;AACF;AAEA,IAAO,gBAAQ;;;AClPR,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,gBAAgB;AAClB;AAWO,IAAM,mBAAN,cAA+B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,YAAY,MAAM,OAAO;AACvB,UAAM,IAAI;AAOV,SAAK,QAAQ;AAAA,EACf;AACF;AA8BA,IAAM,cAAN,cAA0B,eAAO;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,YAAY,QAAQ;AAAA,MACpB,OAAO,QAAQ;AAAA,MACf,aACE,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAAA,IAC9D,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,oBAAoB;AAMzB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,aAAa;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,YAAY;AAChC,UAAM,cAAc,KAAK,eAAe;AACxC,QAAI,aAAa;AACf,YAAM,MAAM,kBAAkB,aAAa,YAAY,CAAC;AACxD,mBAAa,YAAY,GAAG;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,QAAQ,YAAY,YAAY,YAAY;AACnD,UAAM,mBAAmB,KAAK,cAAc;AAC5C,QACE,CAAC,oBACD,CAAC,cACD,WAAW,kBAAkB,UAAU,GACvC;AACA,UAAI,kBAAkB;AACpB,qBAAa;AAAA,MACf;AACA,aAAO,KAAK,iBAAiB,QAAQ,YAAY,YAAY,UAAU;AAAA,IACzE;AACA,QAAI,KAAK,mBAAmB;AAC1B,UACE,KAAK,wBAAwB,KAAK,YAAY,KAC9C,WAAW,KAAK,kBAAkB,cAAc,GAAG,UAAU,KAC7D,KAAK,kBAAkB,cAAc,KAAK,cAC1C,OAAO,KAAK,kBAAkB,UAAU,GAAG,MAAM,GACjD;AACA,eAAO,KAAK;AAAA,MACd;AACA,WAAK,kBAAkB,QAAQ;AAC/B,WAAK,oBAAoB;AAAA,IAC3B;AAEA,SAAK,oBAAoB,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAACC,SAAQC,aAAYC,gBACnB,KAAK,iBAAiBF,SAAQC,aAAYC,aAAY,gBAAgB;AAAA,MACxE,KAAK,eAAe;AAAA,IACtB;AACA,SAAK,uBAAuB,KAAK,YAAY;AAE7C,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,QAAQ,YAAY,YAAY,YAAY;AAC3D,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,OAAO;AACvB,UAAM;AAAA;AAAA,MAAsD,MAAM;AAAA;AAClE,QAAI;AACJ,YAAQ,MAAM,SAAS,GAAG;AAAA,MACxB,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF;AACE;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,WAAK,cAAc,IAAI,iBAAiB,MAAM,KAAK,CAAC;AAAA,IACtD;AAAA,EACF;AACF;AAQO,SAAS,yBAAyB,OAAO,KAAK;AACF,EAAC,MAAM,SAAS,EAAG,MAAM;AAC5E;AAEA,IAAOC,iBAAQ;;;AC9Nf,IAAM,iBAAN,cAA6B,cAAM;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAC/B,UAAM,OAAO;AAAA,EACf;AACF;AAEA,IAAO,oBAAQ;;;AC5Bf,IAAM,2BAAN,cAAuCC,eAAoB;AAAA;AAAA;AAAA;AAAA,EAIzD,YAAY,YAAY;AACtB,UAAM,UAAU;AAMhB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,SAAS,KAAK,OAAO,SAAS,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,YAAY;AACvB,UAAM,aAAa,WAAW,iBAAiB,WAAW,UAAU;AACpE,UAAM,aAAa,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,iBAAiB,UAAU;AAEjC,UAAM,cAAc,KAAK,SAAS,EAAE,UAAU;AAE9C,UAAM,QAAQ,WAAW;AAEzB,QAAI,iBAAiB,WAAW;AAChC,QAAI,WAAW,WAAW,QAAW;AACnC,uBAAiB;AAAA,QACf;AAAA,QACA,eAAe,WAAW,QAAQ,UAAU,UAAU;AAAA,MACxD;AAAA,IACF;AAEA,QACE,CAAC,MAAM,iBAAS,SAAS,KACzB,CAAC,MAAM,iBAAS,WAAW,KAC3B,CAAC,QAAQ,cAAc,GACvB;AACA,UAAI,aAAa;AACf,cAAM,aAAa,UAAU;AAC7B,cAAM,QAAQ,YAAY;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,OAAO;AACT,cAAI,KAAK,UAAU,KAAK,GAAG;AACzB,iBAAK,SAAS;AAAA,UAChB,WAAW,MAAM,SAAS,MAAM,mBAAW,OAAO;AAChD,iBAAK,SAAS;AAAA,UAChB;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO;AACb,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,MAAM,MAAM;AAAA,IACd;AAEA,UAAM,cAAc,MAAM,UAAU;AACpC,QAAI,aAAa;AACf,UAAI,CAAC,mBAAmB,aAAa,UAAU,GAAG;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,cAAc,KAAK,OAAO,UAAU;AAC1C,UAAM,MAAM,KAAK,SAAS;AAE1B,UAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAM,MAAM,KAAK;AAAA,MACf,IAAI,UAAU,WAAW,CAAC,IAAI,YAAY,CAAC,KAAK;AAAA,IAClD;AACA,QAAI,MAAM,KAAK,OAAO,IAAI,OAAO;AAC/B,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,UAAU,WAAW;AAC5C,UAAM,MAAM,KAAK;AAAA,MACf,IAAI,WAAW,YAAY,CAAC,IAAI,WAAW,CAAC,KAAK;AAAA,IACnD;AACA,QAAI,MAAM,KAAK,OAAO,IAAI,QAAQ;AAChC,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,aAAa,KAAK,KAAK,GAAG;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,YAAY,QAAQ;AAC9B,UAAM,QAAQ,KAAK;AACnB,UAAM,cAAc,MAAM,UAAU;AACpC,UAAM,kBAAkB,MAAM,cAAc;AAC5C,UAAM,kBAAkB,MAAM,cAAc;AAC5C,UAAM,aAAa,WAAW,iBAAiB,WAAW,UAAU;AACpE,UAAM,aAAa,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,iBAAiB,UAAU;AACjC,UAAM,QACH,aAAa,mBAAoB,iBAAiB;AAErD,UAAM,SAAS,WAAW;AAC1B,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,UAAU;AAE3B,UAAM,QAAQ,KAAK,MAAO,SAAS,MAAM,IAAI,aAAc,UAAU;AACrE,UAAM,SAAS,KAAK,MAAO,UAAU,MAAM,IAAI,aAAc,UAAU;AAGvE;AAAA,MACE,KAAK;AAAA,MACL,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ;AAAA,MACT,CAAC,SAAS;AAAA,IACZ;AACA,gBAAY,KAAK,uBAAuB,KAAK,cAAc;AAE3D,UAAM,kBAAkB,SAAkB,KAAK,cAAc;AAE7D,SAAK,aAAa,QAAQ,iBAAiB,KAAK,cAAc,UAAU,CAAC;AAEzE,UAAMC,WAAU,KAAK;AACrB,UAAM,SAASA,SAAQ;AAEvB,QAAI,OAAO,SAAS,SAAS,OAAO,UAAU,QAAQ;AACpD,aAAO,QAAQ;AACf,aAAO,SAAS;AAAA,IAClB,WAAW,CAAC,KAAK,iBAAiB;AAChC,MAAAA,SAAQ,UAAU,GAAG,GAAG,OAAO,MAAM;AAAA,IACvC;AAGA,QAAI,UAAU;AACd,QAAIC,UAAS;AACb,QAAI,WAAW,QAAQ;AACrB,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AACA,MAAAA,UAAS,WAAiB,aAAa,WAAW,MAAM;AACxD,gBAAUA,WAAU,CAAC,eAAe,aAAa,WAAW,MAAM;AAClE,UAAI,SAAS;AACX,aAAK,cAAcD,UAAS,YAAY,WAAW;AAAA,MACrD;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,SAAS;AAE1B,UAAM,YAAY;AAAA,MAChB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACC,mBAAmB,YAAY,CAAC,IAAI,WAAW,CAAC,KAAM;AAAA,MACtD,mBAAmB,WAAW,CAAC,IAAI,YAAY,CAAC,KAAM;AAAA,IACzD;AAEA,SAAK,qBAAsB,kBAAkB,aAAc;AAE3D,UAAM,KAAK,IAAI,QAAQ,UAAU,CAAC;AAClC,UAAM,KAAK,IAAI,SAAS,UAAU,CAAC;AAEnC,QAAI,CAAC,KAAK,SAAS,EAAE,UAAU,EAAE,eAAe,GAAG;AACjD,MAAAA,SAAQ,wBAAwB;AAAA,IAClC;AAEA,SAAK,UAAUA,UAAS,UAAU;AAClC,QAAIC,WAAU,MAAM,OAAO,MAAM,KAAK;AACpC,YAAM,KAAK,UAAU,CAAC;AACtB,YAAM,KAAK,UAAU,CAAC;AACtB,YAAM,UAAU,WAAW;AAC3B,UAAI;AACJ,UAAI,YAAY,GAAG;AACjB,wBAAgBD,SAAQ;AACxB,QAAAA,SAAQ,cAAc;AAAA,MACxB;AACA,MAAAA,SAAQ,UAAU,KAAK,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,EAAE;AACpE,UAAI,YAAY,GAAG;AACjB,QAAAA,SAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,SAAK,WAAWA,UAAS,UAAU;AAEnC,QAAI,SAAS;AACX,MAAAA,SAAQ,QAAQ;AAAA,IAClB;AACA,IAAAA,SAAQ,wBAAwB;AAEhC,QAAI,oBAAoB,OAAO,MAAM,WAAW;AAC9C,aAAO,MAAM,YAAY;AAAA,IAC3B;AAEA,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,qBAAQ;;;ACvPf,IAAM,aAAN,cAAyB,kBAAe;AAAA;AAAA;AAAA;AAAA,EAItC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EAEA,iBAAiB;AACf,WAAO,IAAI,mBAAyB,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,QAAQ,OAAO;AACb,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AACF;AAEA,IAAOE,iBAAQ;;;ACnCf,IAAM,cAAN,cAA0B,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlC,YAAY,QAAQ,YAAY,YAAY,QAAQ,QAAQ;AAC1D,UAAM,QAAQ,WAAW,SAAY,mBAAW,OAAO,mBAAW;AAElE,UAAM,QAAQ,YAAY,YAAY,KAAK;AAO3C,SAAK,UAAU,WAAW,SAAY,SAAS;AAM/C,SAAK,UAAU;AAMf,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK;AACf,QAAI,KAAK;AACP,WAAK,SAAS;AACd,WAAK,QAAQ,mBAAW;AAAA,IAC1B,OAAO;AACL,WAAK,QAAQ,mBAAW;AAAA,IAC1B;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,SAAS,mBAAW,MAAM;AACjC,WAAK,QAAQ,mBAAW;AACxB,WAAK,QAAQ;AACb,WAAK,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,sBAAQ;;;ACzEf,IAAI,eAAe;AACnB,IAAI;AACF,MAAI,UAAU,IAAI,EAAE;AACtB,SAAS,GAAG;AACV,iBAAe;AACjB;AAGA,IAAI;AAQG,SAAS,aAAa,MAAM,OAAO,QAAQ;AAChD,MAAI,cAAc;AAChB,WAAO,IAAI,UAAU,MAAM,OAAO,MAAM;AAAA,EAC1C;AAEA,MAAI,CAAC,SAAS;AACZ,cAAU,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAAA,EAC5D;AACA,QAAM,YAAY,QAAQ,gBAAgB,OAAO,MAAM;AACvD,YAAU,KAAK,IAAI,IAAI;AACvB,SAAO;AACT;AAoBA,SAAS,aAAa,WAAW;AAC/B,MAAI,qBAAqB;AACzB,MAAI;AACF,QAAI,UAAU,IAAI,EAAE;AAAA,EACtB,SAAS,GAAG;AACV,yBAAqB;AAAA,EACvB;AAEA,WAAS,mBAAmB,MAAM,OAAO,QAAQ;AAC/C,QAAI,oBAAoB;AACtB,aAAO,IAAI,UAAU,MAAM,OAAO,MAAM;AAAA,IAC1C;AACA,WAAO,EAAC,MAAY,OAAc,OAAc;AAAA,EAClD;AAEA,SAAO,SAAU,MAAM;AAErB,UAAM,UAAU,KAAK,SAAS;AAC9B,UAAM,OAAO,KAAK,MAAM;AACxB,UAAM,WAAW,KAAK,UAAU;AAChC,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,SAAS,KAAK,QAAQ;AAE5B,UAAM,aAAa,QAAQ;AAC3B,UAAM,WAAW,QAAQ,CAAC,EAAE;AAE5B,QAAI,UAAU;AACZ,YAAM,SAAS,IAAI,MAAM,UAAU;AACnC,eAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,eAAO,CAAC,IAAI;AAAA,UACV,IAAI,kBAAkB,QAAQ,CAAC,CAAC;AAAA,UAChC;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAMC,UAAS,UAAU,QAAQ,IAAI,EAAE;AACvC,aAAOA,QAAO;AAAA,IAChB;AAEA,UAAM,SAAS,IAAI,kBAAkB,QAAQ;AAC7C,UAAM,SAAS,IAAI,MAAM,UAAU;AACnC,UAAM,SAAS,IAAI,MAAM,UAAU;AACnC,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,aAAO,CAAC,IAAI,IAAI,kBAAkB,QAAQ,CAAC,CAAC;AAC5C,aAAO,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACzB;AACA,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,eAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,cAAM,QAAQ,OAAO,CAAC;AACtB,eAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC;AACtB,eAAO,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,CAAC;AAC1B,eAAO,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,CAAC;AAC1B,eAAO,CAAC,EAAE,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,MAC5B;AACA,YAAM,QAAQ,UAAU,QAAQ,IAAI;AACpC,aAAO,CAAC,IAAI,MAAM,CAAC;AACnB,aAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AACvB,aAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AACvB,aAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AAAA,IACzB;AACA,WAAO,OAAO;AAAA,EAChB;AACF;AAQA,SAAS,aAAa,QAAQ,WAAW;AACvC,QAAM,MAAM,OAAO,KAAK,OAAO,OAAO,CAAC,CAAC,EAAE,IAAI,SAAU,MAAM;AAC5D,WAAO,WAAW,OAAO,QAAQ,OAAO,IAAI,IAAI,EAAE,SAAS,IAAI;AAAA,EACjE,CAAC;AAED,QAAM,QAAQ,IAAI,OAAO;AAAA,IACvB,yBAAyB,aAAa,SAAS,IAAI;AAAA,IACnD,OAAO,UAAU,SAAS;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,SAAS,IAAI;AAAA,IACjB,OAAO,SAAS,cACZ,iCACA,OAAO,KAAK,MAAM,KAAK,IAAI,GAAG,QAAQ,EAAE,SAAS,QAAQ,IACzD,IAAI,gBAAgB,IAAI,KAAK,OAAO,EAAC,MAAM,kBAAiB,CAAC,CAAC;AAAA,EACpE;AACA,SAAO,iBAAiB,WAAW,SAAS;AAC5C,SAAO;AACT;AAaA,SAAS,iBAAiB,QAAQ,WAAW;AAC3C,QAAM,SAAS,aAAa,OAAO,SAAS;AAC5C,MAAI,aAAa;AACjB,SAAO;AAAA,IACL,aAAa,SAAU,MAAM;AAC3B,iBAAW,WAAY;AACrB,YAAI,YAAY;AACd;AAAA,QACF;AACA,kBAAU,EAAC,MAAM,EAAC,QAAQ,OAAO,IAAI,GAAG,MAAM,KAAK,MAAM,EAAC,EAAC,CAAC;AAAA,MAC9D,GAAG,CAAC;AAAA,IACN;AAAA,IACA,WAAW,WAAY;AACrB,mBAAa;AAAA,IACf;AAAA,EACF;AACF;AA0BO,IAAM,YAAN,cAAwB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAY,QAAQ;AAClB,UAAM;AAEN,SAAK,YAAY,CAAC,CAAC,OAAO;AAC1B,QAAI;AACJ,QAAI,OAAO,YAAY,GAAG;AACxB,gBAAU;AAAA,IACZ,WAAW,KAAK,WAAW;AACzB,gBAAU;AAAA,IACZ,OAAO;AACL,gBAAU,OAAO,WAAW;AAAA,IAC9B;AAKA,UAAM,UAAU,IAAI,MAAM,OAAO;AACjC,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,gBAAQ,CAAC,IAAI,aAAa,QAAQ,KAAK,iBAAiB,KAAK,MAAM,CAAC,CAAC;AAAA,MACvE;AAAA,IACF,OAAO;AACL,cAAQ,CAAC,IAAI;AAAA,QACX;AAAA,QACA,KAAK,iBAAiB,KAAK,MAAM,CAAC;AAAA,MACpC;AAAA,IACF;AACA,SAAK,WAAW;AAMhB,SAAK,SAAS,CAAC;AAEf,SAAK,kBAAkB,OAAO,SAAS;AACvC,SAAK,WAAW;AAMhB,SAAK,cAAc,CAAC;AAMpB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,QAAQ,MAAM,UAAU;AAC9B,SAAK,SAAS;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,KAAK;AACZ,SAAK,OAAO,KAAK,GAAG;AACpB,WAAO,KAAK,OAAO,SAAS,KAAK,iBAAiB;AAChD,WAAK,OAAO,MAAM,EAAE,SAAS,MAAM,IAAI;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,KAAK,YAAY,KAAK,OAAO,WAAW,GAAG;AAC7C;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,OAAO,MAAM;AAC9B,SAAK,OAAO;AACZ,UAAM,QAAQ,IAAI,OAAO,CAAC,EAAE;AAC5B,UAAM,SAAS,IAAI,OAAO,CAAC,EAAE;AAC7B,UAAM,UAAU,IAAI,OAAO,IAAI,SAAU,OAAO;AAC9C,aAAO,MAAM,KAAK;AAAA,IACpB,CAAC;AACD,UAAM,UAAU,KAAK,SAAS;AAC9B,SAAK,WAAW;AAChB,QAAI,YAAY,GAAG;AACjB,WAAK,SAAS,CAAC,EAAE;AAAA,QACf;AAAA,UACE;AAAA,UACA,MAAM,IAAI;AAAA,UACV,UAAU,KAAK;AAAA,UACf;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AACA;AAAA,IACF;AAEA,UAAM,SAAS,IAAI,OAAO,CAAC,EAAE,KAAK;AAClC,UAAM,gBAAgB,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO;AACxD,aAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,YAAM,SAAS,IAAI;AACnB,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,eAAO,KAAK,QAAQ,CAAC,EAAE,MAAM,QAAQ,SAAS,aAAa,CAAC;AAAA,MAC9D;AACA,WAAK,SAAS,CAAC,EAAE;AAAA,QACf;AAAA,UACE,SAAS;AAAA,UACT,MAAM,IAAI;AAAA,UACV,UAAU,KAAK;AAAA,UACf;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,OAAO,OAAO;AAC7B,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,SAAK,YAAY,KAAK,IAAI,MAAM;AAChC,MAAE,KAAK;AACP,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,UAAM,MAAM,KAAK;AACjB,UAAM,UAAU,KAAK,SAAS;AAC9B,QAAI,MAAM;AACV,QAAI,YAAY,GAAG;AACjB,aAAO,IAAI,kBAAkB,KAAK,YAAY,CAAC,EAAE,QAAQ,CAAC;AAC1D,aAAO,KAAK,YAAY,CAAC,EAAE,MAAM;AAAA,IACnC,OAAO;AACL,YAAM,SAAS,IAAI,OAAO,CAAC,EAAE,KAAK;AAClC,aAAO,IAAI,kBAAkB,MAAM;AACnC,aAAO,IAAI,MAAM,OAAO;AACxB,YAAM,gBAAgB,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO;AACxD,eAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,cAAM,SAAS,KAAK,YAAY,CAAC,EAAE,QAAQ;AAC3C,cAAM,SAAS,IAAI;AACnB,aAAK,IAAI,IAAI,kBAAkB,MAAM,GAAG,MAAM;AAC9C,aAAK,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,MAAM;AAAA,MACtC;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,cAAc,CAAC;AACpB,QAAI;AAAA,MACF;AAAA,MACA,aAAa,MAAM,IAAI,OAAO,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,EAAE,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAG;AAC7C,WAAK,SAAS,CAAC,EAAE,UAAU;AAAA,IAC7B;AACA,SAAK,SAAS,SAAS;AAAA,EACzB;AACF;AAwBA,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,iBAAiB;AACnB;AAgBO,IAAM,oBAAN,cAAgC,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3C,YAAY,MAAM,YAAY,MAAM;AAClC,UAAM,IAAI;AAOV,SAAK,SAAS,WAAW;AAOzB,SAAK,aAAa,WAAW,UAAU,aAAa,WAAW;AAQ/D,SAAK,OAAO;AAAA,EACd;AACF;AA4CA,IAAM,eAAN,cAA2BC,eAAY;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,YAAY;AAAA,IACd,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,aAAa;AAMlB,SAAK,iBACH,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAMhE,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMlE,SAAK,UAAU,aAAa,QAAQ,OAAO;AAE3C,UAAM,UAAU,KAAK,QAAQ,KAAK,IAAI;AACtC,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,WAAK,QAAQ,CAAC,EAAE,iBAAiB,kBAAU,QAAQ,OAAO;AAAA,IAC5D;AAGA,SAAK,kBAAkB,QAAQ,gBAAgB;AAM/C,SAAK,aAAa,IAAI,kBAAU,WAAY;AAC1C,aAAO;AAAA,IACT,GAAG,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAOlC,SAAK;AAOL,SAAK,uBAAuB;AAM5B,SAAK;AAML,SAAK,cAAc;AAAA,MACjB,SAAS;AAAA,MACT,4BAA4B,OAAgB;AAAA,MAC5C,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,kBAAkB,oBAAoB,KAAK,OAAO;AAAA,MAClD,YAAY;AAAA,MACZ,4BAA4B,OAAgB;AAAA,MAC5C,qBAAqB,CAAC;AAAA,MACtB,MAAM,CAAC,GAAG,CAAC;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,MAAM,KAAK,IAAI;AAAA,MACf,WAAW,CAAC;AAAA,MACZ;AAAA;AAAA,QAAsD;AAAA,UACpD,UAAU;AAAA,QACZ;AAAA;AAAA,MACA,WAAW,CAAC;AAAA,MACZ,aAAa,CAAC;AAAA,MACd,OAAO,OAAO,IAAI;AAAA,MAClB,eAAe,CAAC;AAAA,IAClB;AAEA,SAAK,gBAAgB,SAAU,YAAY;AACzC,YAAM,eAAe,CAAC;AACtB,eACM,QAAQ,GAAG,OAAO,QAAQ,QAAQ,QACtC,QAAQ,MACR,EAAE,OACF;AACA,cAAM,gBAAgB,QAAQ,QAAQ,KAAK;AAC3C,cAAM,SACJ,yBAAyB,iBACrB,gBACA,cAAc,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,cAAM,oBAAoB,OAAO,gBAAgB;AACjD,YAAI,OAAO,sBAAsB,YAAY;AAC3C,gBAAM,oBAAoB,kBAAkB,UAAU;AACtD,uBAAa,KAAK,MAAM,cAAc,iBAAiB;AAAA,QACzD;AAAA,MACF;AACA,aAAO,aAAa,WAAW,IAAI,eAAe;AAAA,IACpD,CAAC;AAED,QAAI,QAAQ,cAAc,QAAW;AACnC,WAAK,aAAa,QAAQ,WAAW,QAAQ,GAAG;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,WAAW,KAAK;AAC3B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAEA,SAAK,aAAa,IAAI,UAAU;AAAA,MAC9B;AAAA,MACA,UAAU,KAAK,mBAAmB;AAAA,MAClC,OAAO;AAAA,MACP;AAAA,MACA,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,QAAQ,YAAY,YAAY;AAChD,UAAM;AAAA;AAAA,MACJ,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW;AAAA;AAGpC,eAAW;AAAA,IACT,OAAO,OAAO,CAAC,GAAG,WAAW,SAAS;AAGxC,UAAM,SAAS,UAAU,MAAM;AAE/B,eAAW,KAAK,CAAC,IAAI,KAAK,KAAK,SAAS,MAAM,IAAI,UAAU;AAC5D,eAAW,KAAK,CAAC,IAAI,KAAK,KAAK,UAAU,MAAM,IAAI,UAAU;AAC7D,eAAW,SAAS;AAAA,MAClB,OAAO,CAAC,IAAK,WAAW,KAAK,CAAC,IAAI,aAAc;AAAA,MAChD,OAAO,CAAC,IAAK,WAAW,KAAK,CAAC,IAAI,aAAc;AAAA,MAChD,OAAO,CAAC,IAAK,WAAW,KAAK,CAAC,IAAI,aAAc;AAAA,MAChD,OAAO,CAAC,IAAK,WAAW,KAAK,CAAC,IAAI,aAAc;AAAA,IAClD;AACA,eAAW,OAAO,KAAK,IAAI;AAE3B,UAAM,YAAY,WAAW;AAC7B,cAAU,SAAS;AACnB,cAAU,aAAa;AACvB,cAAU,aAAa;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,QAAI,QAAQ;AACZ,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,eAAS,KAAK,QAAQ,CAAC,EAAE,UAAU;AACnC,UAAI,CAAC,UAAU,OAAO,SAAS,MAAM,SAAS;AAC5C,gBAAQ;AACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,QAAQ,YAAY,YAAY,YAAY;AACnD,QAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,aAAO;AAAA,IACT;AAEA,SAAK,WAAW,cAAc,IAAI,EAAE;AAEpC,iBAAa,KAAK,sBAAsB,UAAU;AAClD,UAAM,aAAa,KAAK,kBAAkB,QAAQ,YAAY,UAAU;AACxE,SAAK,uBAAuB;AAG5B,QAAI,KAAK,sBAAsB;AAC7B,YAAM,qBAAqB,KAAK,qBAAqB,cAAc;AACnE,YAAM,iBAAiB,KAAK,qBAAqB,UAAU;AAC3D,UACE,eAAe,sBACf,CAAC,OAAO,WAAW,QAAQ,cAAc,GACzC;AACA,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AAEA,QACE,CAAC,KAAK,wBACN,KAAK,YAAY,MAAM,KAAK,mBAC5B;AACA,WAAK,gBAAgB;AAAA,IACvB;AAEA,QAAI,WAAW,SAAS;AACtB,4BAAsB,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,IAC/C;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,UAAM,aAAa,KAAK;AACxB,UAAM,MAAM,KAAK,QAAQ;AACzB,UAAM,aAAa,IAAI,MAAM,GAAG;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,iBAAW,aAAa;AACxB,iBAAW,gBAAgB,CAAC;AAC5B,YAAM,YAAY,aAAa,KAAK,QAAQ,CAAC,GAAG,UAAU;AAC1D,UAAI,WAAW;AACb,mBAAW,CAAC,IAAI;AAAA,MAClB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAEA,UAAM,OAAO,CAAC;AACd,SAAK;AAAA,MACH,IAAI,kBAAkB,gBAAgB,kBAAkB,YAAY,IAAI;AAAA,IAC1E;AACA,SAAK,WAAW;AAAA,MACd;AAAA,MACA;AAAA,MACA,KAAK,kBAAkB,KAAK,MAAM,UAAU;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,YAAY,KAAK,QAAQ,MAAM;AAC/C,QAAI,OAAO,CAAC,QAAQ;AAClB;AAAA,IACF;AAGA,UAAM,SAAS,WAAW;AAC1B,UAAM,aAAa,WAAW,UAAU;AACxC,QACE,eAAe,KAAK,qBAAqB,UAAU,cACnD,CAAC,OAAO,QAAQ,KAAK,qBAAqB,MAAM,GAChD;AACA;AAAA,IACF;AAEA,QAAIC;AACJ,QAAI,KAAK,sBAAsB;AAC7B,MAAAA,WAAU,KAAK,qBAAqB,SAAS,EAAE,WAAW,IAAI;AAAA,IAChE,OAAO;AACL,YAAM,QAAQ,KAAK,MAAM,SAAS,MAAM,IAAI,UAAU;AACtD,YAAM,SAAS,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU;AACxD,MAAAA,WAAU,sBAAsB,OAAO,MAAM;AAC7C,WAAK,uBAAuB,IAAI;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACAA,SAAQ;AAAA,MACV;AAAA,IACF;AACA,IAAAA,SAAQ,aAAa,QAAQ,GAAG,CAAC;AAEjC,QAAI,WAAW,SAAS;AACtB,4BAAsB,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,IAC/C,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AACA,SAAK,oBAAoB,KAAK,YAAY;AAE1C,SAAK;AAAA,MACH,IAAI,kBAAkB,gBAAgB,iBAAiB,YAAY,IAAI;AAAA,IACzE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAY;AACzB,QAAI,CAAC,KAAK,iBAAiB;AACzB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,MAAM,eAAe;AACvC,QAAI,CAAC,aAAa;AAChB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,cAAM,SAAS,KAAK,QAAQ,CAAC,EAAE,UAAU;AACzC,sBAAc,OAAO,eAAe,UAAU;AAC9C,YAAI,aAAa;AACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AACA,UAAM,gBAAgB;AAAA,EACxB;AACF;AAOA,aAAa,UAAU;AAOvB,IAAI,gBAAgB;AAQpB,SAAS,aAAa,OAAO,YAAY;AACvC,QAAM,WAAW,MAAM,YAAY;AACnC,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,6BAA6B,KAAK;AAAA,EACpD;AAEA,MAAI,CAAC,SAAS,aAAa,UAAU,GAAG;AACtC,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,WAAW,KAAK,CAAC;AAC/B,QAAM,SAAS,WAAW,KAAK,CAAC;AAChC,MAAI,UAAU,KAAK,WAAW,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,YAAY,SAAS,YAAY,YAAY,IAAI;AACvD,MAAI;AACJ,MAAI,qBAAqB,mBAAmB;AAC1C,cAAU;AAAA,EACZ,OAAO;AACL,QAAI,WAAW;AACb,gBAAU,UAAU;AAAA,IACtB;AACA,QAAI,EAAE,mBAAmB,oBAAoB;AAC3C,YAAM,IAAI,MAAM,mCAAmC,OAAO;AAAA,IAC5D;AACA,QAAI,QAAQ,UAAU,SAAS,QAAQ,WAAW,QAAQ;AACxD,YAAMA,WAAU,QAAQ,WAAW,IAAI;AACvC,aAAOA,SAAQ,aAAa,GAAG,GAAG,OAAO,MAAM;AAAA,IACjD;AAAA,EACF;AAEA,MAAI,CAAC,eAAe;AAClB,oBAAgB,sBAAsB,OAAO,QAAQ,QAAW;AAAA,MAC9D,oBAAoB;AAAA,IACtB,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,cAAc;AAC7B,QAAI,OAAO,UAAU,SAAS,OAAO,WAAW,QAAQ;AACtD,sBAAgB,sBAAsB,OAAO,QAAQ,QAAW;AAAA,QAC9D,oBAAoB;AAAA,MACtB,CAAC;AAAA,IACH,OAAO;AACL,oBAAc,UAAU,GAAG,GAAG,OAAO,MAAM;AAAA,IAC7C;AAAA,EACF;AACA,gBAAc,UAAU,SAAS,GAAG,GAAG,OAAO,MAAM;AACpD,SAAO,cAAc,aAAa,GAAG,GAAG,OAAO,MAAM;AACvD;AAOA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,OAAO,IAAI,SAAU,OAAO;AACjC,WAAO,MAAM,cAAc;AAAA,EAC7B,CAAC;AACH;AAOA,SAAS,aAAa,SAAS;AAC7B,QAAM,MAAM,QAAQ;AACpB,QAAM,SAAS,IAAI,MAAM,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,WAAO,CAAC,IAAI,YAAY,QAAQ,CAAC,CAAC;AAAA,EACpC;AACA,SAAO;AACT;AAOA,SAAS,YAAY,eAAe;AAElC,MAAI;AACJ,MAAI,yBAAyB,gBAAQ;AACnC,QAAI,yBAAyBC,eAAY;AACvC,cAAQ,IAAIA,cAAU,EAAC,QAAQ,cAAa,CAAC;AAAA,IAC/C,WAAW,yBAAyBF,gBAAa;AAC/C,cAAQ,IAAIA,eAAW,EAAC,QAAQ,cAAa,CAAC;AAAA,IAChD;AAAA,EACF,OAAO;AACL,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AAEA,IAAO,iBAAQ;;;ACl/BR,SAAS,MAAM,KAAK,UAAU,SAAS,eAAe;AAC3D,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,MAAM,SAAS,OAAO,QAAQ;AACpC,WAAS,UAAU;AACjB,WAAO,OAAO,GAAG;AACjB,WAAO,WAAW,YAAY,MAAM;AAAA,EACtC;AACA,SAAO,QAAQ;AACf,SAAO,MACL,OACC,IAAI,SAAS,GAAG,IAAI,MAAM,QAC1B,iBAAiB,cAClB,MACA;AACF,QAAM,QAAQ,WAAW,WAAY;AACnC,YAAQ;AACR,QAAI,SAAS;AACX,cAAQ;AAAA,IACV;AAAA,EACF,GAAG,GAAK;AACR,SAAO,GAAG,IAAI,SAAU,MAAM;AAC5B,iBAAa,KAAK;AAClB,YAAQ;AACR,aAAS,IAAI;AAAA,EACf;AACA,WAAS,KAAK,YAAY,MAAM;AAClC;AAEO,IAAM,gBAAN,cAA4B,MAAM;AAAA;AAAA;AAAA;AAAA,EAIvC,YAAY,UAAU;AACpB,UAAM,UAAU,iCAAiC,SAAS;AAC1D,UAAM,OAAO;AAKb,SAAK,OAAO;AAKZ,SAAK,WAAW;AAAA,EAClB;AACF;AAEO,IAAM,cAAN,cAA0B,MAAM;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,QAAQ;AAClB,UAAM,yBAAyB;AAK/B,SAAK,OAAO;AAKZ,SAAK,SAAS;AAAA,EAChB;AACF;AAMO,SAAS,QAAQ,KAAK;AAC3B,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAI5C,aAAS,OAAO,OAAO;AACrB,YAAMG,UAAS,MAAM;AAErB,UAAI,CAACA,QAAO,UAAWA,QAAO,UAAU,OAAOA,QAAO,SAAS,KAAM;AACnE,YAAI;AACJ,YAAI;AACF,iBAAO,KAAK,MAAMA,QAAO,YAAY;AAAA,QACvC,SAAS,KAAK;AACZ,gBAAM,UAAU,0CAA0C,IAAI;AAC9D,iBAAO,IAAI,MAAM,OAAO,CAAC;AACzB;AAAA,QACF;AACA,gBAAQ,IAAI;AACZ;AAAA,MACF;AAEA,aAAO,IAAI,cAAcA,OAAM,CAAC;AAAA,IAClC;AAKA,aAAS,QAAQ,OAAO;AACtB,aAAO,IAAI,YAAY,MAAM,MAAM,CAAC;AAAA,IACtC;AAEA,UAAM,SAAS,IAAI,eAAe;AAClC,WAAO,iBAAiB,QAAQ,MAAM;AACtC,WAAO,iBAAiB,SAAS,OAAO;AACxC,WAAO,KAAK,OAAO,GAAG;AACtB,WAAO,iBAAiB,UAAU,kBAAkB;AACpD,WAAO,KAAK;AAAA,EACd,CAAC;AACH;AAOO,SAAS,WAAW,MAAM,KAAK;AACpC,MAAI,IAAI,SAAS,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,SAAO,IAAI,IAAI,KAAK,IAAI,EAAE;AAC5B;;;AClEA,IAAM,WAAN,cAAuB,kBAAU;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,aAAa,QAAQ;AAAA,MACrB,YAAY,IAAc,WAAW;AAAA,MACrC,4BAA4B,QAAQ;AAAA,MACpC,OAAO;AAAA,MACP,kBAAkB,QAAQ;AAAA,MAC1B,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,YAAY;AAMjB,SAAK,YAAY,QAAQ;AAEzB,QAAI,QAAQ,KAAK;AACf,UAAI,QAAQ,OAAO;AACjB;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,uBAAuB,KAAK,IAAI;AAAA,UACrC,KAAK,oBAAoB,KAAK,IAAI;AAAA,QACpC;AAAA,MACF,OAAO;AACL,cAAM,SAAS,IAAI,eAAe;AAClC,eAAO,iBAAiB,QAAQ,KAAK,WAAW,KAAK,IAAI,CAAC;AAC1D,eAAO,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;AAC5D,eAAO,KAAK,OAAO,QAAQ,GAAG;AAC9B,eAAO,KAAK;AAAA,MACd;AAAA,IACF,WAAW,QAAQ,UAAU;AAC3B,WAAK,uBAAuB,QAAQ,QAAQ;AAAA,IAC9C,OAAO;AACL,aAAO,OAAO,EAAE;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,UAAM;AAAA;AAAA,MAAwC,MAAM;AAAA;AAEpD,QAAI,CAAC,OAAO,UAAW,OAAO,UAAU,OAAO,OAAO,SAAS,KAAM;AACnE,UAAI;AACJ,UAAI;AACF;AAAA,QAAkC,KAAK,MAAM,OAAO,YAAY;AAAA,MAClE,SAAS,KAAK;AACZ,aAAK,oBAAoB;AACzB;AAAA,MACF;AACA,WAAK,uBAAuB,QAAQ;AAAA,IACtC,OAAO;AACL,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,UAAU;AAC/B,UAAM,qBAAqB,IAAc,WAAW;AAEpD,UAAM,mBAAmB,KAAK,cAAc;AAC5C,QAAI;AACJ,QAAI,SAAS,QAAQ,MAAM,QAAW;AACpC,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AACA,eAAS,eAAe,SAAS,QAAQ,GAAG,SAAS;AAAA,IACvD;AAEA,UAAM,aAAa,qBAAqB,gBAAgB;AACxD,UAAM,UAAU,SAAS,SAAS,KAAK;AACvC,UAAM,UAAU,SAAS,SAAS,KAAK;AACvC,UAAM,WAAW,UAAU;AAAA,MACzB,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,WAAW;AAEhB,SAAK,kBAAkB,oBAAoB,SAAS,OAAO,GAAG,QAAQ;AAEtE,QAAI,SAAS,aAAa,KAAK,CAAC,KAAK,gBAAgB,GAAG;AACtD,YAAM,oBAAoB,WAAW,SAAY,SAAS;AAC1D,WAAK,gBAAgB,SAAU,YAAY;AACzC,YAAI,WAAW,mBAAmB,WAAW,MAAM,GAAG;AACpD,iBAAO,CAAC,SAAS,aAAa,CAAC;AAAA,QACjC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,SAAK,YAAY;AACjB,SAAK,SAAS,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,SAAS,OAAO;AAAA,EACvB;AACF;AAEA,IAAO,mBAAQ;;;AC3LR,SAAS,YAAY,MAAM;AAChC,SAAO,gBAAgB,SACrB,gBAAgB,qBAChB,gBAAgB,mBACd,OACA;AACN;AAMO,SAAS,YAAY,MAAM;AAChC,SAAO,gBAAgB,cACrB,gBAAgB,qBAChB,gBAAgB,gBAChB,gBAAgB,WACd,OACA;AACN;AAKA,IAAIC,iBAAgB;AAMb,SAAS,QAAQ,OAAO;AAC7B,MAAI,CAACA,gBAAe;AAClB,IAAAA,iBAAgB;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA,EAAC,oBAAoB,KAAI;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,SAASA,eAAc;AAC7B,QAAM,QAAQ,MAAM;AACpB,MAAI,OAAO,UAAU,OAAO;AAC1B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,SAAS,MAAM;AACrB,MAAI,OAAO,WAAW,QAAQ;AAC5B,WAAO,SAAS;AAAA,EAClB;AACA,EAAAA,eAAc,UAAU,GAAG,GAAG,OAAO,MAAM;AAC3C,EAAAA,eAAc,UAAU,OAAO,GAAG,CAAC;AACnC,SAAOA,eAAc,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AACzD;AAKA,IAAM,cAAc,CAAC,KAAK,GAAG;AAe7B,IAAM,WAAN,cAAuB,aAAK;AAAA;AAAA;AAAA;AAAA,EAI1B,YAAY,SAAS;AACnB,UAAM,QAAQ,kBAAU;AAExB,UAAM,QAAQ,WAAW,OAAO;AAAA,MAC9B,YAAY,QAAQ;AAAA,MACpB,aAAa,QAAQ;AAAA,IACvB,CAAC;AAMD,SAAK,UAAU,QAAQ;AAMvB,SAAK,QAAQ;AAMb,SAAK,SAAS;AAMd,SAAK,QAAQ,QAAQ,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,QAAI,KAAK,OAAO;AACd,aAAO,KAAK;AAAA,IACd;AACA,UAAM,YAAY,YAAY,KAAK,KAAK;AACxC,QAAI,WAAW;AACb,aAAO,CAAC,UAAU,OAAO,UAAU,MAAM;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,QAAI,KAAK,UAAU,kBAAU,QAAQ,KAAK,UAAU,kBAAU,OAAO;AACnE;AAAA,IACF;AACA,SAAK,QAAQ,kBAAU;AACvB,SAAK,QAAQ;AAEb,UAAM,OAAO;AACb,SAAK,QAAQ,EACV,KAAK,SAAU,MAAM;AACpB,WAAK,QAAQ;AACb,WAAK,QAAQ,kBAAU;AACvB,WAAK,QAAQ;AAAA,IACf,CAAC,EACA,MAAM,SAAU,OAAO;AACtB,WAAK,SAAS;AACd,WAAK,QAAQ,kBAAU;AACvB,WAAK,QAAQ;AAAA,IACf,CAAC;AAAA,EACL;AACF;AAEA,IAAO,mBAAQ;;;AChJf,IAAM,iBAAN,cAA6B,iBAAS;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,WAAW,QAAQ;AAAA,MACnB,QAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAC/C,aAAa,QAAQ;AAAA,MACrB,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,cAAc,QAAQ;AAM3B,SAAK,UAAU,QAAQ;AAMvB,SAAK,cAAc;AAMnB,SAAK,eAAe;AAMpB,SAAK,cAAc;AAMnB,SAAK,kBAAkB,QAAQ;AAM/B,SAAK,kBAAkB,QAAQ;AAM/B,SAAK,oBAAoB,QAAQ,oBAAoB,QAAQ;AAM7D,SAAK,eAAe,CAAC;AAMrB,SAAK,uBAAuB;AAM5B,SAAK,WAAW;AAEhB,UAAM,eAAe,KAAK,gBAAgB;AAAA,MACxC,KAAK;AAAA,IACP;AACA,UAAM,kBAAkB,KAAK,gBAAgB,UAAU;AACvD,QAAI,kBAAkB,KAAK,gBAAgB,UAAU;AAErD,UAAM,sBAAsB,kBACxB,gBAAgB,cAAc,eAAe,IAC7C;AAEJ,QAAI,QAAQ,mBAAmB,MAAM,GAAG;AAGtC,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,UAAM,aAAa,QAAQ;AAC3B,UAAM,mBAAmB,WAAW,UAAU;AAC9C,QAAI,kBAAkB;AACpB,UAAI,CAAC,iBAAiB;AACpB,0BAAkB;AAAA,MACpB,OAAO;AACL,0BAAkB,gBAAgB,iBAAiB,gBAAgB;AAAA,MACrE;AAAA,IACF;AAEA,UAAM,mBAAmB,KAAK,gBAAgB;AAAA,MAC5C,KAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,UAAM,aAAa,QAAQ;AAC3B,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,gBAAgB,KAAK,oBAAoB,GAAG;AAGxD,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,UAAM,yBACJ,QAAQ,mBAAmB,SACvB,QAAQ,iBACR;AAMN,SAAK,iBAAiB,IAAI;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,KAAK,eAAe,aAAa,EAAE,WAAW,GAAG;AAEnD,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,SAAK,WAAW,KAAK,gBAAgB,kBAAkB,gBAAgB;AACvE,QAAI,eAAe,KAAK,eAAe,sBAAsB;AAE7D,QAAI,iBAAiB;AACnB,UAAI,WAAW,SAAS,GAAG;AACzB,qBAAa,CAAC,IAAI;AAAA,UAChB,aAAa,CAAC;AAAA,UACd,gBAAgB,CAAC;AAAA,UACjB,gBAAgB,CAAC;AAAA,QACnB;AACA,qBAAa,CAAC,IAAI;AAAA,UAChB,aAAa,CAAC;AAAA,UACd,gBAAgB,CAAC;AAAA,UACjB,gBAAgB,CAAC;AAAA,QACnB;AAAA,MACF,OAAO;AACL,uBAAe,gBAAgB,cAAc,eAAe;AAAA,MAC9D;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,WAAK,QAAQ,kBAAU;AAAA,IACzB,OAAO;AACL,YAAM,cAAc,KAAK,gBAAgB;AAAA,QACvC;AAAA,QACA,KAAK;AAAA,MACP;AACA,YAAM,UAAU,QAAQ;AACxB,eAAS,OAAO,YAAY,MAAM,QAAQ,YAAY,MAAM,QAAQ;AAClE,iBAAS,OAAO,YAAY,MAAM,QAAQ,YAAY,MAAM,QAAQ;AAClE,gBAAM,OAAO,QAAQ,KAAK,UAAU,MAAM,MAAM,KAAK,WAAW;AAChE,cAAI,MAAM;AACR,iBAAK,aAAa,KAAK,IAAI;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAK,QAAQ,kBAAU;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,cAAc,CAAC;AACrB,SAAK,aAAa,QAAQ,CAAC,SAAS;AAClC,UAAI,CAAC,QAAQ,KAAK,SAAS,MAAM,kBAAU,QAAQ;AACjD;AAAA,MACF;AACA,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,SAAS,KAAK;AAIpB,UAAI;AACJ,YAAM,YAAY,YAAY,KAAK,QAAQ,CAAC;AAC5C,UAAI,WAAW;AACb,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW,QAAQ,YAAY,KAAK,QAAQ,CAAC,CAAC;AAAA,MAChD;AACA,YAAM,YAAY,CAAC,KAAK,CAAC,IAAI,IAAI,QAAQ,KAAK,CAAC,IAAI,IAAI,MAAM;AAC7D,YAAM,UAAU,oBAAoB;AACpC,YAAM,aAAa,UAAU,CAAC,IAAI,UAAU,CAAC;AAC7C,YAAM,WAAW,UAAU,eAAe;AAC1C,YAAM,YAAY,IAAI,SAAS,SAAS,MAAM;AAC9C,YAAM,kBAAkB,SAAS;AACjC,YAAM,gBAAiB,kBAAkB,UAAU,SAAU;AAC7D,YAAM,cAAc,UAAU,aAAa,UAAU,CAAC;AACtD,YAAM,YAAY,KAAK;AAAA,QACrB,cAAc,kBAAkB,UAAU,CAAC;AAAA,MAC7C;AACA,YAAM,eAAe,aAAa;AAClC,UAAI,aAAa;AACjB,UAAI,UAAU,WAAW,cAAc;AACrC,qBAAa,IAAI,SAAS,YAAY;AACtC,YAAI,YAAY;AAChB,YAAI,YAAY;AAChB,cAAM,WAAW,UAAU,CAAC,IAAI;AAChC,iBAAS,WAAW,GAAG,WAAW,UAAU,CAAC,GAAG,EAAE,UAAU;AAC1D,mBAAS,WAAW,GAAG,WAAW,UAAU,EAAE,UAAU;AACtD,uBAAW,WAAW,IAAI,UAAU,YAAY,QAAQ;AAAA,UAC1D;AACA,uBAAa,cAAc;AAAA,QAC7B;AAAA,MACF;AACA,kBAAY,KAAK;AAAA,QACf,QAAQ,KAAK,gBAAgB,mBAAmB,KAAK,SAAS;AAAA,QAC9D,MAAM,IAAI,WAAW,WAAW,MAAM;AAAA,QACtC,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,aAAa,SAAS;AAE3B,QAAI,YAAY,WAAW,GAAG;AAC5B,WAAK,QAAQ,kBAAU;AAAA,IACzB,OAAO;AACL,YAAM,IAAI,KAAK,kBAAkB,CAAC;AAClC,YAAM,OAAO,KAAK,gBAAgB,YAAY,CAAC;AAC/C,YAAM,cAAc,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC;AAC5D,YAAM,eAAe,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC;AAC7D,YAAM,mBAAmB,KAAK,gBAAgB,cAAc,CAAC;AAC7D,YAAM,mBAAmB,KAAK,gBAAgB;AAAA,QAC5C,KAAK;AAAA,MACP;AAEA,YAAM,eAAe,KAAK,gBAAgB;AAAA,QACxC,KAAK;AAAA,MACP;AAEA,UAAI,OAAO;AAEX,YAAM,gBAAgB,YAAY,CAAC,EAAE;AAErC,YAAM,UAAU,KAAK,KAAK,gBAAgB,CAAC;AAC3C,eAAS,SAAS,UAAU,GAAG,UAAU,GAAG,EAAE,QAAQ;AACpD,cAAM,UAAU,CAAC;AACjB,iBAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,EAAE,GAAG;AACtD,gBAAM,aAAa,YAAY,CAAC;AAChC,gBAAM,SAAS,WAAW;AAC1B,gBAAM,YAAY,WAAW;AAC7B,gBAAM,QAAQ,UAAU,CAAC;AACzB,gBAAM,SAAS,UAAU,CAAC;AAC1B,gBAAMC,WAAU,sBAAsB,OAAO,QAAQ,UAAU;AAC/D,gBAAMC,aAAYD,SAAQ,gBAAgB,OAAO,MAAM;AACvD,gBAAME,QAAOD,WAAU;AACvB,cAAIE,UAAS,SAAS;AACtB,mBAAS,IAAI,GAAGC,OAAMF,MAAK,QAAQ,IAAIE,MAAK,KAAK,GAAG;AAClD,YAAAF,MAAK,CAAC,IAAI,OAAOC,OAAM;AACvB,YAAAD,MAAK,IAAI,CAAC,IAAI,OAAOC,UAAS,CAAC;AAC/B,YAAAD,MAAK,IAAI,CAAC,IAAI,OAAOC,UAAS,CAAC;AAC/B,YAAAD,MAAK,IAAI,CAAC,IAAI;AACd,YAAAC,WAAU;AAAA,UACZ;AACA,UAAAH,SAAQ,aAAaC,YAAW,GAAG,CAAC;AACpC,kBAAQ,KAAK;AAAA,YACX,QAAQ,WAAW;AAAA,YACnB,OAAOD,SAAQ;AAAA,UACjB,CAAC;AAAA,QACH;AAEA,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,KAAK,gBAAgB,UAAU;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,EAAE,GAAG;AAClD,gBAAMK,UAAS,QAAQ,CAAC,EAAE;AAC1B,gBAAML,WAAUK,QAAO,WAAW,IAAI;AACtC,wBAAcL,QAAO;AACrB,qBAAW,KAAKA,SAAQ,MAAM;AAAA,QAChC;AAEA,cAAMA,WAAU,OAAO,WAAW,IAAI;AACtC,cAAM,YAAYA,SAAQ;AAAA,UACxB;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAEA,sBAAcA,QAAO;AACrB,mBAAW,KAAK,MAAM;AAEtB,YAAI,CAAC,OAAO;AACV,kBAAQ,IAAI;AAAA,YACV,gBAAgB,UAAU,QAAQ,UAAU;AAAA,UAC9C;AACA,kBAAQ,IAAI,YAAY,CAAC,EAAE,SAAS,MAAM,MAAM;AAAA,QAClD;AAEA,cAAM,OAAO,UAAU;AACvB,YAAI,SAAS,SAAS;AACtB,iBAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK,GAAG;AAClD,cAAI,KAAK,IAAI,CAAC,MAAM,KAAK;AACvB,kBAAM,MAAM,IAAI,KAAK,CAAC;AACtB,kBAAM,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC;AAC9B,kBAAM,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,UAChC,OAAO;AACL,kBAAM,MAAM,IAAI;AAChB,kBAAM,SAAS,CAAC,IAAI;AACpB,kBAAM,SAAS,CAAC,IAAI;AAAA,UACtB;AACA,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,WAAK,cAAc;AACnB,WAAK,cAAc;AAAA,QACjB,KAAK,MAAM,cAAc,KAAK,WAAW;AAAA,QACzC,KAAK,MAAM,eAAe,KAAK,WAAW;AAAA,MAC5C;AACA,WAAK,QAAQ,kBAAU;AAAA,IACzB;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,UAAU,kBAAU,QAAQ,KAAK,UAAU,kBAAU,OAAO;AACnE;AAAA,IACF;AACA,SAAK,QAAQ,kBAAU;AACvB,SAAK,QAAQ;AAEb,QAAI,aAAa;AAEjB,SAAK,uBAAuB,CAAC;AAC7B,SAAK,aAAa,QAAQ,CAAC,SAAS;AAClC,YAAM,QAAQ,KAAK,SAAS;AAC5B,UAAI,UAAU,kBAAU,QAAQ,UAAU,kBAAU,SAAS;AAC3D;AAAA,MACF;AACA;AAEA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA,kBAAU;AAAA,QACV,WAAY;AACV,gBAAMM,SAAQ,KAAK,SAAS;AAC5B,cACEA,UAAS,kBAAU,UACnBA,UAAS,kBAAU,SACnBA,UAAS,kBAAU,OACnB;AACA,0BAAc,eAAe;AAC7B;AACA,gBAAI,eAAe,GAAG;AACpB,mBAAK,iBAAiB;AACtB,mBAAK,WAAW;AAAA,YAClB;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,MACF;AACA,WAAK,qBAAqB,KAAK,eAAe;AAAA,IAChD,CAAC;AAED,QAAI,eAAe,GAAG;AACpB,iBAAW,KAAK,WAAW,KAAK,IAAI,GAAG,CAAC;AAAA,IAC1C,OAAO;AACL,WAAK,aAAa,QAAQ,SAAU,MAAM;AACxC,cAAM,QAAQ,KAAK,SAAS;AAC5B,YAAI,SAAS,kBAAU,MAAM;AAC3B,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,qBAAqB,QAAQ,aAAa;AAC/C,SAAK,uBAAuB;AAAA,EAC9B;AACF;AAEA,IAAOC,oBAAQ;", "names": ["sourceState", "extent", "resolution", "pixelRatio", "Image_default", "Layer_default", "context", "render", "Image_default", "output", "Image_default", "context", "Tile_default", "client", "sharedContext", "context", "imageData", "data", "offset", "len", "canvas", "state", "DataTile_default"]}