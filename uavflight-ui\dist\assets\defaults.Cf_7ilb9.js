const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.B2FR0lVF.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/header.CDsBv0mY.js","assets/main.DP48xYwf.js"])))=>i.map(i=>d[i]);
import{u as k,N as D,a as r,__tla as P}from"./index.BSP3cg_z.js";import{d as i,k as p,l as j,s as A,o as I,w as y,B as R,e as L,b as M,v as s,t,u as o,j as _,y as O}from"./vue.CnN__PXn.js";let d,S=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{let u;u=i({name:"layoutDefaults"}),d=i({...u,setup(V){const h=_(()=>r(()=>import("./aside.B2FR0lVF.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),m=_(()=>r(()=>import("./header.CDsBv0mY.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([4,1,2,3]))),w=_(()=>r(()=>import("./main.DP48xYwf.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([5,1,2,3]))),e=p(""),l=p(),b=j(),v=k(),{themeConfig:E}=A(v),n=()=>{e.value.update(),l.value.layoutMainScrollbarRef.update()},c=()=>{O(()=>{setTimeout(()=>{n(),e.value.wrapRef.scrollTop=0,l.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return I(()=>{c(),D.done(600)}),y(()=>b.path,()=>{c()}),y(E,()=>{n()},{deep:!0}),(a,g)=>{const T=R("el-scrollbar"),f=R("el-container");return M(),L(f,{class:"layout-container"},{default:s(()=>[t(o(h)),t(f,{class:"layout-container-view h100"},{default:s(()=>[t(T,{ref_key:"layoutScrollbarRef",ref:e,class:"layout-backtop"},{default:s(()=>[t(o(m)),t(o(w),{ref_key:"layoutMainRef",ref:l},null,512)]),_:1},512)]),_:1})]),_:1})}}})});export{S as __tla,d as default};
