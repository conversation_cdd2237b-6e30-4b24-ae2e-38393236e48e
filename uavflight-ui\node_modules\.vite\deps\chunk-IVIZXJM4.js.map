{"version": 3, "sources": ["../../ieee754/index.js", "../../pbf/index.js", "../../ol/format/MVT.js", "../../ol/render/Feature.js"], "sourcesContent": ["/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "'use strict';\n\nmodule.exports = Pbf;\n\nvar ieee754 = require('ieee754');\n\nfunction Pbf(buf) {\n    this.buf = ArrayBuffer.isView && ArrayBuffer.isView(buf) ? buf : new Uint8Array(buf || 0);\n    this.pos = 0;\n    this.type = 0;\n    this.length = this.buf.length;\n}\n\nPbf.Varint  = 0; // varint: int32, int64, uint32, uint64, sint32, sint64, bool, enum\nPbf.Fixed64 = 1; // 64-bit: double, fixed64, sfixed64\nPbf.Bytes   = 2; // length-delimited: string, bytes, embedded messages, packed repeated fields\nPbf.Fixed32 = 5; // 32-bit: float, fixed32, sfixed32\n\nvar SHIFT_LEFT_32 = (1 << 16) * (1 << 16),\n    SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;\n\n// Threshold chosen based on both benchmarking and knowledge about browser string\n// data structures (which currently switch structure types at 12 bytes or more)\nvar TEXT_DECODER_MIN_LENGTH = 12;\nvar utf8TextDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf8');\n\nPbf.prototype = {\n\n    destroy: function() {\n        this.buf = null;\n    },\n\n    // === READING =================================================================\n\n    readFields: function(readField, result, end) {\n        end = end || this.length;\n\n        while (this.pos < end) {\n            var val = this.readVarint(),\n                tag = val >> 3,\n                startPos = this.pos;\n\n            this.type = val & 0x7;\n            readField(tag, result, this);\n\n            if (this.pos === startPos) this.skip(val);\n        }\n        return result;\n    },\n\n    readMessage: function(readField, result) {\n        return this.readFields(readField, result, this.readVarint() + this.pos);\n    },\n\n    readFixed32: function() {\n        var val = readUInt32(this.buf, this.pos);\n        this.pos += 4;\n        return val;\n    },\n\n    readSFixed32: function() {\n        var val = readInt32(this.buf, this.pos);\n        this.pos += 4;\n        return val;\n    },\n\n    // 64-bit int handling is based on github.com/dpw/node-buffer-more-ints (MIT-licensed)\n\n    readFixed64: function() {\n        var val = readUInt32(this.buf, this.pos) + readUInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;\n        this.pos += 8;\n        return val;\n    },\n\n    readSFixed64: function() {\n        var val = readUInt32(this.buf, this.pos) + readInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;\n        this.pos += 8;\n        return val;\n    },\n\n    readFloat: function() {\n        var val = ieee754.read(this.buf, this.pos, true, 23, 4);\n        this.pos += 4;\n        return val;\n    },\n\n    readDouble: function() {\n        var val = ieee754.read(this.buf, this.pos, true, 52, 8);\n        this.pos += 8;\n        return val;\n    },\n\n    readVarint: function(isSigned) {\n        var buf = this.buf,\n            val, b;\n\n        b = buf[this.pos++]; val  =  b & 0x7f;        if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 7;  if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 14; if (b < 0x80) return val;\n        b = buf[this.pos++]; val |= (b & 0x7f) << 21; if (b < 0x80) return val;\n        b = buf[this.pos];   val |= (b & 0x0f) << 28;\n\n        return readVarintRemainder(val, isSigned, this);\n    },\n\n    readVarint64: function() { // for compatibility with v2.0.1\n        return this.readVarint(true);\n    },\n\n    readSVarint: function() {\n        var num = this.readVarint();\n        return num % 2 === 1 ? (num + 1) / -2 : num / 2; // zigzag encoding\n    },\n\n    readBoolean: function() {\n        return Boolean(this.readVarint());\n    },\n\n    readString: function() {\n        var end = this.readVarint() + this.pos;\n        var pos = this.pos;\n        this.pos = end;\n\n        if (end - pos >= TEXT_DECODER_MIN_LENGTH && utf8TextDecoder) {\n            // longer strings are fast with the built-in browser TextDecoder API\n            return readUtf8TextDecoder(this.buf, pos, end);\n        }\n        // short strings are fast with our custom implementation\n        return readUtf8(this.buf, pos, end);\n    },\n\n    readBytes: function() {\n        var end = this.readVarint() + this.pos,\n            buffer = this.buf.subarray(this.pos, end);\n        this.pos = end;\n        return buffer;\n    },\n\n    // verbose for performance reasons; doesn't affect gzipped size\n\n    readPackedVarint: function(arr, isSigned) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readVarint(isSigned));\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readVarint(isSigned));\n        return arr;\n    },\n    readPackedSVarint: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readSVarint());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSVarint());\n        return arr;\n    },\n    readPackedBoolean: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readBoolean());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readBoolean());\n        return arr;\n    },\n    readPackedFloat: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readFloat());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFloat());\n        return arr;\n    },\n    readPackedDouble: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readDouble());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readDouble());\n        return arr;\n    },\n    readPackedFixed32: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readFixed32());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFixed32());\n        return arr;\n    },\n    readPackedSFixed32: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readSFixed32());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSFixed32());\n        return arr;\n    },\n    readPackedFixed64: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readFixed64());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readFixed64());\n        return arr;\n    },\n    readPackedSFixed64: function(arr) {\n        if (this.type !== Pbf.Bytes) return arr.push(this.readSFixed64());\n        var end = readPackedEnd(this);\n        arr = arr || [];\n        while (this.pos < end) arr.push(this.readSFixed64());\n        return arr;\n    },\n\n    skip: function(val) {\n        var type = val & 0x7;\n        if (type === Pbf.Varint) while (this.buf[this.pos++] > 0x7f) {}\n        else if (type === Pbf.Bytes) this.pos = this.readVarint() + this.pos;\n        else if (type === Pbf.Fixed32) this.pos += 4;\n        else if (type === Pbf.Fixed64) this.pos += 8;\n        else throw new Error('Unimplemented type: ' + type);\n    },\n\n    // === WRITING =================================================================\n\n    writeTag: function(tag, type) {\n        this.writeVarint((tag << 3) | type);\n    },\n\n    realloc: function(min) {\n        var length = this.length || 16;\n\n        while (length < this.pos + min) length *= 2;\n\n        if (length !== this.length) {\n            var buf = new Uint8Array(length);\n            buf.set(this.buf);\n            this.buf = buf;\n            this.length = length;\n        }\n    },\n\n    finish: function() {\n        this.length = this.pos;\n        this.pos = 0;\n        return this.buf.subarray(0, this.length);\n    },\n\n    writeFixed32: function(val) {\n        this.realloc(4);\n        writeInt32(this.buf, val, this.pos);\n        this.pos += 4;\n    },\n\n    writeSFixed32: function(val) {\n        this.realloc(4);\n        writeInt32(this.buf, val, this.pos);\n        this.pos += 4;\n    },\n\n    writeFixed64: function(val) {\n        this.realloc(8);\n        writeInt32(this.buf, val & -1, this.pos);\n        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);\n        this.pos += 8;\n    },\n\n    writeSFixed64: function(val) {\n        this.realloc(8);\n        writeInt32(this.buf, val & -1, this.pos);\n        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);\n        this.pos += 8;\n    },\n\n    writeVarint: function(val) {\n        val = +val || 0;\n\n        if (val > 0xfffffff || val < 0) {\n            writeBigVarint(val, this);\n            return;\n        }\n\n        this.realloc(4);\n\n        this.buf[this.pos++] =           val & 0x7f  | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] = ((val >>>= 7) & 0x7f) | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] = ((val >>>= 7) & 0x7f) | (val > 0x7f ? 0x80 : 0); if (val <= 0x7f) return;\n        this.buf[this.pos++] =   (val >>> 7) & 0x7f;\n    },\n\n    writeSVarint: function(val) {\n        this.writeVarint(val < 0 ? -val * 2 - 1 : val * 2);\n    },\n\n    writeBoolean: function(val) {\n        this.writeVarint(Boolean(val));\n    },\n\n    writeString: function(str) {\n        str = String(str);\n        this.realloc(str.length * 4);\n\n        this.pos++; // reserve 1 byte for short string length\n\n        var startPos = this.pos;\n        // write the string directly to the buffer and see how much was written\n        this.pos = writeUtf8(this.buf, str, this.pos);\n        var len = this.pos - startPos;\n\n        if (len >= 0x80) makeRoomForExtraLength(startPos, len, this);\n\n        // finally, write the message length in the reserved place and restore the position\n        this.pos = startPos - 1;\n        this.writeVarint(len);\n        this.pos += len;\n    },\n\n    writeFloat: function(val) {\n        this.realloc(4);\n        ieee754.write(this.buf, val, this.pos, true, 23, 4);\n        this.pos += 4;\n    },\n\n    writeDouble: function(val) {\n        this.realloc(8);\n        ieee754.write(this.buf, val, this.pos, true, 52, 8);\n        this.pos += 8;\n    },\n\n    writeBytes: function(buffer) {\n        var len = buffer.length;\n        this.writeVarint(len);\n        this.realloc(len);\n        for (var i = 0; i < len; i++) this.buf[this.pos++] = buffer[i];\n    },\n\n    writeRawMessage: function(fn, obj) {\n        this.pos++; // reserve 1 byte for short message length\n\n        // write the message directly to the buffer and see how much was written\n        var startPos = this.pos;\n        fn(obj, this);\n        var len = this.pos - startPos;\n\n        if (len >= 0x80) makeRoomForExtraLength(startPos, len, this);\n\n        // finally, write the message length in the reserved place and restore the position\n        this.pos = startPos - 1;\n        this.writeVarint(len);\n        this.pos += len;\n    },\n\n    writeMessage: function(tag, fn, obj) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeRawMessage(fn, obj);\n    },\n\n    writePackedVarint:   function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedVarint, arr);   },\n    writePackedSVarint:  function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedSVarint, arr);  },\n    writePackedBoolean:  function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedBoolean, arr);  },\n    writePackedFloat:    function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedFloat, arr);    },\n    writePackedDouble:   function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedDouble, arr);   },\n    writePackedFixed32:  function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedFixed32, arr);  },\n    writePackedSFixed32: function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedSFixed32, arr); },\n    writePackedFixed64:  function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedFixed64, arr);  },\n    writePackedSFixed64: function(tag, arr) { if (arr.length) this.writeMessage(tag, writePackedSFixed64, arr); },\n\n    writeBytesField: function(tag, buffer) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeBytes(buffer);\n    },\n    writeFixed32Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeFixed32(val);\n    },\n    writeSFixed32Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeSFixed32(val);\n    },\n    writeFixed64Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeFixed64(val);\n    },\n    writeSFixed64Field: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeSFixed64(val);\n    },\n    writeVarintField: function(tag, val) {\n        this.writeTag(tag, Pbf.Varint);\n        this.writeVarint(val);\n    },\n    writeSVarintField: function(tag, val) {\n        this.writeTag(tag, Pbf.Varint);\n        this.writeSVarint(val);\n    },\n    writeStringField: function(tag, str) {\n        this.writeTag(tag, Pbf.Bytes);\n        this.writeString(str);\n    },\n    writeFloatField: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed32);\n        this.writeFloat(val);\n    },\n    writeDoubleField: function(tag, val) {\n        this.writeTag(tag, Pbf.Fixed64);\n        this.writeDouble(val);\n    },\n    writeBooleanField: function(tag, val) {\n        this.writeVarintField(tag, Boolean(val));\n    }\n};\n\nfunction readVarintRemainder(l, s, p) {\n    var buf = p.buf,\n        h, b;\n\n    b = buf[p.pos++]; h  = (b & 0x70) >> 4;  if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 3;  if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 10; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 17; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x7f) << 24; if (b < 0x80) return toNum(l, h, s);\n    b = buf[p.pos++]; h |= (b & 0x01) << 31; if (b < 0x80) return toNum(l, h, s);\n\n    throw new Error('Expected varint not more than 10 bytes');\n}\n\nfunction readPackedEnd(pbf) {\n    return pbf.type === Pbf.Bytes ?\n        pbf.readVarint() + pbf.pos : pbf.pos + 1;\n}\n\nfunction toNum(low, high, isSigned) {\n    if (isSigned) {\n        return high * 0x100000000 + (low >>> 0);\n    }\n\n    return ((high >>> 0) * 0x100000000) + (low >>> 0);\n}\n\nfunction writeBigVarint(val, pbf) {\n    var low, high;\n\n    if (val >= 0) {\n        low  = (val % 0x100000000) | 0;\n        high = (val / 0x100000000) | 0;\n    } else {\n        low  = ~(-val % 0x100000000);\n        high = ~(-val / 0x100000000);\n\n        if (low ^ 0xffffffff) {\n            low = (low + 1) | 0;\n        } else {\n            low = 0;\n            high = (high + 1) | 0;\n        }\n    }\n\n    if (val >= 0x10000000000000000 || val < -0x10000000000000000) {\n        throw new Error('Given varint doesn\\'t fit into 10 bytes');\n    }\n\n    pbf.realloc(10);\n\n    writeBigVarintLow(low, high, pbf);\n    writeBigVarintHigh(high, pbf);\n}\n\nfunction writeBigVarintLow(low, high, pbf) {\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos++] = low & 0x7f | 0x80; low >>>= 7;\n    pbf.buf[pbf.pos]   = low & 0x7f;\n}\n\nfunction writeBigVarintHigh(high, pbf) {\n    var lsb = (high & 0x07) << 4;\n\n    pbf.buf[pbf.pos++] |= lsb         | ((high >>>= 3) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f | ((high >>>= 7) ? 0x80 : 0); if (!high) return;\n    pbf.buf[pbf.pos++]  = high & 0x7f;\n}\n\nfunction makeRoomForExtraLength(startPos, len, pbf) {\n    var extraLen =\n        len <= 0x3fff ? 1 :\n        len <= 0x1fffff ? 2 :\n        len <= 0xfffffff ? 3 : Math.floor(Math.log(len) / (Math.LN2 * 7));\n\n    // if 1 byte isn't enough for encoding message length, shift the data to the right\n    pbf.realloc(extraLen);\n    for (var i = pbf.pos - 1; i >= startPos; i--) pbf.buf[i + extraLen] = pbf.buf[i];\n}\n\nfunction writePackedVarint(arr, pbf)   { for (var i = 0; i < arr.length; i++) pbf.writeVarint(arr[i]);   }\nfunction writePackedSVarint(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeSVarint(arr[i]);  }\nfunction writePackedFloat(arr, pbf)    { for (var i = 0; i < arr.length; i++) pbf.writeFloat(arr[i]);    }\nfunction writePackedDouble(arr, pbf)   { for (var i = 0; i < arr.length; i++) pbf.writeDouble(arr[i]);   }\nfunction writePackedBoolean(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeBoolean(arr[i]);  }\nfunction writePackedFixed32(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeFixed32(arr[i]);  }\nfunction writePackedSFixed32(arr, pbf) { for (var i = 0; i < arr.length; i++) pbf.writeSFixed32(arr[i]); }\nfunction writePackedFixed64(arr, pbf)  { for (var i = 0; i < arr.length; i++) pbf.writeFixed64(arr[i]);  }\nfunction writePackedSFixed64(arr, pbf) { for (var i = 0; i < arr.length; i++) pbf.writeSFixed64(arr[i]); }\n\n// Buffer code below from https://github.com/feross/buffer, MIT-licensed\n\nfunction readUInt32(buf, pos) {\n    return ((buf[pos]) |\n        (buf[pos + 1] << 8) |\n        (buf[pos + 2] << 16)) +\n        (buf[pos + 3] * 0x1000000);\n}\n\nfunction writeInt32(buf, val, pos) {\n    buf[pos] = val;\n    buf[pos + 1] = (val >>> 8);\n    buf[pos + 2] = (val >>> 16);\n    buf[pos + 3] = (val >>> 24);\n}\n\nfunction readInt32(buf, pos) {\n    return ((buf[pos]) |\n        (buf[pos + 1] << 8) |\n        (buf[pos + 2] << 16)) +\n        (buf[pos + 3] << 24);\n}\n\nfunction readUtf8(buf, pos, end) {\n    var str = '';\n    var i = pos;\n\n    while (i < end) {\n        var b0 = buf[i];\n        var c = null; // codepoint\n        var bytesPerSequence =\n            b0 > 0xEF ? 4 :\n            b0 > 0xDF ? 3 :\n            b0 > 0xBF ? 2 : 1;\n\n        if (i + bytesPerSequence > end) break;\n\n        var b1, b2, b3;\n\n        if (bytesPerSequence === 1) {\n            if (b0 < 0x80) {\n                c = b0;\n            }\n        } else if (bytesPerSequence === 2) {\n            b1 = buf[i + 1];\n            if ((b1 & 0xC0) === 0x80) {\n                c = (b0 & 0x1F) << 0x6 | (b1 & 0x3F);\n                if (c <= 0x7F) {\n                    c = null;\n                }\n            }\n        } else if (bytesPerSequence === 3) {\n            b1 = buf[i + 1];\n            b2 = buf[i + 2];\n            if ((b1 & 0xC0) === 0x80 && (b2 & 0xC0) === 0x80) {\n                c = (b0 & 0xF) << 0xC | (b1 & 0x3F) << 0x6 | (b2 & 0x3F);\n                if (c <= 0x7FF || (c >= 0xD800 && c <= 0xDFFF)) {\n                    c = null;\n                }\n            }\n        } else if (bytesPerSequence === 4) {\n            b1 = buf[i + 1];\n            b2 = buf[i + 2];\n            b3 = buf[i + 3];\n            if ((b1 & 0xC0) === 0x80 && (b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n                c = (b0 & 0xF) << 0x12 | (b1 & 0x3F) << 0xC | (b2 & 0x3F) << 0x6 | (b3 & 0x3F);\n                if (c <= 0xFFFF || c >= 0x110000) {\n                    c = null;\n                }\n            }\n        }\n\n        if (c === null) {\n            c = 0xFFFD;\n            bytesPerSequence = 1;\n\n        } else if (c > 0xFFFF) {\n            c -= 0x10000;\n            str += String.fromCharCode(c >>> 10 & 0x3FF | 0xD800);\n            c = 0xDC00 | c & 0x3FF;\n        }\n\n        str += String.fromCharCode(c);\n        i += bytesPerSequence;\n    }\n\n    return str;\n}\n\nfunction readUtf8TextDecoder(buf, pos, end) {\n    return utf8TextDecoder.decode(buf.subarray(pos, end));\n}\n\nfunction writeUtf8(buf, str, pos) {\n    for (var i = 0, c, lead; i < str.length; i++) {\n        c = str.charCodeAt(i); // code point\n\n        if (c > 0xD7FF && c < 0xE000) {\n            if (lead) {\n                if (c < 0xDC00) {\n                    buf[pos++] = 0xEF;\n                    buf[pos++] = 0xBF;\n                    buf[pos++] = 0xBD;\n                    lead = c;\n                    continue;\n                } else {\n                    c = lead - 0xD800 << 10 | c - 0xDC00 | 0x10000;\n                    lead = null;\n                }\n            } else {\n                if (c > 0xDBFF || (i + 1 === str.length)) {\n                    buf[pos++] = 0xEF;\n                    buf[pos++] = 0xBF;\n                    buf[pos++] = 0xBD;\n                } else {\n                    lead = c;\n                }\n                continue;\n            }\n        } else if (lead) {\n            buf[pos++] = 0xEF;\n            buf[pos++] = 0xBF;\n            buf[pos++] = 0xBD;\n            lead = null;\n        }\n\n        if (c < 0x80) {\n            buf[pos++] = c;\n        } else {\n            if (c < 0x800) {\n                buf[pos++] = c >> 0x6 | 0xC0;\n            } else {\n                if (c < 0x10000) {\n                    buf[pos++] = c >> 0xC | 0xE0;\n                } else {\n                    buf[pos++] = c >> 0x12 | 0xF0;\n                    buf[pos++] = c >> 0xC & 0x3F | 0x80;\n                }\n                buf[pos++] = c >> 0x6 & 0x3F | 0x80;\n            }\n            buf[pos++] = c & 0x3F | 0x80;\n        }\n    }\n    return pos;\n}\n", "/**\n * @module ol/format/MVT\n */\n//FIXME Implement projection handling\n\nimport FeatureFormat, {transformGeometryWithOptions} from './Feature.js';\nimport LineString from '../geom/LineString.js';\nimport MultiLineString from '../geom/MultiLineString.js';\nimport MultiPoint from '../geom/MultiPoint.js';\nimport MultiPolygon from '../geom/MultiPolygon.js';\nimport PBF from 'pbf';\nimport Point from '../geom/Point.js';\nimport Polygon from '../geom/Polygon.js';\nimport Projection from '../proj/Projection.js';\nimport RenderFeature from '../render/Feature.js';\nimport {assert} from '../asserts.js';\nimport {get} from '../proj.js';\nimport {inflateEnds} from '../geom/flat/orient.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../Feature.js\").FeatureClass} [featureClass] Class for features returned by\n * {@link module:ol/format/MVT~MVT#readFeatures}. Set to {@link module:ol/Feature~Feature} to get full editing and geometry\n * support at the cost of decreased rendering performance. The default is\n * {@link module:ol/render/Feature~RenderFeature}, which is optimized for rendering and hit detection.\n * @property {string} [geometryName='geometry'] Geometry name to use when creating features.\n * @property {string} [layerName='layer'] Name of the feature attribute that holds the layer name.\n * @property {Array<string>} [layers] Layers to read features from. If not provided, features will be read from all\n * @property {string} [idProperty] Optional property that will be assigned as the feature id and removed from the properties.\n * layers.\n */\n\n/**\n * @classdesc\n * Feature format for reading data in the Mapbox MVT format.\n *\n * @param {Options} [options] Options.\n * @api\n */\nclass MVT extends FeatureFormat {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    options = options ? options : {};\n\n    /**\n     * @type {Projection}\n     */\n    this.dataProjection = new Projection({\n      code: '',\n      units: 'tile-pixels',\n    });\n\n    /**\n     * @private\n     * @type {import(\"../Feature.js\").FeatureClass}\n     */\n    this.featureClass_ = options.featureClass\n      ? options.featureClass\n      : RenderFeature;\n\n    /**\n     * @private\n     * @type {string|undefined}\n     */\n    this.geometryName_ = options.geometryName;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.layerName_ = options.layerName ? options.layerName : 'layer';\n\n    /**\n     * @private\n     * @type {Array<string>|null}\n     */\n    this.layers_ = options.layers ? options.layers : null;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.idProperty_ = options.idProperty;\n\n    this.supportedMediaTypes = [\n      'application/vnd.mapbox-vector-tile',\n      'application/x-protobuf',\n    ];\n  }\n\n  /**\n   * Read the raw geometry from the pbf offset stored in a raw feature's geometry\n   * property.\n   * @param {PBF} pbf PBF.\n   * @param {Object} feature Raw feature.\n   * @param {Array<number>} flatCoordinates Array to store flat coordinates in.\n   * @param {Array<number>} ends Array to store ends in.\n   * @private\n   */\n  readRawGeometry_(pbf, feature, flatCoordinates, ends) {\n    pbf.pos = feature.geometry;\n\n    const end = pbf.readVarint() + pbf.pos;\n    let cmd = 1;\n    let length = 0;\n    let x = 0;\n    let y = 0;\n    let coordsLen = 0;\n    let currentEnd = 0;\n\n    while (pbf.pos < end) {\n      if (!length) {\n        const cmdLen = pbf.readVarint();\n        cmd = cmdLen & 0x7;\n        length = cmdLen >> 3;\n      }\n\n      length--;\n\n      if (cmd === 1 || cmd === 2) {\n        x += pbf.readSVarint();\n        y += pbf.readSVarint();\n\n        if (cmd === 1) {\n          // moveTo\n          if (coordsLen > currentEnd) {\n            ends.push(coordsLen);\n            currentEnd = coordsLen;\n          }\n        }\n\n        flatCoordinates.push(x, y);\n        coordsLen += 2;\n      } else if (cmd === 7) {\n        if (coordsLen > currentEnd) {\n          // close polygon\n          flatCoordinates.push(\n            flatCoordinates[currentEnd],\n            flatCoordinates[currentEnd + 1]\n          );\n          coordsLen += 2;\n        }\n      } else {\n        assert(false, 59); // Invalid command found in the PBF\n      }\n    }\n\n    if (coordsLen > currentEnd) {\n      ends.push(coordsLen);\n      currentEnd = coordsLen;\n    }\n  }\n\n  /**\n   * @private\n   * @param {PBF} pbf PBF\n   * @param {Object} rawFeature Raw Mapbox feature.\n   * @param {import(\"./Feature.js\").ReadOptions} options Read options.\n   * @return {import(\"../Feature.js\").FeatureLike|null} Feature.\n   */\n  createFeature_(pbf, rawFeature, options) {\n    const type = rawFeature.type;\n    if (type === 0) {\n      return null;\n    }\n\n    let feature;\n    const values = rawFeature.properties;\n\n    let id;\n    if (!this.idProperty_) {\n      id = rawFeature.id;\n    } else {\n      id = values[this.idProperty_];\n      delete values[this.idProperty_];\n    }\n\n    values[this.layerName_] = rawFeature.layer.name;\n\n    const flatCoordinates = /** @type {Array<number>} */ ([]);\n    const ends = /** @type {Array<number>} */ ([]);\n    this.readRawGeometry_(pbf, rawFeature, flatCoordinates, ends);\n\n    const geometryType = getGeometryType(type, ends.length);\n\n    if (this.featureClass_ === RenderFeature) {\n      feature = new this.featureClass_(\n        geometryType,\n        flatCoordinates,\n        ends,\n        values,\n        id\n      );\n      feature.transform(options.dataProjection);\n    } else {\n      let geom;\n      if (geometryType == 'Polygon') {\n        const endss = inflateEnds(flatCoordinates, ends);\n        geom =\n          endss.length > 1\n            ? new MultiPolygon(flatCoordinates, 'XY', endss)\n            : new Polygon(flatCoordinates, 'XY', ends);\n      } else {\n        geom =\n          geometryType === 'Point'\n            ? new Point(flatCoordinates, 'XY')\n            : geometryType === 'LineString'\n            ? new LineString(flatCoordinates, 'XY')\n            : geometryType === 'MultiPoint'\n            ? new MultiPoint(flatCoordinates, 'XY')\n            : geometryType === 'MultiLineString'\n            ? new MultiLineString(flatCoordinates, 'XY', ends)\n            : null;\n      }\n      const ctor = /** @type {typeof import(\"../Feature.js\").default} */ (\n        this.featureClass_\n      );\n      feature = new ctor();\n      if (this.geometryName_) {\n        feature.setGeometryName(this.geometryName_);\n      }\n      const geometry = transformGeometryWithOptions(geom, false, options);\n      feature.setGeometry(geometry);\n      if (id !== undefined) {\n        feature.setId(id);\n      }\n      feature.setProperties(values, true);\n    }\n\n    return feature;\n  }\n\n  /**\n   * @return {import(\"./Feature.js\").Type} Format.\n   */\n  getType() {\n    return 'arraybuffer';\n  }\n\n  /**\n   * Read all features.\n   *\n   * @param {ArrayBuffer} source Source.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @return {Array<import(\"../Feature.js\").FeatureLike>} Features.\n   * @api\n   */\n  readFeatures(source, options) {\n    const layers = this.layers_;\n    options = this.adaptOptions(options);\n    const dataProjection = get(options.dataProjection);\n    dataProjection.setWorldExtent(options.extent);\n    options.dataProjection = dataProjection;\n\n    const pbf = new PBF(/** @type {ArrayBuffer} */ (source));\n    const pbfLayers = pbf.readFields(layersPBFReader, {});\n    const features = [];\n    for (const name in pbfLayers) {\n      if (layers && !layers.includes(name)) {\n        continue;\n      }\n      const pbfLayer = pbfLayers[name];\n\n      const extent = pbfLayer ? [0, 0, pbfLayer.extent, pbfLayer.extent] : null;\n      dataProjection.setExtent(extent);\n\n      for (let i = 0, ii = pbfLayer.length; i < ii; ++i) {\n        const rawFeature = readRawFeature(pbf, pbfLayer, i);\n        const feature = this.createFeature_(pbf, rawFeature, options);\n        if (feature !== null) {\n          features.push(feature);\n        }\n      }\n    }\n\n    return features;\n  }\n\n  /**\n   * Read the projection from the source.\n   *\n   * @param {Document|Element|Object|string} source Source.\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   * @api\n   */\n  readProjection(source) {\n    return this.dataProjection;\n  }\n\n  /**\n   * Sets the layers that features will be read from.\n   * @param {Array<string>} layers Layers.\n   * @api\n   */\n  setLayers(layers) {\n    this.layers_ = layers;\n  }\n}\n\n/**\n * Reader callback for parsing layers.\n * @param {number} tag The tag.\n * @param {Object} layers The layers object.\n * @param {PBF} pbf The PBF.\n */\nfunction layersPBFReader(tag, layers, pbf) {\n  if (tag === 3) {\n    const layer = {\n      keys: [],\n      values: [],\n      features: [],\n    };\n    const end = pbf.readVarint() + pbf.pos;\n    pbf.readFields(layerPBFReader, layer, end);\n    layer.length = layer.features.length;\n    if (layer.length) {\n      layers[layer.name] = layer;\n    }\n  }\n}\n\n/**\n * Reader callback for parsing layer.\n * @param {number} tag The tag.\n * @param {Object} layer The layer object.\n * @param {PBF} pbf The PBF.\n */\nfunction layerPBFReader(tag, layer, pbf) {\n  if (tag === 15) {\n    layer.version = pbf.readVarint();\n  } else if (tag === 1) {\n    layer.name = pbf.readString();\n  } else if (tag === 5) {\n    layer.extent = pbf.readVarint();\n  } else if (tag === 2) {\n    layer.features.push(pbf.pos);\n  } else if (tag === 3) {\n    layer.keys.push(pbf.readString());\n  } else if (tag === 4) {\n    let value = null;\n    const end = pbf.readVarint() + pbf.pos;\n    while (pbf.pos < end) {\n      tag = pbf.readVarint() >> 3;\n      value =\n        tag === 1\n          ? pbf.readString()\n          : tag === 2\n          ? pbf.readFloat()\n          : tag === 3\n          ? pbf.readDouble()\n          : tag === 4\n          ? pbf.readVarint64()\n          : tag === 5\n          ? pbf.readVarint()\n          : tag === 6\n          ? pbf.readSVarint()\n          : tag === 7\n          ? pbf.readBoolean()\n          : null;\n    }\n    layer.values.push(value);\n  }\n}\n\n/**\n * Reader callback for parsing feature.\n * @param {number} tag The tag.\n * @param {Object} feature The feature object.\n * @param {PBF} pbf The PBF.\n */\nfunction featurePBFReader(tag, feature, pbf) {\n  if (tag == 1) {\n    feature.id = pbf.readVarint();\n  } else if (tag == 2) {\n    const end = pbf.readVarint() + pbf.pos;\n    while (pbf.pos < end) {\n      const key = feature.layer.keys[pbf.readVarint()];\n      const value = feature.layer.values[pbf.readVarint()];\n      feature.properties[key] = value;\n    }\n  } else if (tag == 3) {\n    feature.type = pbf.readVarint();\n  } else if (tag == 4) {\n    feature.geometry = pbf.pos;\n  }\n}\n\n/**\n * Read a raw feature from the pbf offset stored at index `i` in the raw layer.\n * @param {PBF} pbf PBF.\n * @param {Object} layer Raw layer.\n * @param {number} i Index of the feature in the raw layer's `features` array.\n * @return {Object} Raw feature.\n */\nfunction readRawFeature(pbf, layer, i) {\n  pbf.pos = layer.features[i];\n  const end = pbf.readVarint() + pbf.pos;\n\n  const feature = {\n    layer: layer,\n    type: 0,\n    properties: {},\n  };\n  pbf.readFields(featurePBFReader, feature, end);\n  return feature;\n}\n\n/**\n * @param {number} type The raw feature's geometry type\n * @param {number} numEnds Number of ends of the flat coordinates of the\n * geometry.\n * @return {import(\"../geom/Geometry.js\").Type} The geometry type.\n */\nfunction getGeometryType(type, numEnds) {\n  /** @type {import(\"../geom/Geometry.js\").Type} */\n  let geometryType;\n  if (type === 1) {\n    geometryType = numEnds === 1 ? 'Point' : 'MultiPoint';\n  } else if (type === 2) {\n    geometryType = numEnds === 1 ? 'LineString' : 'MultiLineString';\n  } else if (type === 3) {\n    geometryType = 'Polygon';\n    // MultiPolygon not relevant for rendering - winding order determines\n    // outer rings of polygons.\n  }\n  return geometryType;\n}\n\nexport default MVT;\n", "/**\n * @module ol/render/Feature\n */\nimport Feature from '../Feature.js';\nimport {\n  LineString,\n  MultiLineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from '../geom.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../transform.js';\nimport {\n  createOrUpdateFromCoordinate,\n  createOrUpdateFromFlatCoordinates,\n  getCenter,\n  getHeight,\n} from '../extent.js';\nimport {extend} from '../array.js';\nimport {\n  getInteriorPointOfArray,\n  getInteriorPointsOfMultiArray,\n} from '../geom/flat/interiorpoint.js';\nimport {get as getProjection} from '../proj.js';\nimport {inflateEnds} from '../geom/flat/orient.js';\nimport {interpolatePoint} from '../geom/flat/interpolate.js';\nimport {linearRingss as linearRingssCenter} from '../geom/flat/center.js';\nimport {transform2D} from '../geom/flat/transform.js';\n\n/**\n * @type {import(\"../transform.js\").Transform}\n */\nconst tmpTransform = createTransform();\n\n/**\n * Lightweight, read-only, {@link module:ol/Feature~Feature} and {@link module:ol/geom/Geometry~Geometry} like\n * structure, optimized for vector tile rendering and styling. Geometry access\n * through the API is limited to getting the type and extent of the geometry.\n */\nclass RenderFeature {\n  /**\n   * @param {import(\"../geom/Geometry.js\").Type} type Geometry type.\n   * @param {Array<number>} flatCoordinates Flat coordinates. These always need\n   *     to be right-handed for polygons.\n   * @param {Array<number>|Array<Array<number>>} ends Ends or Endss.\n   * @param {Object<string, *>} properties Properties.\n   * @param {number|string|undefined} id Feature id.\n   */\n  constructor(type, flatCoordinates, ends, properties, id) {\n    /**\n     * @type {import(\"../style/Style.js\").StyleFunction|undefined}\n     */\n    this.styleFunction;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent|undefined}\n     */\n    this.extent_;\n\n    /**\n     * @private\n     * @type {number|string|undefined}\n     */\n    this.id_ = id;\n\n    /**\n     * @private\n     * @type {import(\"../geom/Geometry.js\").Type}\n     */\n    this.type_ = type;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatCoordinates_ = flatCoordinates;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatInteriorPoints_ = null;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatMidpoints_ = null;\n\n    /**\n     * @private\n     * @type {Array<number>|Array<Array<number>>}\n     */\n    this.ends_ = ends;\n\n    /**\n     * @private\n     * @type {Object<string, *>}\n     */\n    this.properties_ = properties;\n  }\n\n  /**\n   * Get a feature property by its key.\n   * @param {string} key Key\n   * @return {*} Value for the requested key.\n   * @api\n   */\n  get(key) {\n    return this.properties_[key];\n  }\n\n  /**\n   * Get the extent of this feature's geometry.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent() {\n    if (!this.extent_) {\n      this.extent_ =\n        this.type_ === 'Point'\n          ? createOrUpdateFromCoordinate(this.flatCoordinates_)\n          : createOrUpdateFromFlatCoordinates(\n              this.flatCoordinates_,\n              0,\n              this.flatCoordinates_.length,\n              2\n            );\n    }\n    return this.extent_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoint() {\n    if (!this.flatInteriorPoints_) {\n      const flatCenter = getCenter(this.getExtent());\n      this.flatInteriorPoints_ = getInteriorPointOfArray(\n        this.flatCoordinates_,\n        0,\n        /** @type {Array<number>} */ (this.ends_),\n        2,\n        flatCenter,\n        0\n      );\n    }\n    return this.flatInteriorPoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoints() {\n    if (!this.flatInteriorPoints_) {\n      const flatCenters = linearRingssCenter(\n        this.flatCoordinates_,\n        0,\n        /** @type {Array<Array<number>>} */ (this.ends_),\n        2\n      );\n      this.flatInteriorPoints_ = getInteriorPointsOfMultiArray(\n        this.flatCoordinates_,\n        0,\n        /** @type {Array<Array<number>>} */ (this.ends_),\n        2,\n        flatCenters\n      );\n    }\n    return this.flatInteriorPoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoint.\n   */\n  getFlatMidpoint() {\n    if (!this.flatMidpoints_) {\n      this.flatMidpoints_ = interpolatePoint(\n        this.flatCoordinates_,\n        0,\n        this.flatCoordinates_.length,\n        2,\n        0.5\n      );\n    }\n    return this.flatMidpoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoints.\n   */\n  getFlatMidpoints() {\n    if (!this.flatMidpoints_) {\n      this.flatMidpoints_ = [];\n      const flatCoordinates = this.flatCoordinates_;\n      let offset = 0;\n      const ends = /** @type {Array<number>} */ (this.ends_);\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        const end = ends[i];\n        const midpoint = interpolatePoint(flatCoordinates, offset, end, 2, 0.5);\n        extend(this.flatMidpoints_, midpoint);\n        offset = end;\n      }\n    }\n    return this.flatMidpoints_;\n  }\n\n  /**\n   * Get the feature identifier.  This is a stable identifier for the feature and\n   * is set when reading data from a remote source.\n   * @return {number|string|undefined} Id.\n   * @api\n   */\n  getId() {\n    return this.id_;\n  }\n\n  /**\n   * @return {Array<number>} Flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    return this.flatCoordinates_;\n  }\n\n  /**\n   * For API compatibility with {@link module:ol/Feature~Feature}, this method is useful when\n   * determining the geometry type in style function (see {@link #getType}).\n   * @return {RenderFeature} Feature.\n   * @api\n   */\n  getGeometry() {\n    return this;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {RenderFeature} Simplified geometry.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    return this;\n  }\n\n  /**\n   * Get a transformed and simplified version of the geometry.\n   * @abstract\n   * @param {number} squaredTolerance Squared tolerance.\n   * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n   * @return {RenderFeature} Simplified geometry.\n   */\n  simplifyTransformed(squaredTolerance, transform) {\n    return this;\n  }\n\n  /**\n   * Get the feature properties.\n   * @return {Object<string, *>} Feature properties.\n   * @api\n   */\n  getProperties() {\n    return this.properties_;\n  }\n\n  /**\n   * @return {number} Stride.\n   */\n  getStride() {\n    return 2;\n  }\n\n  /**\n   * @return {import('../style/Style.js').StyleFunction|undefined} Style\n   */\n  getStyleFunction() {\n    return this.styleFunction;\n  }\n\n  /**\n   * Get the type of this feature's geometry.\n   * @return {import(\"../geom/Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return this.type_;\n  }\n\n  /**\n   * Transform geometry coordinates from tile pixel space to projected.\n   *\n   * @param {import(\"../proj.js\").ProjectionLike} projection The data projection\n   */\n  transform(projection) {\n    projection = getProjection(projection);\n    const pixelExtent = projection.getExtent();\n    const projectedExtent = projection.getWorldExtent();\n    if (pixelExtent && projectedExtent) {\n      const scale = getHeight(projectedExtent) / getHeight(pixelExtent);\n      composeTransform(\n        tmpTransform,\n        projectedExtent[0],\n        projectedExtent[3],\n        scale,\n        -scale,\n        0,\n        0,\n        0\n      );\n      transform2D(\n        this.flatCoordinates_,\n        0,\n        this.flatCoordinates_.length,\n        2,\n        tmpTransform,\n        this.flatCoordinates_\n      );\n    }\n  }\n  /**\n   * @return {Array<number>|Array<Array<number>>} Ends or endss.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n}\n\nRenderFeature.prototype.getEndss = RenderFeature.prototype.getEnds;\n\n/**\n * @return {Array<number>} Flat coordinates.\n */\nRenderFeature.prototype.getFlatCoordinates =\n  RenderFeature.prototype.getOrientedFlatCoordinates;\n\n/**\n * Create a geometry from an `ol/render/Feature`\n * @param {RenderFeature} renderFeature\n * Render Feature\n * @return {Point|MultiPoint|LineString|MultiLineString|Polygon|MultiPolygon}\n * New geometry instance.\n * @api\n */\nexport function toGeometry(renderFeature) {\n  const geometryType = renderFeature.getType();\n  switch (geometryType) {\n    case 'Point':\n      return new Point(renderFeature.getFlatCoordinates());\n    case 'MultiPoint':\n      return new MultiPoint(renderFeature.getFlatCoordinates(), 'XY');\n    case 'LineString':\n      return new LineString(renderFeature.getFlatCoordinates(), 'XY');\n    case 'MultiLineString':\n      return new MultiLineString(\n        renderFeature.getFlatCoordinates(),\n        'XY',\n        /** @type {Array<number>} */ (renderFeature.getEnds())\n      );\n    case 'Polygon':\n      const flatCoordinates = renderFeature.getFlatCoordinates();\n      const ends = /** @type {Array<number>} */ (renderFeature.getEnds());\n      const endss = inflateEnds(flatCoordinates, ends);\n      return endss.length > 1\n        ? new MultiPolygon(flatCoordinates, 'XY', endss)\n        : new Polygon(flatCoordinates, 'XY', ends);\n    default:\n      throw new Error('Invalid geometry type:' + geometryType);\n  }\n}\n\n/**\n * Create an `ol/Feature` from an `ol/render/Feature`\n * @param {RenderFeature} renderFeature RenderFeature\n * @param {string} [geometryName='geometry'] Geometry name to use\n * when creating the Feature.\n * @return {Feature} Newly constructed `ol/Feature` with properties,\n * geometry, and id copied over.\n * @api\n */\nexport function toFeature(renderFeature, geometryName) {\n  const id = renderFeature.getId();\n  const geometry = toGeometry(renderFeature);\n  const properties = renderFeature.getProperties();\n  const feature = new Feature();\n  if (geometryName !== undefined) {\n    feature.setGeometryName(geometryName);\n  }\n  feature.setGeometry(geometry);\n  if (id !== undefined) {\n    feature.setId(id);\n  }\n  feature.setProperties(properties, true);\n  return feature;\n}\n\nexport default RenderFeature;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AACA,YAAQ,OAAO,SAAU,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC3D,UAAI,GAAG;AACP,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ;AACZ,UAAI,IAAI,OAAQ,SAAS,IAAK;AAC9B,UAAI,IAAI,OAAO,KAAK;AACpB,UAAI,IAAI,OAAO,SAAS,CAAC;AAEzB,WAAK;AAEL,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,MAAC;AAE3E,UAAI,MAAM,GAAG;AACX,YAAI,IAAI;AAAA,MACV,WAAW,MAAM,MAAM;AACrB,eAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,YAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,YAAI,IAAI;AAAA,MACV;AACA,cAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AAAA,IAChD;AAEA,YAAQ,QAAQ,SAAU,QAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACnE,UAAI,GAAG,GAAG;AACV,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,UAAI,IAAI,OAAO,IAAK,SAAS;AAC7B,UAAI,IAAI,OAAO,IAAI;AACnB,UAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,cAAQ,KAAK,IAAI,KAAK;AAEtB,UAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,YAAI,MAAM,KAAK,IAAI,IAAI;AACvB,YAAI;AAAA,MACN,OAAO;AACL,YAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,YAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,eAAK;AAAA,QACP;AACA,YAAI,IAAI,SAAS,GAAG;AAClB,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,mBAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,QACrC;AACA,YAAI,QAAQ,KAAK,GAAG;AAClB;AACA,eAAK;AAAA,QACP;AAEA,YAAI,IAAI,SAAS,MAAM;AACrB,cAAI;AACJ,cAAI;AAAA,QACN,WAAW,IAAI,SAAS,GAAG;AACzB,eAAM,QAAQ,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI;AACxC,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,cAAI;AAAA,QACN;AAAA,MACF;AAEA,aAAO,QAAQ,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE/E,UAAK,KAAK,OAAQ;AAClB,cAAQ;AACR,aAAO,OAAO,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,MAAC;AAE9E,aAAO,SAAS,IAAI,CAAC,KAAK,IAAI;AAAA,IAChC;AAAA;AAAA;;;ACpFA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,QAAI,UAAU;AAEd,aAAS,IAAI,KAAK;AACd,WAAK,MAAM,YAAY,UAAU,YAAY,OAAO,GAAG,IAAI,MAAM,IAAI,WAAW,OAAO,CAAC;AACxF,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,SAAS,KAAK,IAAI;AAAA,IAC3B;AAEA,QAAI,SAAU;AACd,QAAI,UAAU;AACd,QAAI,QAAU;AACd,QAAI,UAAU;AAEd,QAAI,iBAAiB,KAAK,OAAO,KAAK;AAAtC,QACI,iBAAiB,IAAI;AAIzB,QAAI,0BAA0B;AAC9B,QAAI,kBAAkB,OAAO,gBAAgB,cAAc,OAAO,IAAI,YAAY,MAAM;AAExF,QAAI,YAAY;AAAA,MAEZ,SAAS,WAAW;AAChB,aAAK,MAAM;AAAA,MACf;AAAA;AAAA,MAIA,YAAY,SAAS,WAAW,QAAQ,KAAK;AACzC,cAAM,OAAO,KAAK;AAElB,eAAO,KAAK,MAAM,KAAK;AACnB,cAAI,MAAM,KAAK,WAAW,GACtB,MAAM,OAAO,GACb,WAAW,KAAK;AAEpB,eAAK,OAAO,MAAM;AAClB,oBAAU,KAAK,QAAQ,IAAI;AAE3B,cAAI,KAAK,QAAQ,SAAU,MAAK,KAAK,GAAG;AAAA,QAC5C;AACA,eAAO;AAAA,MACX;AAAA,MAEA,aAAa,SAAS,WAAW,QAAQ;AACrC,eAAO,KAAK,WAAW,WAAW,QAAQ,KAAK,WAAW,IAAI,KAAK,GAAG;AAAA,MAC1E;AAAA,MAEA,aAAa,WAAW;AACpB,YAAI,MAAM,WAAW,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,MAEA,cAAc,WAAW;AACrB,YAAI,MAAM,UAAU,KAAK,KAAK,KAAK,GAAG;AACtC,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA;AAAA,MAIA,aAAa,WAAW;AACpB,YAAI,MAAM,WAAW,KAAK,KAAK,KAAK,GAAG,IAAI,WAAW,KAAK,KAAK,KAAK,MAAM,CAAC,IAAI;AAChF,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,MAEA,cAAc,WAAW;AACrB,YAAI,MAAM,WAAW,KAAK,KAAK,KAAK,GAAG,IAAI,UAAU,KAAK,KAAK,KAAK,MAAM,CAAC,IAAI;AAC/E,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,MAEA,WAAW,WAAW;AAClB,YAAI,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC;AACtD,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,MAEA,YAAY,WAAW;AACnB,YAAI,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC;AACtD,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAAA,MAEA,YAAY,SAAS,UAAU;AAC3B,YAAI,MAAM,KAAK,KACX,KAAK;AAET,YAAI,IAAI,KAAK,KAAK;AAAG,cAAQ,IAAI;AAAa,YAAI,IAAI,IAAM,QAAO;AACnE,YAAI,IAAI,KAAK,KAAK;AAAG,gBAAQ,IAAI,QAAS;AAAI,YAAI,IAAI,IAAM,QAAO;AACnE,YAAI,IAAI,KAAK,KAAK;AAAG,gBAAQ,IAAI,QAAS;AAAI,YAAI,IAAI,IAAM,QAAO;AACnE,YAAI,IAAI,KAAK,KAAK;AAAG,gBAAQ,IAAI,QAAS;AAAI,YAAI,IAAI,IAAM,QAAO;AACnE,YAAI,IAAI,KAAK,GAAG;AAAK,gBAAQ,IAAI,OAAS;AAE1C,eAAO,oBAAoB,KAAK,UAAU,IAAI;AAAA,MAClD;AAAA,MAEA,cAAc,WAAW;AACrB,eAAO,KAAK,WAAW,IAAI;AAAA,MAC/B;AAAA,MAEA,aAAa,WAAW;AACpB,YAAI,MAAM,KAAK,WAAW;AAC1B,eAAO,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,MAClD;AAAA,MAEA,aAAa,WAAW;AACpB,eAAO,QAAQ,KAAK,WAAW,CAAC;AAAA,MACpC;AAAA,MAEA,YAAY,WAAW;AACnB,YAAI,MAAM,KAAK,WAAW,IAAI,KAAK;AACnC,YAAI,MAAM,KAAK;AACf,aAAK,MAAM;AAEX,YAAI,MAAM,OAAO,2BAA2B,iBAAiB;AAEzD,iBAAO,oBAAoB,KAAK,KAAK,KAAK,GAAG;AAAA,QACjD;AAEA,eAAO,SAAS,KAAK,KAAK,KAAK,GAAG;AAAA,MACtC;AAAA,MAEA,WAAW,WAAW;AAClB,YAAI,MAAM,KAAK,WAAW,IAAI,KAAK,KAC/B,SAAS,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG;AAC5C,aAAK,MAAM;AACX,eAAO;AAAA,MACX;AAAA;AAAA,MAIA,kBAAkB,SAAS,KAAK,UAAU;AACtC,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,WAAW,QAAQ,CAAC;AACtE,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,WAAW,QAAQ,CAAC;AACzD,eAAO;AAAA,MACX;AAAA,MACA,mBAAmB,SAAS,KAAK;AAC7B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,YAAY,CAAC;AAC/D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,YAAY,CAAC;AAClD,eAAO;AAAA,MACX;AAAA,MACA,mBAAmB,SAAS,KAAK;AAC7B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,YAAY,CAAC;AAC/D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,YAAY,CAAC;AAClD,eAAO;AAAA,MACX;AAAA,MACA,iBAAiB,SAAS,KAAK;AAC3B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,UAAU,CAAC;AAC7D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,UAAU,CAAC;AAChD,eAAO;AAAA,MACX;AAAA,MACA,kBAAkB,SAAS,KAAK;AAC5B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,WAAW,CAAC;AAC9D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,WAAW,CAAC;AACjD,eAAO;AAAA,MACX;AAAA,MACA,mBAAmB,SAAS,KAAK;AAC7B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,YAAY,CAAC;AAC/D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,YAAY,CAAC;AAClD,eAAO;AAAA,MACX;AAAA,MACA,oBAAoB,SAAS,KAAK;AAC9B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,aAAa,CAAC;AAChE,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,aAAa,CAAC;AACnD,eAAO;AAAA,MACX;AAAA,MACA,mBAAmB,SAAS,KAAK;AAC7B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,YAAY,CAAC;AAC/D,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,YAAY,CAAC;AAClD,eAAO;AAAA,MACX;AAAA,MACA,oBAAoB,SAAS,KAAK;AAC9B,YAAI,KAAK,SAAS,IAAI,MAAO,QAAO,IAAI,KAAK,KAAK,aAAa,CAAC;AAChE,YAAI,MAAM,cAAc,IAAI;AAC5B,cAAM,OAAO,CAAC;AACd,eAAO,KAAK,MAAM,IAAK,KAAI,KAAK,KAAK,aAAa,CAAC;AACnD,eAAO;AAAA,MACX;AAAA,MAEA,MAAM,SAAS,KAAK;AAChB,YAAI,OAAO,MAAM;AACjB,YAAI,SAAS,IAAI,OAAQ,QAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAM;AAAA,QAAC;AAAA,iBACrD,SAAS,IAAI,MAAO,MAAK,MAAM,KAAK,WAAW,IAAI,KAAK;AAAA,iBACxD,SAAS,IAAI,QAAS,MAAK,OAAO;AAAA,iBAClC,SAAS,IAAI,QAAS,MAAK,OAAO;AAAA,YACtC,OAAM,IAAI,MAAM,yBAAyB,IAAI;AAAA,MACtD;AAAA;AAAA,MAIA,UAAU,SAAS,KAAK,MAAM;AAC1B,aAAK,YAAa,OAAO,IAAK,IAAI;AAAA,MACtC;AAAA,MAEA,SAAS,SAAS,KAAK;AACnB,YAAI,SAAS,KAAK,UAAU;AAE5B,eAAO,SAAS,KAAK,MAAM,IAAK,WAAU;AAE1C,YAAI,WAAW,KAAK,QAAQ;AACxB,cAAI,MAAM,IAAI,WAAW,MAAM;AAC/B,cAAI,IAAI,KAAK,GAAG;AAChB,eAAK,MAAM;AACX,eAAK,SAAS;AAAA,QAClB;AAAA,MACJ;AAAA,MAEA,QAAQ,WAAW;AACf,aAAK,SAAS,KAAK;AACnB,aAAK,MAAM;AACX,eAAO,KAAK,IAAI,SAAS,GAAG,KAAK,MAAM;AAAA,MAC3C;AAAA,MAEA,cAAc,SAAS,KAAK;AACxB,aAAK,QAAQ,CAAC;AACd,mBAAW,KAAK,KAAK,KAAK,KAAK,GAAG;AAClC,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,eAAe,SAAS,KAAK;AACzB,aAAK,QAAQ,CAAC;AACd,mBAAW,KAAK,KAAK,KAAK,KAAK,GAAG;AAClC,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,cAAc,SAAS,KAAK;AACxB,aAAK,QAAQ,CAAC;AACd,mBAAW,KAAK,KAAK,MAAM,IAAI,KAAK,GAAG;AACvC,mBAAW,KAAK,KAAK,KAAK,MAAM,MAAM,cAAc,GAAG,KAAK,MAAM,CAAC;AACnE,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,eAAe,SAAS,KAAK;AACzB,aAAK,QAAQ,CAAC;AACd,mBAAW,KAAK,KAAK,MAAM,IAAI,KAAK,GAAG;AACvC,mBAAW,KAAK,KAAK,KAAK,MAAM,MAAM,cAAc,GAAG,KAAK,MAAM,CAAC;AACnE,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,aAAa,SAAS,KAAK;AACvB,cAAM,CAAC,OAAO;AAEd,YAAI,MAAM,aAAa,MAAM,GAAG;AAC5B,yBAAe,KAAK,IAAI;AACxB;AAAA,QACJ;AAEA,aAAK,QAAQ,CAAC;AAEd,aAAK,IAAI,KAAK,KAAK,IAAc,MAAM,OAAS,MAAM,MAAO,MAAO;AAAI,YAAI,OAAO,IAAM;AACzF,aAAK,IAAI,KAAK,KAAK,KAAM,SAAS,KAAK,OAAS,MAAM,MAAO,MAAO;AAAI,YAAI,OAAO,IAAM;AACzF,aAAK,IAAI,KAAK,KAAK,KAAM,SAAS,KAAK,OAAS,MAAM,MAAO,MAAO;AAAI,YAAI,OAAO,IAAM;AACzF,aAAK,IAAI,KAAK,KAAK,IAAO,QAAQ,IAAK;AAAA,MAC3C;AAAA,MAEA,cAAc,SAAS,KAAK;AACxB,aAAK,YAAY,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,CAAC;AAAA,MACrD;AAAA,MAEA,cAAc,SAAS,KAAK;AACxB,aAAK,YAAY,QAAQ,GAAG,CAAC;AAAA,MACjC;AAAA,MAEA,aAAa,SAAS,KAAK;AACvB,cAAM,OAAO,GAAG;AAChB,aAAK,QAAQ,IAAI,SAAS,CAAC;AAE3B,aAAK;AAEL,YAAI,WAAW,KAAK;AAEpB,aAAK,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,GAAG;AAC5C,YAAI,MAAM,KAAK,MAAM;AAErB,YAAI,OAAO,IAAM,wBAAuB,UAAU,KAAK,IAAI;AAG3D,aAAK,MAAM,WAAW;AACtB,aAAK,YAAY,GAAG;AACpB,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,YAAY,SAAS,KAAK;AACtB,aAAK,QAAQ,CAAC;AACd,gBAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC;AAClD,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,aAAa,SAAS,KAAK;AACvB,aAAK,QAAQ,CAAC;AACd,gBAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC;AAClD,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,YAAY,SAAS,QAAQ;AACzB,YAAI,MAAM,OAAO;AACjB,aAAK,YAAY,GAAG;AACpB,aAAK,QAAQ,GAAG;AAChB,iBAAS,IAAI,GAAG,IAAI,KAAK,IAAK,MAAK,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC;AAAA,MACjE;AAAA,MAEA,iBAAiB,SAAS,IAAI,KAAK;AAC/B,aAAK;AAGL,YAAI,WAAW,KAAK;AACpB,WAAG,KAAK,IAAI;AACZ,YAAI,MAAM,KAAK,MAAM;AAErB,YAAI,OAAO,IAAM,wBAAuB,UAAU,KAAK,IAAI;AAG3D,aAAK,MAAM,WAAW;AACtB,aAAK,YAAY,GAAG;AACpB,aAAK,OAAO;AAAA,MAChB;AAAA,MAEA,cAAc,SAAS,KAAK,IAAI,KAAK;AACjC,aAAK,SAAS,KAAK,IAAI,KAAK;AAC5B,aAAK,gBAAgB,IAAI,GAAG;AAAA,MAChC;AAAA,MAEA,mBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,mBAAmB,GAAG;AAAA,MAAK;AAAA,MAC5G,oBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,oBAAoB,GAAG;AAAA,MAAI;AAAA,MAC5G,oBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,oBAAoB,GAAG;AAAA,MAAI;AAAA,MAC5G,kBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,kBAAkB,GAAG;AAAA,MAAM;AAAA,MAC5G,mBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,mBAAmB,GAAG;AAAA,MAAK;AAAA,MAC5G,oBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,oBAAoB,GAAG;AAAA,MAAI;AAAA,MAC5G,qBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,qBAAqB,GAAG;AAAA,MAAG;AAAA,MAC5G,oBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,oBAAoB,GAAG;AAAA,MAAI;AAAA,MAC5G,qBAAqB,SAAS,KAAK,KAAK;AAAE,YAAI,IAAI,OAAQ,MAAK,aAAa,KAAK,qBAAqB,GAAG;AAAA,MAAG;AAAA,MAE5G,iBAAiB,SAAS,KAAK,QAAQ;AACnC,aAAK,SAAS,KAAK,IAAI,KAAK;AAC5B,aAAK,WAAW,MAAM;AAAA,MAC1B;AAAA,MACA,mBAAmB,SAAS,KAAK,KAAK;AAClC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,aAAa,GAAG;AAAA,MACzB;AAAA,MACA,oBAAoB,SAAS,KAAK,KAAK;AACnC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,cAAc,GAAG;AAAA,MAC1B;AAAA,MACA,mBAAmB,SAAS,KAAK,KAAK;AAClC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,aAAa,GAAG;AAAA,MACzB;AAAA,MACA,oBAAoB,SAAS,KAAK,KAAK;AACnC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,cAAc,GAAG;AAAA,MAC1B;AAAA,MACA,kBAAkB,SAAS,KAAK,KAAK;AACjC,aAAK,SAAS,KAAK,IAAI,MAAM;AAC7B,aAAK,YAAY,GAAG;AAAA,MACxB;AAAA,MACA,mBAAmB,SAAS,KAAK,KAAK;AAClC,aAAK,SAAS,KAAK,IAAI,MAAM;AAC7B,aAAK,aAAa,GAAG;AAAA,MACzB;AAAA,MACA,kBAAkB,SAAS,KAAK,KAAK;AACjC,aAAK,SAAS,KAAK,IAAI,KAAK;AAC5B,aAAK,YAAY,GAAG;AAAA,MACxB;AAAA,MACA,iBAAiB,SAAS,KAAK,KAAK;AAChC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,WAAW,GAAG;AAAA,MACvB;AAAA,MACA,kBAAkB,SAAS,KAAK,KAAK;AACjC,aAAK,SAAS,KAAK,IAAI,OAAO;AAC9B,aAAK,YAAY,GAAG;AAAA,MACxB;AAAA,MACA,mBAAmB,SAAS,KAAK,KAAK;AAClC,aAAK,iBAAiB,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC3C;AAAA,IACJ;AAEA,aAAS,oBAAoB,GAAG,GAAG,GAAG;AAClC,UAAI,MAAM,EAAE,KACR,GAAG;AAEP,UAAI,IAAI,EAAE,KAAK;AAAG,WAAM,IAAI,QAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAC3E,UAAI,IAAI,EAAE,KAAK;AAAG,YAAM,IAAI,QAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAC3E,UAAI,IAAI,EAAE,KAAK;AAAG,YAAM,IAAI,QAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAC3E,UAAI,IAAI,EAAE,KAAK;AAAG,YAAM,IAAI,QAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAC3E,UAAI,IAAI,EAAE,KAAK;AAAG,YAAM,IAAI,QAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAC3E,UAAI,IAAI,EAAE,KAAK;AAAG,YAAM,IAAI,MAAS;AAAI,UAAI,IAAI,IAAM,QAAO,MAAM,GAAG,GAAG,CAAC;AAE3E,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC5D;AAEA,aAAS,cAAc,KAAK;AACxB,aAAO,IAAI,SAAS,IAAI,QACpB,IAAI,WAAW,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC/C;AAEA,aAAS,MAAM,KAAK,MAAM,UAAU;AAChC,UAAI,UAAU;AACV,eAAO,OAAO,cAAe,QAAQ;AAAA,MACzC;AAEA,cAAS,SAAS,KAAK,cAAgB,QAAQ;AAAA,IACnD;AAEA,aAAS,eAAe,KAAK,KAAK;AAC9B,UAAI,KAAK;AAET,UAAI,OAAO,GAAG;AACV,cAAQ,MAAM,aAAe;AAC7B,eAAQ,MAAM,aAAe;AAAA,MACjC,OAAO;AACH,cAAO,EAAE,CAAC,MAAM;AAChB,eAAO,EAAE,CAAC,MAAM;AAEhB,YAAI,MAAM,YAAY;AAClB,gBAAO,MAAM,IAAK;AAAA,QACtB,OAAO;AACH,gBAAM;AACN,iBAAQ,OAAO,IAAK;AAAA,QACxB;AAAA,MACJ;AAEA,UAAI,OAAO,uBAAuB,MAAM,sBAAsB;AAC1D,cAAM,IAAI,MAAM,wCAAyC;AAAA,MAC7D;AAEA,UAAI,QAAQ,EAAE;AAEd,wBAAkB,KAAK,MAAM,GAAG;AAChC,yBAAmB,MAAM,GAAG;AAAA,IAChC;AAEA,aAAS,kBAAkB,KAAK,MAAM,KAAK;AACvC,UAAI,IAAI,IAAI,KAAK,IAAI,MAAM,MAAO;AAAM,eAAS;AACjD,UAAI,IAAI,IAAI,KAAK,IAAI,MAAM,MAAO;AAAM,eAAS;AACjD,UAAI,IAAI,IAAI,KAAK,IAAI,MAAM,MAAO;AAAM,eAAS;AACjD,UAAI,IAAI,IAAI,KAAK,IAAI,MAAM,MAAO;AAAM,eAAS;AACjD,UAAI,IAAI,IAAI,GAAG,IAAM,MAAM;AAAA,IAC/B;AAEA,aAAS,mBAAmB,MAAM,KAAK;AACnC,UAAI,OAAO,OAAO,MAAS;AAE3B,UAAI,IAAI,IAAI,KAAK,KAAK,QAAgB,UAAU,KAAK,MAAO;AAAI,UAAI,CAAC,KAAM;AAC3E,UAAI,IAAI,IAAI,KAAK,IAAK,OAAO,QAAS,UAAU,KAAK,MAAO;AAAI,UAAI,CAAC,KAAM;AAC3E,UAAI,IAAI,IAAI,KAAK,IAAK,OAAO,QAAS,UAAU,KAAK,MAAO;AAAI,UAAI,CAAC,KAAM;AAC3E,UAAI,IAAI,IAAI,KAAK,IAAK,OAAO,QAAS,UAAU,KAAK,MAAO;AAAI,UAAI,CAAC,KAAM;AAC3E,UAAI,IAAI,IAAI,KAAK,IAAK,OAAO,QAAS,UAAU,KAAK,MAAO;AAAI,UAAI,CAAC,KAAM;AAC3E,UAAI,IAAI,IAAI,KAAK,IAAK,OAAO;AAAA,IACjC;AAEA,aAAS,uBAAuB,UAAU,KAAK,KAAK;AAChD,UAAI,WACA,OAAO,QAAS,IAChB,OAAO,UAAW,IAClB,OAAO,YAAY,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE;AAGpE,UAAI,QAAQ,QAAQ;AACpB,eAAS,IAAI,IAAI,MAAM,GAAG,KAAK,UAAU,IAAK,KAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC;AAAA,IACnF;AAEA,aAAS,kBAAkB,KAAK,KAAO;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,YAAY,IAAI,CAAC,CAAC;AAAA,IAAK;AACzG,aAAS,mBAAmB,KAAK,KAAM;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,aAAa,IAAI,CAAC,CAAC;AAAA,IAAI;AACzG,aAAS,iBAAiB,KAAK,KAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,WAAW,IAAI,CAAC,CAAC;AAAA,IAAM;AACzG,aAAS,kBAAkB,KAAK,KAAO;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,YAAY,IAAI,CAAC,CAAC;AAAA,IAAK;AACzG,aAAS,mBAAmB,KAAK,KAAM;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,aAAa,IAAI,CAAC,CAAC;AAAA,IAAI;AACzG,aAAS,mBAAmB,KAAK,KAAM;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,aAAa,IAAI,CAAC,CAAC;AAAA,IAAI;AACzG,aAAS,oBAAoB,KAAK,KAAK;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,cAAc,IAAI,CAAC,CAAC;AAAA,IAAG;AACzG,aAAS,mBAAmB,KAAK,KAAM;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,aAAa,IAAI,CAAC,CAAC;AAAA,IAAI;AACzG,aAAS,oBAAoB,KAAK,KAAK;AAAE,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,cAAc,IAAI,CAAC,CAAC;AAAA,IAAG;AAIzG,aAAS,WAAW,KAAK,KAAK;AAC1B,cAAS,IAAI,GAAG,IACX,IAAI,MAAM,CAAC,KAAK,IAChB,IAAI,MAAM,CAAC,KAAK,MAChB,IAAI,MAAM,CAAC,IAAI;AAAA,IACxB;AAEA,aAAS,WAAW,KAAK,KAAK,KAAK;AAC/B,UAAI,GAAG,IAAI;AACX,UAAI,MAAM,CAAC,IAAK,QAAQ;AACxB,UAAI,MAAM,CAAC,IAAK,QAAQ;AACxB,UAAI,MAAM,CAAC,IAAK,QAAQ;AAAA,IAC5B;AAEA,aAAS,UAAU,KAAK,KAAK;AACzB,cAAS,IAAI,GAAG,IACX,IAAI,MAAM,CAAC,KAAK,IAChB,IAAI,MAAM,CAAC,KAAK,OAChB,IAAI,MAAM,CAAC,KAAK;AAAA,IACzB;AAEA,aAAS,SAAS,KAAK,KAAK,KAAK;AAC7B,UAAI,MAAM;AACV,UAAI,IAAI;AAER,aAAO,IAAI,KAAK;AACZ,YAAI,KAAK,IAAI,CAAC;AACd,YAAI,IAAI;AACR,YAAI,mBACA,KAAK,MAAO,IACZ,KAAK,MAAO,IACZ,KAAK,MAAO,IAAI;AAEpB,YAAI,IAAI,mBAAmB,IAAK;AAEhC,YAAI,IAAI,IAAI;AAEZ,YAAI,qBAAqB,GAAG;AACxB,cAAI,KAAK,KAAM;AACX,gBAAI;AAAA,UACR;AAAA,QACJ,WAAW,qBAAqB,GAAG;AAC/B,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,KAAK,SAAU,KAAM;AACtB,iBAAK,KAAK,OAAS,IAAO,KAAK;AAC/B,gBAAI,KAAK,KAAM;AACX,kBAAI;AAAA,YACR;AAAA,UACJ;AAAA,QACJ,WAAW,qBAAqB,GAAG;AAC/B,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AAC9C,iBAAK,KAAK,OAAQ,MAAO,KAAK,OAAS,IAAO,KAAK;AACnD,gBAAI,KAAK,QAAU,KAAK,SAAU,KAAK,OAAS;AAC5C,kBAAI;AAAA,YACR;AAAA,UACJ;AAAA,QACJ,WAAW,qBAAqB,GAAG;AAC/B,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,IAAI,IAAI,CAAC;AACd,eAAK,KAAK,SAAU,QAAS,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AACtE,iBAAK,KAAK,OAAQ,MAAQ,KAAK,OAAS,MAAO,KAAK,OAAS,IAAO,KAAK;AACzE,gBAAI,KAAK,SAAU,KAAK,SAAU;AAC9B,kBAAI;AAAA,YACR;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,MAAM,MAAM;AACZ,cAAI;AACJ,6BAAmB;AAAA,QAEvB,WAAW,IAAI,OAAQ;AACnB,eAAK;AACL,iBAAO,OAAO,aAAa,MAAM,KAAK,OAAQ,KAAM;AACpD,cAAI,QAAS,IAAI;AAAA,QACrB;AAEA,eAAO,OAAO,aAAa,CAAC;AAC5B,aAAK;AAAA,MACT;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,oBAAoB,KAAK,KAAK,KAAK;AACxC,aAAO,gBAAgB,OAAO,IAAI,SAAS,KAAK,GAAG,CAAC;AAAA,IACxD;AAEA,aAAS,UAAU,KAAK,KAAK,KAAK;AAC9B,eAAS,IAAI,GAAG,GAAG,MAAM,IAAI,IAAI,QAAQ,KAAK;AAC1C,YAAI,IAAI,WAAW,CAAC;AAEpB,YAAI,IAAI,SAAU,IAAI,OAAQ;AAC1B,cAAI,MAAM;AACN,gBAAI,IAAI,OAAQ;AACZ,kBAAI,KAAK,IAAI;AACb,kBAAI,KAAK,IAAI;AACb,kBAAI,KAAK,IAAI;AACb,qBAAO;AACP;AAAA,YACJ,OAAO;AACH,kBAAI,OAAO,SAAU,KAAK,IAAI,QAAS;AACvC,qBAAO;AAAA,YACX;AAAA,UACJ,OAAO;AACH,gBAAI,IAAI,SAAW,IAAI,MAAM,IAAI,QAAS;AACtC,kBAAI,KAAK,IAAI;AACb,kBAAI,KAAK,IAAI;AACb,kBAAI,KAAK,IAAI;AAAA,YACjB,OAAO;AACH,qBAAO;AAAA,YACX;AACA;AAAA,UACJ;AAAA,QACJ,WAAW,MAAM;AACb,cAAI,KAAK,IAAI;AACb,cAAI,KAAK,IAAI;AACb,cAAI,KAAK,IAAI;AACb,iBAAO;AAAA,QACX;AAEA,YAAI,IAAI,KAAM;AACV,cAAI,KAAK,IAAI;AAAA,QACjB,OAAO;AACH,cAAI,IAAI,MAAO;AACX,gBAAI,KAAK,IAAI,KAAK,IAAM;AAAA,UAC5B,OAAO;AACH,gBAAI,IAAI,OAAS;AACb,kBAAI,KAAK,IAAI,KAAK,KAAM;AAAA,YAC5B,OAAO;AACH,kBAAI,KAAK,IAAI,KAAK,KAAO;AACzB,kBAAI,KAAK,IAAI,KAAK,KAAM,KAAO;AAAA,YACnC;AACA,gBAAI,KAAK,IAAI,KAAK,IAAM,KAAO;AAAA,UACnC;AACA,cAAI,KAAK,IAAI,IAAI,KAAO;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACvnBA,iBAAgB;;;AC0BhB,IAAM,eAAe,OAAgB;AAOrC,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,YAAY,MAAM,iBAAiB,MAAM,YAAY,IAAI;AAIvD,SAAK;AAML,SAAK;AAML,SAAK,MAAM;AAMX,SAAK,QAAQ;AAMb,SAAK,mBAAmB;AAMxB,SAAK,sBAAsB;AAM3B,SAAK,iBAAiB;AAMtB,SAAK,QAAQ;AAMb,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK;AACP,WAAO,KAAK,YAAY,GAAG;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UACH,KAAK,UAAU,UACX,6BAA6B,KAAK,gBAAgB,IAClD;AAAA,QACE,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,MACF;AAAA,IACR;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,aAAa,UAAU,KAAK,UAAU,CAAC;AAC7C,WAAK,sBAAsB;AAAA,QACzB,KAAK;AAAA,QACL;AAAA;AAAA,QAC8B,KAAK;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,QACL;AAAA;AAAA,QACqC,KAAK;AAAA,QAC1C;AAAA,MACF;AACA,WAAK,sBAAsB;AAAA,QACzB,KAAK;AAAA,QACL;AAAA;AAAA,QACqC,KAAK;AAAA,QAC1C;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB;AAAA,QACpB,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,CAAC;AACvB,YAAM,kBAAkB,KAAK;AAC7B,UAAI,SAAS;AACb,YAAM;AAAA;AAAA,QAAqC,KAAK;AAAA;AAChD,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,WAAW,iBAAiB,iBAAiB,QAAQ,KAAK,GAAG,GAAG;AACtE,eAAO,KAAK,gBAAgB,QAAQ;AACpC,iBAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,kBAAkB;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,kBAAkB,WAAW;AAC/C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,YAAY;AACpB,iBAAa,IAAc,UAAU;AACrC,UAAM,cAAc,WAAW,UAAU;AACzC,UAAM,kBAAkB,WAAW,eAAe;AAClD,QAAI,eAAe,iBAAiB;AAClC,YAAM,QAAQ,UAAU,eAAe,IAAI,UAAU,WAAW;AAChE;AAAA,QACE;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,CAAC;AAAA,QACjB;AAAA,QACA,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA;AAAA,QACE,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;AAEA,cAAc,UAAU,WAAW,cAAc,UAAU;AAK3D,cAAc,UAAU,qBACtB,cAAc,UAAU;AA8D1B,IAAOA,mBAAQ;;;ADtWf,IAAM,MAAN,cAAkB,gBAAc;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,UAAU,UAAU,CAAC;AAK/B,SAAK,iBAAiB,IAAI,mBAAW;AAAA,MACnC,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAMD,SAAK,gBAAgB,QAAQ,eACzB,QAAQ,eACRC;AAMJ,SAAK,gBAAgB,QAAQ;AAM7B,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAMjD,SAAK,cAAc,QAAQ;AAE3B,SAAK,sBAAsB;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,KAAK,SAAS,iBAAiB,MAAM;AACpD,QAAI,MAAM,QAAQ;AAElB,UAAM,MAAM,IAAI,WAAW,IAAI,IAAI;AACnC,QAAI,MAAM;AACV,QAAI,SAAS;AACb,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,YAAY;AAChB,QAAI,aAAa;AAEjB,WAAO,IAAI,MAAM,KAAK;AACpB,UAAI,CAAC,QAAQ;AACX,cAAM,SAAS,IAAI,WAAW;AAC9B,cAAM,SAAS;AACf,iBAAS,UAAU;AAAA,MACrB;AAEA;AAEA,UAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,aAAK,IAAI,YAAY;AACrB,aAAK,IAAI,YAAY;AAErB,YAAI,QAAQ,GAAG;AAEb,cAAI,YAAY,YAAY;AAC1B,iBAAK,KAAK,SAAS;AACnB,yBAAa;AAAA,UACf;AAAA,QACF;AAEA,wBAAgB,KAAK,GAAG,CAAC;AACzB,qBAAa;AAAA,MACf,WAAW,QAAQ,GAAG;AACpB,YAAI,YAAY,YAAY;AAE1B,0BAAgB;AAAA,YACd,gBAAgB,UAAU;AAAA,YAC1B,gBAAgB,aAAa,CAAC;AAAA,UAChC;AACA,uBAAa;AAAA,QACf;AAAA,MACF,OAAO;AACL,eAAO,OAAO,EAAE;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,YAAY,YAAY;AAC1B,WAAK,KAAK,SAAS;AACnB,mBAAa;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,KAAK,YAAY,SAAS;AACvC,UAAM,OAAO,WAAW;AACxB,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAEA,QAAI;AACJ,UAAM,SAAS,WAAW;AAE1B,QAAI;AACJ,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,OAAO,KAAK,WAAW;AAC5B,aAAO,OAAO,KAAK,WAAW;AAAA,IAChC;AAEA,WAAO,KAAK,UAAU,IAAI,WAAW,MAAM;AAE3C,UAAM;AAAA;AAAA,MAAgD,CAAC;AAAA;AACvD,UAAM;AAAA;AAAA,MAAqC,CAAC;AAAA;AAC5C,SAAK,iBAAiB,KAAK,YAAY,iBAAiB,IAAI;AAE5D,UAAM,eAAe,gBAAgB,MAAM,KAAK,MAAM;AAEtD,QAAI,KAAK,kBAAkBA,kBAAe;AACxC,gBAAU,IAAI,KAAK;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,cAAQ,UAAU,QAAQ,cAAc;AAAA,IAC1C,OAAO;AACL,UAAI;AACJ,UAAI,gBAAgB,WAAW;AAC7B,cAAM,QAAQ,YAAY,iBAAiB,IAAI;AAC/C,eACE,MAAM,SAAS,IACX,IAAI,qBAAa,iBAAiB,MAAM,KAAK,IAC7C,IAAI,gBAAQ,iBAAiB,MAAM,IAAI;AAAA,MAC/C,OAAO;AACL,eACE,iBAAiB,UACb,IAAI,cAAM,iBAAiB,IAAI,IAC/B,iBAAiB,eACjB,IAAI,mBAAW,iBAAiB,IAAI,IACpC,iBAAiB,eACjB,IAAI,mBAAW,iBAAiB,IAAI,IACpC,iBAAiB,oBACjB,IAAI,wBAAgB,iBAAiB,MAAM,IAAI,IAC/C;AAAA,MACR;AACA,YAAM;AAAA;AAAA,QACJ,KAAK;AAAA;AAEP,gBAAU,IAAI,KAAK;AACnB,UAAI,KAAK,eAAe;AACtB,gBAAQ,gBAAgB,KAAK,aAAa;AAAA,MAC5C;AACA,YAAM,WAAW,6BAA6B,MAAM,OAAO,OAAO;AAClE,cAAQ,YAAY,QAAQ;AAC5B,UAAI,OAAO,QAAW;AACpB,gBAAQ,MAAM,EAAE;AAAA,MAClB;AACA,cAAQ,cAAc,QAAQ,IAAI;AAAA,IACpC;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,QAAQ,SAAS;AAC5B,UAAM,SAAS,KAAK;AACpB,cAAU,KAAK,aAAa,OAAO;AACnC,UAAM,iBAAiB,IAAI,QAAQ,cAAc;AACjD,mBAAe,eAAe,QAAQ,MAAM;AAC5C,YAAQ,iBAAiB;AAEzB,UAAM,MAAM,IAAI,WAAAC;AAAA;AAAA,MAAgC;AAAA,IAAO;AACvD,UAAM,YAAY,IAAI,WAAW,iBAAiB,CAAC,CAAC;AACpD,UAAM,WAAW,CAAC;AAClB,eAAW,QAAQ,WAAW;AAC5B,UAAI,UAAU,CAAC,OAAO,SAAS,IAAI,GAAG;AACpC;AAAA,MACF;AACA,YAAM,WAAW,UAAU,IAAI;AAE/B,YAAM,SAAS,WAAW,CAAC,GAAG,GAAG,SAAS,QAAQ,SAAS,MAAM,IAAI;AACrE,qBAAe,UAAU,MAAM;AAE/B,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,aAAa,eAAe,KAAK,UAAU,CAAC;AAClD,cAAM,UAAU,KAAK,eAAe,KAAK,YAAY,OAAO;AAC5D,YAAI,YAAY,MAAM;AACpB,mBAAS,KAAK,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,QAAQ;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AACF;AAQA,SAAS,gBAAgB,KAAK,QAAQ,KAAK;AACzC,MAAI,QAAQ,GAAG;AACb,UAAM,QAAQ;AAAA,MACZ,MAAM,CAAC;AAAA,MACP,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,IACb;AACA,UAAM,MAAM,IAAI,WAAW,IAAI,IAAI;AACnC,QAAI,WAAW,gBAAgB,OAAO,GAAG;AACzC,UAAM,SAAS,MAAM,SAAS;AAC9B,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,IAAI,IAAI;AAAA,IACvB;AAAA,EACF;AACF;AAQA,SAAS,eAAe,KAAK,OAAO,KAAK;AACvC,MAAI,QAAQ,IAAI;AACd,UAAM,UAAU,IAAI,WAAW;AAAA,EACjC,WAAW,QAAQ,GAAG;AACpB,UAAM,OAAO,IAAI,WAAW;AAAA,EAC9B,WAAW,QAAQ,GAAG;AACpB,UAAM,SAAS,IAAI,WAAW;AAAA,EAChC,WAAW,QAAQ,GAAG;AACpB,UAAM,SAAS,KAAK,IAAI,GAAG;AAAA,EAC7B,WAAW,QAAQ,GAAG;AACpB,UAAM,KAAK,KAAK,IAAI,WAAW,CAAC;AAAA,EAClC,WAAW,QAAQ,GAAG;AACpB,QAAI,QAAQ;AACZ,UAAM,MAAM,IAAI,WAAW,IAAI,IAAI;AACnC,WAAO,IAAI,MAAM,KAAK;AACpB,YAAM,IAAI,WAAW,KAAK;AAC1B,cACE,QAAQ,IACJ,IAAI,WAAW,IACf,QAAQ,IACR,IAAI,UAAU,IACd,QAAQ,IACR,IAAI,WAAW,IACf,QAAQ,IACR,IAAI,aAAa,IACjB,QAAQ,IACR,IAAI,WAAW,IACf,QAAQ,IACR,IAAI,YAAY,IAChB,QAAQ,IACR,IAAI,YAAY,IAChB;AAAA,IACR;AACA,UAAM,OAAO,KAAK,KAAK;AAAA,EACzB;AACF;AAQA,SAAS,iBAAiB,KAAK,SAAS,KAAK;AAC3C,MAAI,OAAO,GAAG;AACZ,YAAQ,KAAK,IAAI,WAAW;AAAA,EAC9B,WAAW,OAAO,GAAG;AACnB,UAAM,MAAM,IAAI,WAAW,IAAI,IAAI;AACnC,WAAO,IAAI,MAAM,KAAK;AACpB,YAAM,MAAM,QAAQ,MAAM,KAAK,IAAI,WAAW,CAAC;AAC/C,YAAM,QAAQ,QAAQ,MAAM,OAAO,IAAI,WAAW,CAAC;AACnD,cAAQ,WAAW,GAAG,IAAI;AAAA,IAC5B;AAAA,EACF,WAAW,OAAO,GAAG;AACnB,YAAQ,OAAO,IAAI,WAAW;AAAA,EAChC,WAAW,OAAO,GAAG;AACnB,YAAQ,WAAW,IAAI;AAAA,EACzB;AACF;AASA,SAAS,eAAe,KAAK,OAAO,GAAG;AACrC,MAAI,MAAM,MAAM,SAAS,CAAC;AAC1B,QAAM,MAAM,IAAI,WAAW,IAAI,IAAI;AAEnC,QAAM,UAAU;AAAA,IACd;AAAA,IACA,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,EACf;AACA,MAAI,WAAW,kBAAkB,SAAS,GAAG;AAC7C,SAAO;AACT;AAQA,SAAS,gBAAgB,MAAM,SAAS;AAEtC,MAAI;AACJ,MAAI,SAAS,GAAG;AACd,mBAAe,YAAY,IAAI,UAAU;AAAA,EAC3C,WAAW,SAAS,GAAG;AACrB,mBAAe,YAAY,IAAI,eAAe;AAAA,EAChD,WAAW,SAAS,GAAG;AACrB,mBAAe;AAAA,EAGjB;AACA,SAAO;AACT;AAEA,IAAO,cAAQ;", "names": ["Feature_default", "Feature_default", "PBF"]}