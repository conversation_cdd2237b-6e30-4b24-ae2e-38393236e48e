{"version": 3, "sources": ["../../ol/style/flat.js", "../../ol/layer/BaseVector.js", "../../ol/render/canvas/Instruction.js", "../../ol/render/VectorContext.js", "../../ol/render/canvas/Builder.js", "../../ol/render/canvas/ImageBuilder.js", "../../ol/render/canvas/LineStringBuilder.js", "../../ol/render/canvas/PolygonBuilder.js", "../../ol/geom/flat/linechunk.js", "../../ol/geom/flat/straightchunk.js", "../../ol/render/canvas/TextBuilder.js", "../../ol/render/canvas/BuilderGroup.js", "../../ol/geom/flat/textpath.js", "../../ol/render/canvas/Executor.js", "../../ol/render/canvas/ExecutorGroup.js", "../../ol/render/canvas/Immediate.js", "../../ol/render/canvas/hitdetect.js", "../../ol/renderer/vector.js"], "sourcesContent": ["/**\n * @module ol/style/flat\n */\n\nimport Circle from '../style/Circle.js';\nimport Fill from './Fill.js';\nimport Icon from './Icon.js';\nimport RegularShape from './RegularShape.js';\nimport Stroke from './Stroke.js';\nimport Style from './Style.js';\nimport Text from './Text.js';\n\n/**\n * For static styling, the [layer.setStyle()]{@link module:ol/layer/Vector~VectorLayer#setStyle} method\n * can be called with an object literal that has fill, stroke, text, icon, regular shape, and/or circle properties.\n * @api\n *\n * @typedef {FlatFill & FlatStroke & FlatText & FlatIcon & FlatShape & FlatCircle} FlatStyle\n */\n\n/**\n * A flat style literal or an array of the same.\n *\n * @typedef {FlatStyle|Array<FlatStyle>} FlatStyleLike\n */\n\n/**\n * Fill style properties applied to polygon features.\n *\n * @typedef {Object} FlatFill\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [fill-color] The fill color.\n */\n\n/**\n * Stroke style properties applied to line strings and polygon boundaries.  To apply a stroke, at least one of\n * `stroke-color` or `stroke-width` must be provided.\n *\n * @typedef {Object} FlatStroke\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [stroke-color] The stroke color.\n * @property {number} [stroke-width] Stroke pixel width.\n * @property {CanvasLineCap} [stroke-line-cap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [stroke-line-join='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [stroke-line-dash] Line dash pattern.\n * @property {number} [stroke-line-dash-offset=0] Line dash offset.\n * @property {number} [stroke-miter-limit=10] Miter limit.\n */\n\n/**\n * Label style properties applied to all features.  At a minimum, a `text-value` must be provided.\n *\n * @typedef {Object} FlatText\n * @property {string|Array<string>} [text-value] Text content or rich text content. For plain text provide a string, which can\n * contain line breaks (`\\n`). For rich text provide an array of text/font tuples. A tuple consists of the text to\n * render and the font to use (or `''` to use the text style's font). A line break has to be a separate tuple (i.e. `'\\n', ''`).\n * **Example:** `['foo', 'bold 10px sans-serif', ' bar', 'italic 10px sans-serif', ' baz', '']` will yield \"**foo** *bar* baz\".\n * **Note:** Rich text is not supported for `'text-placement': 'line'` or the immediate rendering API.\n * @property {string} [text-font] Font style as CSS `font` value, see:\n * https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font. Default is `'10px sans-serif'`\n * @property {number} [text-max-angle=Math.PI/4] When `text-placement` is set to `'line'`, allow a maximum angle between adjacent characters.\n * The expected value is in radians, and the default is 45° (`Math.PI / 4`).\n * @property {number} [text-offset-x=0] Horizontal text offset in pixels. A positive will shift the text right.\n * @property {number} [text-offset-y=0] Vertical text offset in pixels. A positive will shift the text down.\n * @property {boolean} [text-overflow=false] For polygon labels or when `placement` is set to `'line'`, allow text to exceed\n * the width of the polygon at the label position or the length of the path that it follows.\n * @property {import(\"./Text.js\").TextPlacement} [text-placement='point'] Text placement.\n * @property {number} [text-repeat] Repeat interval in pixels. When set, the text will be repeated at this interval. Only available when\n * `text-placement` is set to `'line'`. Overrides `text-align`.\n * @property {number|import(\"../size.js\").Size} [text-scale] Scale.\n * @property {boolean} [text-rotate-with-view=false] Whether to rotate the text with the view.\n * @property {number} [text-rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {CanvasTextAlign} [text-align] Text alignment. Possible values: `'left'`, `'right'`, `'center'`, `'end'` or `'start'`.\n * Default is `'center'` for `'text-placement': 'point'`. For `'text-placement': 'line'`, the default is to let the renderer choose a\n * placement where `text-max-angle` is not exceeded.\n * @property {import('./Text.js').TextJustify} [text-justify] Text justification within the text box.\n * If not set, text is justified towards the `textAlign` anchor.\n * Otherwise, use options `'left'`, `'center'`, or `'right'` to justify the text within the text box.\n * **Note:** `text-justify` is ignored for immediate rendering and also for `'text-placement': 'line'`.\n * @property {CanvasTextBaseline} [text-baseline='middle'] Text base line. Possible values: `'bottom'`, `'top'`, `'middle'`, `'alphabetic'`,\n * `'hanging'`, `'ideographic'`.\n * @property {Array<number>} [text-padding=[0, 0, 0, 0]] Padding in pixels around the text for decluttering and background. The order of\n * values in the array is `[top, right, bottom, left]`.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [text-fill-color] The fill color. Specify `'none'` to avoid hit detection on the fill.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [text-background-fill-color] The fill color.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [text-stroke-color] The stroke color.\n * @property {CanvasLineCap} [text-stroke-line-cap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [text-stroke-line-join='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [text-stroke-line-dash] Line dash pattern.\n * @property {number} [text-stroke-line-dash-offset=0] Line dash offset.\n * @property {number} [text-stroke-miter-limit=10] Miter limit.\n * @property {number} [text-stroke-width] Stroke pixel width.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [text-background-stroke-color] The stroke color.\n * @property {CanvasLineCap} [text-background-stroke-line-cap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [text-background-stroke-line-join='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [text-background-stroke-line-dash] Line dash pattern.\n * @property {number} [text-background-stroke-line-dash-offset=0] Line dash offset.\n * @property {number} [text-background-stroke-miter-limit=10] Miter limit.\n * @property {number} [text-background-stroke-width] Stroke pixel width.\n */\n\n/**\n * Icon style properties applied to point features.  One of `icon-src` or `icon-img` must be provided to render\n * points with an icon.\n *\n * @typedef {Object} FlatIcon\n * @property {string} [icon-src] Image source URI.\n * @property {HTMLImageElement|HTMLCanvasElement} [icon-img] Image object for the icon. If the `icon-src` option is not provided then the\n * provided image must already be loaded. And in that case, it is required\n * to provide the size of the image, with the `icon-img-size` option.\n * @property {import(\"../size.js\").Size} [icon-img-size] Image size in pixels. Only required if `icon-img` is set and `icon-src` is not.\n * The provided size needs to match the actual size of the image.\n * @property {Array<number>} [icon-anchor=[0.5, 0.5]] Anchor. Default value is the icon center.\n * @property {import(\"./Icon.js\").IconOrigin} [icon-anchor-origin='top-left'] Origin of the anchor: `bottom-left`, `bottom-right`,\n * `top-left` or `top-right`.\n * @property {import(\"./Icon.js\").IconAnchorUnits} [icon-anchor-x-units='fraction'] Units in which the anchor x value is\n * specified. A value of `'fraction'` indicates the x value is a fraction of the icon. A value of `'pixels'` indicates\n * the x value in pixels.\n * @property {import(\"./Icon.js\").IconAnchorUnits} [icon-anchor-y-units='fraction'] Units in which the anchor y value is\n * specified. A value of `'fraction'` indicates the y value is a fraction of the icon. A value of `'pixels'` indicates\n * the y value in pixels.\n * @property {import(\"../color.js\").Color|string} [icon-color] Color to tint the icon. If not specified,\n * the icon will be left as is.\n * @property {null|string} [icon-cross-origin] The `crossOrigin` attribute for loaded images. Note that you must provide a\n * `icon-cross-origin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {Array<number>} [icon-offset=[0, 0]] Offset, which, together with the size and the offset origin, define the\n * sub-rectangle to use from the original icon image.\n * @property {Array<number>} [icon-displacement=[0,0]] Displacement of the icon.\n * @property {import(\"./Icon.js\").IconOrigin} [icon-offset-origin='top-left'] Origin of the offset: `bottom-left`, `bottom-right`,\n * `top-left` or `top-right`.\n * @property {number} [icon-opacity=1] Opacity of the icon.\n * @property {number|import(\"../size.js\").Size} [icon-scale=1] Scale.\n * @property {number} [icon-width] Width of the icon. If not specified, the actual image width will be used. Cannot be combined\n * with `scale`.\n * @property {number} [icon-height] Height of the icon. If not specified, the actual image height will be used. Cannot be combined\n * with `scale`.\n * @property {number} [icon-rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {boolean} [icon-rotate-with-view=false] Whether to rotate the icon with the view.\n * @property {import(\"../size.js\").Size} [icon-size] Icon size in pixel. Can be used together with `icon-offset` to define the\n * sub-rectangle to use from the origin (sprite) icon image.\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [icon-declutter-mode] Declutter mode\n */\n\n/**\n * Regular shape style properties for rendering point features.  At least `shape-points` must be provided.\n *\n * @typedef {Object} FlatShape\n * @property {number} [shape-points] Number of points for stars and regular polygons. In case of a polygon, the number of points\n * is the number of sides.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [shape-fill-color] The fill color.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [shape-stroke-color] The stroke color.\n * @property {number} [shape-stroke-width] Stroke pixel width.\n * @property {CanvasLineCap} [shape-stroke-line-cap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [shape-stroke-line-join='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [shape-stroke-line-dash] Line dash pattern.\n * @property {number} [shape-stroke-line-dash-offset=0] Line dash offset.\n * @property {number} [shape-stroke-miter-limit=10] Miter limit.\n * @property {number} [shape-radius] Radius of a regular polygon.\n * @property {number} [shape-radius1] First radius of a star. Ignored if radius is set.\n * @property {number} [shape-radius2] Second radius of a star.\n * @property {number} [shape-angle=0] Shape's angle in radians. A value of 0 will have one of the shape's point facing up.\n * @property {Array<number>} [shape-displacement=[0,0]] Displacement of the shape\n * @property {number} [shape-rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {boolean} [shape-rotate-with-view=false] Whether to rotate the shape with the view.\n * @property {number|import(\"../size.js\").Size} [shape-scale=1] Scale. Unless two dimensional scaling is required a better\n * result may be obtained with appropriate settings for `shape-radius`, `shape-radius1` and `shape-radius2`.\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [shape-declutter-mode] Declutter mode.\n */\n\n/**\n * Circle style properties for rendering point features.  At least `circle-radius` must be provided.\n *\n * @typedef {Object} FlatCircle\n * @property {number} [circle-radius] Circle radius.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [circle-fill-color] The fill color.\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [circle-stroke-color] The stroke color.\n * @property {number} [circle-stroke-width] Stroke pixel width.\n * @property {CanvasLineCap} [circle-stroke-line-cap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [circle-stroke-line-join='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [circle-stroke-line-dash] Line dash pattern.\n * @property {number} [circle-stroke-line-dash-offset=0] Line dash offset.\n * @property {number} [circle-stroke-miter-limit=10] Miter limit.\n * @property {Array<number>} [circle-displacement=[0,0]] displacement\n * @property {number|import(\"../size.js\").Size} [circle-scale=1] Scale. A two dimensional scale will produce an ellipse.\n * Unless two dimensional scaling is required a better result may be obtained with an appropriate setting for `circle-radius`.\n * @property {number} [circle-rotation=0] Rotation in radians\n * (positive rotation clockwise, meaningful only when used in conjunction with a two dimensional scale).\n * @property {boolean} [circle-rotate-with-view=false] Whether to rotate the shape with the view\n * (meaningful only when used in conjunction with a two dimensional scale).\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [circle-declutter-mode] Declutter mode\n */\n\n/**\n * @param {FlatStyle} flatStyle A flat style literal.\n * @return {import(\"./Style.js\").default} A style instance.\n */\nexport function toStyle(flatStyle) {\n  const style = new Style({\n    fill: getFill(flatStyle, ''),\n    stroke: getStroke(flatStyle, ''),\n    text: getText(flatStyle),\n    image: getImage(flatStyle),\n  });\n\n  return style;\n}\n\n/**\n * @param {FlatStyle} flatStyle The flat style.\n * @param {string} prefix The property prefix.\n * @return {Fill|null|undefined} The fill (if any).\n */\nfunction getFill(flatStyle, prefix) {\n  const color = flatStyle[prefix + 'fill-color'];\n  if (!color) {\n    return;\n  }\n  if (color === 'none') {\n    return null;\n  }\n\n  return new Fill({color: color});\n}\n\n/**\n * @param {FlatStyle} flatStyle The flat style.\n * @param {string} prefix The property prefix.\n * @return {Stroke|undefined} The stroke (if any).\n */\nfunction getStroke(flatStyle, prefix) {\n  const width = flatStyle[prefix + 'stroke-width'];\n  const color = flatStyle[prefix + 'stroke-color'];\n  if (!width && !color) {\n    return;\n  }\n\n  return new Stroke({\n    width: width,\n    color: color,\n    lineCap: flatStyle[prefix + 'stroke-line-cap'],\n    lineJoin: flatStyle[prefix + 'stroke-line-join'],\n    lineDash: flatStyle[prefix + 'stroke-line-dash'],\n    lineDashOffset: flatStyle[prefix + 'stroke-line-dash-offset'],\n    miterLimit: flatStyle[prefix + 'stroke-miter-limit'],\n  });\n}\n\n/**\n * @param {FlatStyle} flatStyle The flat style.\n * @return {Text|undefined} The text (if any).\n */\nfunction getText(flatStyle) {\n  const value = flatStyle['text-value'];\n  if (!value) {\n    return;\n  }\n\n  const text = new Text({\n    text: value,\n    font: flatStyle['text-font'],\n    maxAngle: flatStyle['text-max-angle'],\n    offsetX: flatStyle['text-offset-x'],\n    offsetY: flatStyle['text-offset-y'],\n    overflow: flatStyle['text-overflow'],\n    placement: flatStyle['text-placement'],\n    repeat: flatStyle['text-repeat'],\n    scale: flatStyle['text-scale'],\n    rotateWithView: flatStyle['text-rotate-with-view'],\n    rotation: flatStyle['text-rotation'],\n    textAlign: flatStyle['text-align'],\n    justify: flatStyle['text-justify'],\n    textBaseline: flatStyle['text-baseline'],\n    padding: flatStyle['text-padding'],\n    fill: getFill(flatStyle, 'text-'),\n    backgroundFill: getFill(flatStyle, 'text-background-'),\n    stroke: getStroke(flatStyle, 'text-'),\n    backgroundStroke: getStroke(flatStyle, 'text-background-'),\n  });\n\n  return text;\n}\n\n/**\n * @param {FlatStyle} flatStyle The flat style.\n * @return {import(\"./Image.js\").default|undefined} The image (if any).\n */\nfunction getImage(flatStyle) {\n  const iconSrc = flatStyle['icon-src'];\n  const iconImg = flatStyle['icon-img'];\n  if (iconSrc || iconImg) {\n    const icon = new Icon({\n      src: iconSrc,\n      img: iconImg,\n      imgSize: flatStyle['icon-img-size'],\n      anchor: flatStyle['icon-anchor'],\n      anchorOrigin: flatStyle['icon-anchor-origin'],\n      anchorXUnits: flatStyle['icon-anchor-x-units'],\n      anchorYUnits: flatStyle['icon-anchor-y-units'],\n      color: flatStyle['icon-color'],\n      crossOrigin: flatStyle['icon-cross-origin'],\n      offset: flatStyle['icon-offset'],\n      displacement: flatStyle['icon-displacement'],\n      opacity: flatStyle['icon-opacity'],\n      scale: flatStyle['icon-scale'],\n      width: flatStyle['icon-width'],\n      height: flatStyle['icon-height'],\n      rotation: flatStyle['icon-rotation'],\n      rotateWithView: flatStyle['icon-rotate-with-view'],\n      size: flatStyle['icon-size'],\n      declutterMode: flatStyle['icon-declutter-mode'],\n    });\n    return icon;\n  }\n\n  const shapePoints = flatStyle['shape-points'];\n  if (shapePoints) {\n    const prefix = 'shape-';\n    const shape = new RegularShape({\n      points: shapePoints,\n      fill: getFill(flatStyle, prefix),\n      stroke: getStroke(flatStyle, prefix),\n      radius: flatStyle['shape-radius'],\n      radius1: flatStyle['shape-radius1'],\n      radius2: flatStyle['shape-radius2'],\n      angle: flatStyle['shape-angle'],\n      displacement: flatStyle['shape-displacement'],\n      rotation: flatStyle['shape-rotation'],\n      rotateWithView: flatStyle['shape-rotate-with-view'],\n      scale: flatStyle['shape-scale'],\n      declutterMode: flatStyle['shape-declutter-mode'],\n    });\n\n    return shape;\n  }\n\n  const circleRadius = flatStyle['circle-radius'];\n  if (circleRadius) {\n    const prefix = 'circle-';\n    const circle = new Circle({\n      radius: circleRadius,\n      fill: getFill(flatStyle, prefix),\n      stroke: getStroke(flatStyle, prefix),\n      displacement: flatStyle['circle-displacement'],\n      scale: flatStyle['circle-scale'],\n      rotation: flatStyle['circle-rotation'],\n      rotateWithView: flatStyle['circle-rotate-with-view'],\n      declutterMode: flatStyle['circle-declutter-mode'],\n    });\n\n    return circle;\n  }\n\n  return;\n}\n\n/**\n * @return {import('./flat.js').FlatStyle} The default flat style.\n */\nexport function createDefaultStyle() {\n  return {\n    'fill-color': 'rgba(255,255,255,0.4)',\n    'stroke-color': '#3399CC',\n    'stroke-width': 1.25,\n    'circle-radius': 5,\n    'circle-fill-color': 'rgba(255,255,255,0.4)',\n    'circle-stroke-width': 1.25,\n    'circle-stroke-color': '#3399CC',\n  };\n}\n", "/**\n * @module ol/layer/BaseVector\n */\nimport Layer from './Layer.js';\nimport RBush from 'rbush';\nimport Style, {\n  createDefaultStyle,\n  toFunction as toStyleFunction,\n} from '../style/Style.js';\nimport {toStyle} from '../style/flat.js';\n\n/**\n * @template {import(\"../source/Vector.js\").default|import(\"../source/VectorTile.js\").default} VectorSourceType\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {import(\"../render.js\").OrderFunction} [renderOrder] Render order. Function to be used when sorting\n * features before rendering. By default features are drawn in the order that they are created. Use\n * `null` to avoid the sort, but get an undefined draw order.\n * @property {number} [renderBuffer=100] The buffer in pixels around the viewport extent used by the\n * renderer when getting features from the vector source for the rendering or hit-detection.\n * Recommended value: the size of the largest symbol, line width or label.\n * @property {VectorSourceType} [source] Source.\n * @property {import(\"../Map.js\").default} [map] Sets the layer as overlay on a map. The map will not manage\n * this layer in its layers collection, and the layer will be rendered on top. This is useful for\n * temporary layers. The standard way to add a layer to a map and have it managed by the map is to\n * use [map.addLayer()]{@link import(\"../Map.js\").default#addLayer}.\n * @property {boolean} [declutter=false] Declutter images and text. Decluttering is applied to all\n * image and text styles of all Vector and VectorTile layers that have set this to `true`. The priority\n * is defined by the z-index of the layer, the `zIndex` of the style and the render order of features.\n * Higher z-index means higher priority. Within the same z-index, a feature rendered before another has\n * higher priority.\n *\n * As an optimization decluttered features from layers with the same `className` are rendered above\n * the fill and stroke styles of all of those layers regardless of z-index.  To opt out of this\n * behavior and place declutterd features with their own layer configure the layer with a `className`\n * other than `ol-layer`.\n * @property {import(\"../style/Style.js\").StyleLike|import(\"../style/flat.js\").FlatStyleLike|null} [style] Layer style. When set to `null`, only\n * features that have their own style will be rendered. See {@link module:ol/style/Style~Style} for the default style\n * which will be used if this is not set.\n * @property {import(\"./Base.js\").BackgroundColor} [background] Background color for the layer. If not specified, no background\n * will be rendered.\n * @property {boolean} [updateWhileAnimating=false] When set to `true`, feature batches will\n * be recreated during animations. This means that no vectors will be shown clipped, but the\n * setting will have a performance impact for large amounts of vector data. When set to `false`,\n * batches will be recreated when no animation is active.\n * @property {boolean} [updateWhileInteracting=false] When set to `true`, feature batches will\n * be recreated during interactions. See also `updateWhileAnimating`.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @enum {string}\n * @private\n */\nconst Property = {\n  RENDER_ORDER: 'renderOrder',\n};\n\n/**\n * @classdesc\n * Vector data that is rendered client-side.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Vector.js\").default|import(\"../source/VectorTile.js\").default} VectorSourceType\n * @template {import(\"../renderer/canvas/VectorLayer.js\").default|import(\"../renderer/canvas/VectorTileLayer.js\").default|import(\"../renderer/canvas/VectorImageLayer.js\").default|import(\"../renderer/webgl/PointsLayer.js\").default} RendererType\n * @extends {Layer<VectorSourceType, RendererType>}\n * @api\n */\nclass BaseVectorLayer extends Layer {\n  /**\n   * @param {Options<VectorSourceType>} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const baseOptions = Object.assign({}, options);\n\n    delete baseOptions.style;\n    delete baseOptions.renderBuffer;\n    delete baseOptions.updateWhileAnimating;\n    delete baseOptions.updateWhileInteracting;\n    super(baseOptions);\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.declutter_ =\n      options.declutter !== undefined ? options.declutter : false;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.renderBuffer_ =\n      options.renderBuffer !== undefined ? options.renderBuffer : 100;\n\n    /**\n     * User provided style.\n     * @type {import(\"../style/Style.js\").StyleLike}\n     * @private\n     */\n    this.style_ = null;\n\n    /**\n     * Style function for use within the library.\n     * @type {import(\"../style/Style.js\").StyleFunction|undefined}\n     * @private\n     */\n    this.styleFunction_ = undefined;\n\n    this.setStyle(options.style);\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.updateWhileAnimating_ =\n      options.updateWhileAnimating !== undefined\n        ? options.updateWhileAnimating\n        : false;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.updateWhileInteracting_ =\n      options.updateWhileInteracting !== undefined\n        ? options.updateWhileInteracting\n        : false;\n  }\n\n  /**\n   * @return {boolean} Declutter.\n   */\n  getDeclutter() {\n    return this.declutter_;\n  }\n\n  /**\n   * Get the topmost feature that intersects the given pixel on the viewport. Returns a promise\n   * that resolves with an array of features. The array will either contain the topmost feature\n   * when a hit was detected, or it will be empty.\n   *\n   * The hit detection algorithm used for this method is optimized for performance, but is less\n   * accurate than the one used in [map.getFeaturesAtPixel()]{@link import(\"../Map.js\").default#getFeaturesAtPixel}.\n   * Text is not considered, and icons are only represented by their bounding box instead of the exact\n   * image.\n   *\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../Feature\").FeatureLike>>} Promise that resolves with an array of features.\n   * @api\n   */\n  getFeatures(pixel) {\n    return super.getFeatures(pixel);\n  }\n\n  /**\n   * @return {number|undefined} Render buffer.\n   */\n  getRenderBuffer() {\n    return this.renderBuffer_;\n  }\n\n  /**\n   * @return {function(import(\"../Feature.js\").default, import(\"../Feature.js\").default): number|null|undefined} Render\n   *     order.\n   */\n  getRenderOrder() {\n    return /** @type {import(\"../render.js\").OrderFunction|null|undefined} */ (\n      this.get(Property.RENDER_ORDER)\n    );\n  }\n\n  /**\n   * Get the style for features.  This returns whatever was passed to the `style`\n   * option at construction or to the `setStyle` method.\n   * @return {import(\"../style/Style.js\").StyleLike|null|undefined} Layer style.\n   * @api\n   */\n  getStyle() {\n    return this.style_;\n  }\n\n  /**\n   * Get the style function.\n   * @return {import(\"../style/Style.js\").StyleFunction|undefined} Layer style function.\n   * @api\n   */\n  getStyleFunction() {\n    return this.styleFunction_;\n  }\n\n  /**\n   * @return {boolean} Whether the rendered layer should be updated while\n   *     animating.\n   */\n  getUpdateWhileAnimating() {\n    return this.updateWhileAnimating_;\n  }\n\n  /**\n   * @return {boolean} Whether the rendered layer should be updated while\n   *     interacting.\n   */\n  getUpdateWhileInteracting() {\n    return this.updateWhileInteracting_;\n  }\n\n  /**\n   * Render declutter items for this layer\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  renderDeclutter(frameState) {\n    if (!frameState.declutterTree) {\n      frameState.declutterTree = new RBush(9);\n    }\n    /** @type {*} */ (this.getRenderer()).renderDeclutter(frameState);\n  }\n\n  /**\n   * @param {import(\"../render.js\").OrderFunction|null|undefined} renderOrder\n   *     Render order.\n   */\n  setRenderOrder(renderOrder) {\n    this.set(Property.RENDER_ORDER, renderOrder);\n  }\n\n  /**\n   * Set the style for features.  This can be a single style object, an array\n   * of styles, or a function that takes a feature and resolution and returns\n   * an array of styles. If set to `null`, the layer has no style (a `null` style),\n   * so only features that have their own styles will be rendered in the layer. Call\n   * `setStyle()` without arguments to reset to the default style. See\n   * [the ol/style/Style module]{@link module:ol/style/Style~Style} for information on the default style.\n   *\n   * If your layer has a static style, you can use [flat style]{@link module:ol/style/flat~FlatStyle} object\n   * literals instead of using the `Style` and symbolizer constructors (`Fill`, `Stroke`, etc.):\n   * ```js\n   * vectorLayer.setStyle({\n   *   \"fill-color\": \"yellow\",\n   *   \"stroke-color\": \"black\",\n   *   \"stroke-width\": 4\n   * })\n   * ```\n   *\n   * @param {import(\"../style/Style.js\").StyleLike|import(\"../style/flat.js\").FlatStyleLike|null} [style] Layer style.\n   * @api\n   */\n  setStyle(style) {\n    /**\n     * @type {import(\"../style/Style.js\").StyleLike|null}\n     */\n    let styleLike;\n\n    if (style === undefined) {\n      styleLike = createDefaultStyle;\n    } else if (style === null) {\n      styleLike = null;\n    } else if (typeof style === 'function') {\n      styleLike = style;\n    } else if (style instanceof Style) {\n      styleLike = style;\n    } else if (Array.isArray(style)) {\n      const len = style.length;\n\n      /**\n       * @type {Array<Style>}\n       */\n      const styles = new Array(len);\n\n      for (let i = 0; i < len; ++i) {\n        const s = style[i];\n        if (s instanceof Style) {\n          styles[i] = s;\n        } else {\n          styles[i] = toStyle(s);\n        }\n      }\n      styleLike = styles;\n    } else {\n      styleLike = toStyle(style);\n    }\n\n    this.style_ = styleLike;\n    this.styleFunction_ =\n      style === null ? undefined : toStyleFunction(this.style_);\n    this.changed();\n  }\n}\n\nexport default BaseVectorLayer;\n", "/**\n * @module ol/render/canvas/Instruction\n */\n\n/**\n * @enum {number}\n */\nconst Instruction = {\n  BEGIN_GEOMETRY: 0,\n  BEGIN_PATH: 1,\n  CIRCLE: 2,\n  CLOSE_PATH: 3,\n  CUSTOM: 4,\n  DRAW_CHARS: 5,\n  DRAW_IMAGE: 6,\n  END_GEOMETRY: 7,\n  F<PERSON><PERSON>: 8,\n  MOVE_TO_LINE_TO: 9,\n  SET_FILL_STYLE: 10,\n  SET_STROKE_STYLE: 11,\n  STROKE: 12,\n};\n\n/**\n * @type {Array<Instruction>}\n */\nexport const fillInstruction = [Instruction.FILL];\n\n/**\n * @type {Array<Instruction>}\n */\nexport const strokeInstruction = [Instruction.STROKE];\n\n/**\n * @type {Array<Instruction>}\n */\nexport const beginPathInstruction = [Instruction.BEGIN_PATH];\n\n/**\n * @type {Array<Instruction>}\n */\nexport const closePathInstruction = [Instruction.CLOSE_PATH];\n\nexport default Instruction;\n", "/**\n * @module ol/render/VectorContext\n */\n\n/**\n * @classdesc\n * Context for drawing geometries.  A vector context is available on render\n * events and does not need to be constructed directly.\n * @api\n */\nclass VectorContext {\n  /**\n   * Render a geometry with a custom renderer.\n   *\n   * @param {import(\"../geom/SimpleGeometry.js\").default} geometry Geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   * @param {Function} renderer Renderer.\n   * @param {Function} hitDetectionRenderer Renderer.\n   */\n  drawCustom(geometry, feature, renderer, hitDetectionRenderer) {}\n\n  /**\n   * Render a geometry.\n   *\n   * @param {import(\"../geom/Geometry.js\").default} geometry The geometry to render.\n   */\n  drawGeometry(geometry) {}\n\n  /**\n   * Set the rendering style.\n   *\n   * @param {import(\"../style/Style.js\").default} style The rendering style.\n   */\n  setStyle(style) {}\n\n  /**\n   * @param {import(\"../geom/Circle.js\").default} circleGeometry Circle geometry.\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   */\n  drawCircle(circleGeometry, feature) {}\n\n  /**\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {import(\"../style/Style.js\").default} style Style.\n   */\n  drawFeature(feature, style) {}\n\n  /**\n   * @param {import(\"../geom/GeometryCollection.js\").default} geometryCollectionGeometry Geometry collection.\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   */\n  drawGeometryCollection(geometryCollectionGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/LineString.js\").default|import(\"./Feature.js\").default} lineStringGeometry Line string geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawLineString(lineStringGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/MultiLineString.js\").default|import(\"./Feature.js\").default} multiLineStringGeometry MultiLineString geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiLineString(multiLineStringGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/MultiPoint.js\").default|import(\"./Feature.js\").default} multiPointGeometry MultiPoint geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiPoint(multiPointGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/MultiPolygon.js\").default} multiPolygonGeometry MultiPolygon geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiPolygon(multiPolygonGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/Point.js\").default|import(\"./Feature.js\").default} pointGeometry Point geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawPoint(pointGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/Polygon.js\").default|import(\"./Feature.js\").default} polygonGeometry Polygon geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawPolygon(polygonGeometry, feature) {}\n\n  /**\n   * @param {import(\"../geom/SimpleGeometry.js\").default|import(\"./Feature.js\").default} geometry Geometry.\n   * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawText(geometry, feature) {}\n\n  /**\n   * @param {import(\"../style/Fill.js\").default} fillStyle Fill style.\n   * @param {import(\"../style/Stroke.js\").default} strokeStyle Stroke style.\n   */\n  setFillStrokeStyle(fillStyle, strokeStyle) {}\n\n  /**\n   * @param {import(\"../style/Image.js\").default} imageStyle Image style.\n   * @param {import(\"../render/canvas.js\").DeclutterImageWithText} [declutterImageWithText] Shared data for combined decluttering with a text style.\n   */\n  setImageStyle(imageStyle, declutterImageWithText) {}\n\n  /**\n   * @param {import(\"../style/Text.js\").default} textStyle Text style.\n   * @param {import(\"../render/canvas.js\").DeclutterImageWithText} [declutterImageWithText] Shared data for combined decluttering with an image style.\n   */\n  setTextStyle(textStyle, declutterImageWithText) {}\n}\n\nexport default VectorContext;\n", "/**\n * @module ol/render/canvas/Builder\n */\nimport CanvasInstruction from './Instruction.js';\nimport Relationship from '../../extent/Relationship.js';\nimport VectorContext from '../VectorContext.js';\nimport {asColorLike} from '../../colorlike.js';\nimport {\n  buffer,\n  clone,\n  containsCoordinate,\n  coordinateRelationship,\n} from '../../extent.js';\nimport {\n  defaultFillStyle,\n  defaultLineCap,\n  defaultLineDash,\n  defaultLineDashOffset,\n  defaultLineJoin,\n  defaultLineWidth,\n  defaultMiterLimit,\n  defaultStrokeStyle,\n} from '../canvas.js';\nimport {equals, reverseSubArray} from '../../array.js';\nimport {\n  inflateCoordinates,\n  inflateCoordinatesArray,\n  inflateMultiCoordinatesArray,\n} from '../../geom/flat/inflate.js';\n\nclass CanvasBuilder extends VectorContext {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Maximum extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    super();\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.tolerance = tolerance;\n\n    /**\n     * @protected\n     * @const\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.maxExtent = maxExtent;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.pixelRatio = pixelRatio;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.maxLineWidth = 0;\n\n    /**\n     * @protected\n     * @const\n     * @type {number}\n     */\n    this.resolution = resolution;\n\n    /**\n     * @private\n     * @type {Array<*>}\n     */\n    this.beginGeometryInstruction1_ = null;\n\n    /**\n     * @private\n     * @type {Array<*>}\n     */\n    this.beginGeometryInstruction2_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.bufferedMaxExtent_ = null;\n\n    /**\n     * @protected\n     * @type {Array<*>}\n     */\n    this.instructions = [];\n\n    /**\n     * @protected\n     * @type {Array<number>}\n     */\n    this.coordinates = [];\n\n    /**\n     * @private\n     * @type {import(\"../../coordinate.js\").Coordinate}\n     */\n    this.tmpCoordinate_ = [];\n\n    /**\n     * @protected\n     * @type {Array<*>}\n     */\n    this.hitDetectionInstructions = [];\n\n    /**\n     * @protected\n     * @type {import(\"../canvas.js\").FillStrokeState}\n     */\n    this.state = /** @type {import(\"../canvas.js\").FillStrokeState} */ ({});\n  }\n\n  /**\n   * @protected\n   * @param {Array<number>} dashArray Dash array.\n   * @return {Array<number>} Dash array with pixel ratio applied\n   */\n  applyPixelRatio(dashArray) {\n    const pixelRatio = this.pixelRatio;\n    return pixelRatio == 1\n      ? dashArray\n      : dashArray.map(function (dash) {\n          return dash * pixelRatio;\n        });\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} stride Stride.\n   * @protected\n   * @return {number} My end\n   */\n  appendFlatPointCoordinates(flatCoordinates, stride) {\n    const extent = this.getBufferedMaxExtent();\n    const tmpCoord = this.tmpCoordinate_;\n    const coordinates = this.coordinates;\n    let myEnd = coordinates.length;\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      tmpCoord[0] = flatCoordinates[i];\n      tmpCoord[1] = flatCoordinates[i + 1];\n      if (containsCoordinate(extent, tmpCoord)) {\n        coordinates[myEnd++] = tmpCoord[0];\n        coordinates[myEnd++] = tmpCoord[1];\n      }\n    }\n    return myEnd;\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {number} end End.\n   * @param {number} stride Stride.\n   * @param {boolean} closed Last input coordinate equals first.\n   * @param {boolean} skipFirst Skip first coordinate.\n   * @protected\n   * @return {number} My end.\n   */\n  appendFlatLineCoordinates(\n    flatCoordinates,\n    offset,\n    end,\n    stride,\n    closed,\n    skipFirst\n  ) {\n    const coordinates = this.coordinates;\n    let myEnd = coordinates.length;\n    const extent = this.getBufferedMaxExtent();\n    if (skipFirst) {\n      offset += stride;\n    }\n    let lastXCoord = flatCoordinates[offset];\n    let lastYCoord = flatCoordinates[offset + 1];\n    const nextCoord = this.tmpCoordinate_;\n    let skipped = true;\n\n    let i, lastRel, nextRel;\n    for (i = offset + stride; i < end; i += stride) {\n      nextCoord[0] = flatCoordinates[i];\n      nextCoord[1] = flatCoordinates[i + 1];\n      nextRel = coordinateRelationship(extent, nextCoord);\n      if (nextRel !== lastRel) {\n        if (skipped) {\n          coordinates[myEnd++] = lastXCoord;\n          coordinates[myEnd++] = lastYCoord;\n          skipped = false;\n        }\n        coordinates[myEnd++] = nextCoord[0];\n        coordinates[myEnd++] = nextCoord[1];\n      } else if (nextRel === Relationship.INTERSECTING) {\n        coordinates[myEnd++] = nextCoord[0];\n        coordinates[myEnd++] = nextCoord[1];\n        skipped = false;\n      } else {\n        skipped = true;\n      }\n      lastXCoord = nextCoord[0];\n      lastYCoord = nextCoord[1];\n      lastRel = nextRel;\n    }\n\n    // Last coordinate equals first or only one point to append:\n    if ((closed && skipped) || i === offset + stride) {\n      coordinates[myEnd++] = lastXCoord;\n      coordinates[myEnd++] = lastYCoord;\n    }\n    return myEnd;\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {Array<number>} ends Ends.\n   * @param {number} stride Stride.\n   * @param {Array<number>} builderEnds Builder ends.\n   * @return {number} Offset.\n   */\n  drawCustomCoordinates_(flatCoordinates, offset, ends, stride, builderEnds) {\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const builderEnd = this.appendFlatLineCoordinates(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        false,\n        false\n      );\n      builderEnds.push(builderEnd);\n      offset = end;\n    }\n    return offset;\n  }\n\n  /**\n   * @param {import(\"../../geom/SimpleGeometry.js\").default} geometry Geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   * @param {Function} renderer Renderer.\n   * @param {Function} hitDetectionRenderer Renderer.\n   */\n  drawCustom(geometry, feature, renderer, hitDetectionRenderer) {\n    this.beginGeometry(geometry, feature);\n\n    const type = geometry.getType();\n    const stride = geometry.getStride();\n    const builderBegin = this.coordinates.length;\n\n    let flatCoordinates, builderEnd, builderEnds, builderEndss;\n    let offset;\n\n    switch (type) {\n      case 'MultiPolygon':\n        flatCoordinates =\n          /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (\n            geometry\n          ).getOrientedFlatCoordinates();\n        builderEndss = [];\n        const endss =\n          /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (\n            geometry\n          ).getEndss();\n        offset = 0;\n        for (let i = 0, ii = endss.length; i < ii; ++i) {\n          const myEnds = [];\n          offset = this.drawCustomCoordinates_(\n            flatCoordinates,\n            offset,\n            endss[i],\n            stride,\n            myEnds\n          );\n          builderEndss.push(myEnds);\n        }\n        this.instructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEndss,\n          geometry,\n          renderer,\n          inflateMultiCoordinatesArray,\n        ]);\n        this.hitDetectionInstructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEndss,\n          geometry,\n          hitDetectionRenderer || renderer,\n          inflateMultiCoordinatesArray,\n        ]);\n        break;\n      case 'Polygon':\n      case 'MultiLineString':\n        builderEnds = [];\n        flatCoordinates =\n          type == 'Polygon'\n            ? /** @type {import(\"../../geom/Polygon.js\").default} */ (\n                geometry\n              ).getOrientedFlatCoordinates()\n            : geometry.getFlatCoordinates();\n        offset = this.drawCustomCoordinates_(\n          flatCoordinates,\n          0,\n          /** @type {import(\"../../geom/Polygon.js\").default|import(\"../../geom/MultiLineString.js\").default} */ (\n            geometry\n          ).getEnds(),\n          stride,\n          builderEnds\n        );\n        this.instructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnds,\n          geometry,\n          renderer,\n          inflateCoordinatesArray,\n        ]);\n        this.hitDetectionInstructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnds,\n          geometry,\n          hitDetectionRenderer || renderer,\n          inflateCoordinatesArray,\n        ]);\n        break;\n      case 'LineString':\n      case 'Circle':\n        flatCoordinates = geometry.getFlatCoordinates();\n        builderEnd = this.appendFlatLineCoordinates(\n          flatCoordinates,\n          0,\n          flatCoordinates.length,\n          stride,\n          false,\n          false\n        );\n        this.instructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnd,\n          geometry,\n          renderer,\n          inflateCoordinates,\n        ]);\n        this.hitDetectionInstructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnd,\n          geometry,\n          hitDetectionRenderer || renderer,\n          inflateCoordinates,\n        ]);\n        break;\n      case 'MultiPoint':\n        flatCoordinates = geometry.getFlatCoordinates();\n        builderEnd = this.appendFlatPointCoordinates(flatCoordinates, stride);\n\n        if (builderEnd > builderBegin) {\n          this.instructions.push([\n            CanvasInstruction.CUSTOM,\n            builderBegin,\n            builderEnd,\n            geometry,\n            renderer,\n            inflateCoordinates,\n          ]);\n          this.hitDetectionInstructions.push([\n            CanvasInstruction.CUSTOM,\n            builderBegin,\n            builderEnd,\n            geometry,\n            hitDetectionRenderer || renderer,\n            inflateCoordinates,\n          ]);\n        }\n        break;\n      case 'Point':\n        flatCoordinates = geometry.getFlatCoordinates();\n        this.coordinates.push(flatCoordinates[0], flatCoordinates[1]);\n        builderEnd = this.coordinates.length;\n\n        this.instructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnd,\n          geometry,\n          renderer,\n        ]);\n        this.hitDetectionInstructions.push([\n          CanvasInstruction.CUSTOM,\n          builderBegin,\n          builderEnd,\n          geometry,\n          hitDetectionRenderer || renderer,\n        ]);\n        break;\n      default:\n    }\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @protected\n   * @param {import(\"../../geom/Geometry\").default|import(\"../Feature.js\").default} geometry The geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  beginGeometry(geometry, feature) {\n    this.beginGeometryInstruction1_ = [\n      CanvasInstruction.BEGIN_GEOMETRY,\n      feature,\n      0,\n      geometry,\n    ];\n    this.instructions.push(this.beginGeometryInstruction1_);\n    this.beginGeometryInstruction2_ = [\n      CanvasInstruction.BEGIN_GEOMETRY,\n      feature,\n      0,\n      geometry,\n    ];\n    this.hitDetectionInstructions.push(this.beginGeometryInstruction2_);\n  }\n\n  /**\n   * @return {import(\"../canvas.js\").SerializableInstructions} the serializable instructions.\n   */\n  finish() {\n    return {\n      instructions: this.instructions,\n      hitDetectionInstructions: this.hitDetectionInstructions,\n      coordinates: this.coordinates,\n    };\n  }\n\n  /**\n   * Reverse the hit detection instructions.\n   */\n  reverseHitDetectionInstructions() {\n    const hitDetectionInstructions = this.hitDetectionInstructions;\n    // step 1 - reverse array\n    hitDetectionInstructions.reverse();\n    // step 2 - reverse instructions within geometry blocks\n    let i;\n    const n = hitDetectionInstructions.length;\n    let instruction;\n    let type;\n    let begin = -1;\n    for (i = 0; i < n; ++i) {\n      instruction = hitDetectionInstructions[i];\n      type = /** @type {import(\"./Instruction.js\").default} */ (instruction[0]);\n      if (type == CanvasInstruction.END_GEOMETRY) {\n        begin = i;\n      } else if (type == CanvasInstruction.BEGIN_GEOMETRY) {\n        instruction[2] = i;\n        reverseSubArray(this.hitDetectionInstructions, begin, i);\n        begin = -1;\n      }\n    }\n  }\n\n  /**\n   * @param {import(\"../../style/Fill.js\").default} fillStyle Fill style.\n   * @param {import(\"../../style/Stroke.js\").default} strokeStyle Stroke style.\n   */\n  setFillStrokeStyle(fillStyle, strokeStyle) {\n    const state = this.state;\n    if (fillStyle) {\n      const fillStyleColor = fillStyle.getColor();\n      state.fillStyle = asColorLike(\n        fillStyleColor ? fillStyleColor : defaultFillStyle\n      );\n    } else {\n      state.fillStyle = undefined;\n    }\n    if (strokeStyle) {\n      const strokeStyleColor = strokeStyle.getColor();\n      state.strokeStyle = asColorLike(\n        strokeStyleColor ? strokeStyleColor : defaultStrokeStyle\n      );\n      const strokeStyleLineCap = strokeStyle.getLineCap();\n      state.lineCap =\n        strokeStyleLineCap !== undefined ? strokeStyleLineCap : defaultLineCap;\n      const strokeStyleLineDash = strokeStyle.getLineDash();\n      state.lineDash = strokeStyleLineDash\n        ? strokeStyleLineDash.slice()\n        : defaultLineDash;\n      const strokeStyleLineDashOffset = strokeStyle.getLineDashOffset();\n      state.lineDashOffset = strokeStyleLineDashOffset\n        ? strokeStyleLineDashOffset\n        : defaultLineDashOffset;\n      const strokeStyleLineJoin = strokeStyle.getLineJoin();\n      state.lineJoin =\n        strokeStyleLineJoin !== undefined\n          ? strokeStyleLineJoin\n          : defaultLineJoin;\n      const strokeStyleWidth = strokeStyle.getWidth();\n      state.lineWidth =\n        strokeStyleWidth !== undefined ? strokeStyleWidth : defaultLineWidth;\n      const strokeStyleMiterLimit = strokeStyle.getMiterLimit();\n      state.miterLimit =\n        strokeStyleMiterLimit !== undefined\n          ? strokeStyleMiterLimit\n          : defaultMiterLimit;\n\n      if (state.lineWidth > this.maxLineWidth) {\n        this.maxLineWidth = state.lineWidth;\n        // invalidate the buffered max extent cache\n        this.bufferedMaxExtent_ = null;\n      }\n    } else {\n      state.strokeStyle = undefined;\n      state.lineCap = undefined;\n      state.lineDash = null;\n      state.lineDashOffset = undefined;\n      state.lineJoin = undefined;\n      state.lineWidth = undefined;\n      state.miterLimit = undefined;\n    }\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   * @return {Array<*>} Fill instruction.\n   */\n  createFill(state) {\n    const fillStyle = state.fillStyle;\n    /** @type {Array<*>} */\n    const fillInstruction = [CanvasInstruction.SET_FILL_STYLE, fillStyle];\n    if (typeof fillStyle !== 'string') {\n      // Fill is a pattern or gradient - align it!\n      fillInstruction.push(true);\n    }\n    return fillInstruction;\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   */\n  applyStroke(state) {\n    this.instructions.push(this.createStroke(state));\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   * @return {Array<*>} Stroke instruction.\n   */\n  createStroke(state) {\n    return [\n      CanvasInstruction.SET_STROKE_STYLE,\n      state.strokeStyle,\n      state.lineWidth * this.pixelRatio,\n      state.lineCap,\n      state.lineJoin,\n      state.miterLimit,\n      this.applyPixelRatio(state.lineDash),\n      state.lineDashOffset * this.pixelRatio,\n    ];\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   * @param {function(this:CanvasBuilder, import(\"../canvas.js\").FillStrokeState):Array<*>} createFill Create fill.\n   */\n  updateFillStyle(state, createFill) {\n    const fillStyle = state.fillStyle;\n    if (typeof fillStyle !== 'string' || state.currentFillStyle != fillStyle) {\n      if (fillStyle !== undefined) {\n        this.instructions.push(createFill.call(this, state));\n      }\n      state.currentFillStyle = fillStyle;\n    }\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   * @param {function(this:CanvasBuilder, import(\"../canvas.js\").FillStrokeState): void} applyStroke Apply stroke.\n   */\n  updateStrokeStyle(state, applyStroke) {\n    const strokeStyle = state.strokeStyle;\n    const lineCap = state.lineCap;\n    const lineDash = state.lineDash;\n    const lineDashOffset = state.lineDashOffset;\n    const lineJoin = state.lineJoin;\n    const lineWidth = state.lineWidth;\n    const miterLimit = state.miterLimit;\n    if (\n      state.currentStrokeStyle != strokeStyle ||\n      state.currentLineCap != lineCap ||\n      (lineDash != state.currentLineDash &&\n        !equals(state.currentLineDash, lineDash)) ||\n      state.currentLineDashOffset != lineDashOffset ||\n      state.currentLineJoin != lineJoin ||\n      state.currentLineWidth != lineWidth ||\n      state.currentMiterLimit != miterLimit\n    ) {\n      if (strokeStyle !== undefined) {\n        applyStroke.call(this, state);\n      }\n      state.currentStrokeStyle = strokeStyle;\n      state.currentLineCap = lineCap;\n      state.currentLineDash = lineDash;\n      state.currentLineDashOffset = lineDashOffset;\n      state.currentLineJoin = lineJoin;\n      state.currentLineWidth = lineWidth;\n      state.currentMiterLimit = miterLimit;\n    }\n  }\n\n  /**\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  endGeometry(feature) {\n    this.beginGeometryInstruction1_[2] = this.instructions.length;\n    this.beginGeometryInstruction1_ = null;\n    this.beginGeometryInstruction2_[2] = this.hitDetectionInstructions.length;\n    this.beginGeometryInstruction2_ = null;\n    const endGeometryInstruction = [CanvasInstruction.END_GEOMETRY, feature];\n    this.instructions.push(endGeometryInstruction);\n    this.hitDetectionInstructions.push(endGeometryInstruction);\n  }\n\n  /**\n   * Get the buffered rendering extent.  Rendering will be clipped to the extent\n   * provided to the constructor.  To account for symbolizers that may intersect\n   * this extent, we calculate a buffered extent (e.g. based on stroke width).\n   * @return {import(\"../../extent.js\").Extent} The buffered rendering extent.\n   * @protected\n   */\n  getBufferedMaxExtent() {\n    if (!this.bufferedMaxExtent_) {\n      this.bufferedMaxExtent_ = clone(this.maxExtent);\n      if (this.maxLineWidth > 0) {\n        const width = (this.resolution * (this.maxLineWidth + 1)) / 2;\n        buffer(this.bufferedMaxExtent_, width, this.bufferedMaxExtent_);\n      }\n    }\n    return this.bufferedMaxExtent_;\n  }\n}\n\nexport default CanvasBuilder;\n", "/**\n * @module ol/render/canvas/ImageBuilder\n */\nimport CanvasBuilder from './Builder.js';\nimport CanvasInstruction from './Instruction.js';\n\nclass CanvasImageBuilder extends CanvasBuilder {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Maximum extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    super(tolerance, maxExtent, resolution, pixelRatio);\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement}\n     */\n    this.hitDetectionImage_ = null;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement}\n     */\n    this.image_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.imagePixelRatio_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.anchorX_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.anchorY_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.height_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.opacity_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.originX_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.originY_ = undefined;\n\n    /**\n     * @private\n     * @type {boolean|undefined}\n     */\n    this.rotateWithView_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.rotation_ = undefined;\n\n    /**\n     * @private\n     * @type {import(\"../../size.js\").Size|undefined}\n     */\n    this.scale_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.width_ = undefined;\n\n    /**\n     * @private\n     * @type {\"declutter\"|\"obstacle\"|\"none\"|undefined}\n     */\n    this.declutterMode_ = undefined;\n\n    /**\n     * Data shared with a text builder for combined decluttering.\n     * @private\n     * @type {import(\"../canvas.js\").DeclutterImageWithText}\n     */\n    this.declutterImageWithText_ = undefined;\n  }\n\n  /**\n   * @param {import(\"../../geom/Point.js\").default|import(\"../Feature.js\").default} pointGeometry Point geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawPoint(pointGeometry, feature) {\n    if (!this.image_) {\n      return;\n    }\n    this.beginGeometry(pointGeometry, feature);\n    const flatCoordinates = pointGeometry.getFlatCoordinates();\n    const stride = pointGeometry.getStride();\n    const myBegin = this.coordinates.length;\n    const myEnd = this.appendFlatPointCoordinates(flatCoordinates, stride);\n    this.instructions.push([\n      CanvasInstruction.DRAW_IMAGE,\n      myBegin,\n      myEnd,\n      this.image_,\n      // Remaining arguments to DRAW_IMAGE are in alphabetical order\n      this.anchorX_ * this.imagePixelRatio_,\n      this.anchorY_ * this.imagePixelRatio_,\n      Math.ceil(this.height_ * this.imagePixelRatio_),\n      this.opacity_,\n      this.originX_ * this.imagePixelRatio_,\n      this.originY_ * this.imagePixelRatio_,\n      this.rotateWithView_,\n      this.rotation_,\n      [\n        (this.scale_[0] * this.pixelRatio) / this.imagePixelRatio_,\n        (this.scale_[1] * this.pixelRatio) / this.imagePixelRatio_,\n      ],\n      Math.ceil(this.width_ * this.imagePixelRatio_),\n      this.declutterMode_,\n      this.declutterImageWithText_,\n    ]);\n    this.hitDetectionInstructions.push([\n      CanvasInstruction.DRAW_IMAGE,\n      myBegin,\n      myEnd,\n      this.hitDetectionImage_,\n      // Remaining arguments to DRAW_IMAGE are in alphabetical order\n      this.anchorX_,\n      this.anchorY_,\n      this.height_,\n      1,\n      this.originX_,\n      this.originY_,\n      this.rotateWithView_,\n      this.rotation_,\n      this.scale_,\n      this.width_,\n      this.declutterMode_,\n      this.declutterImageWithText_,\n    ]);\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @param {import(\"../../geom/MultiPoint.js\").default|import(\"../Feature.js\").default} multiPointGeometry MultiPoint geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiPoint(multiPointGeometry, feature) {\n    if (!this.image_) {\n      return;\n    }\n    this.beginGeometry(multiPointGeometry, feature);\n    const flatCoordinates = multiPointGeometry.getFlatCoordinates();\n    const stride = multiPointGeometry.getStride();\n    const myBegin = this.coordinates.length;\n    const myEnd = this.appendFlatPointCoordinates(flatCoordinates, stride);\n    this.instructions.push([\n      CanvasInstruction.DRAW_IMAGE,\n      myBegin,\n      myEnd,\n      this.image_,\n      // Remaining arguments to DRAW_IMAGE are in alphabetical order\n      this.anchorX_ * this.imagePixelRatio_,\n      this.anchorY_ * this.imagePixelRatio_,\n      Math.ceil(this.height_ * this.imagePixelRatio_),\n      this.opacity_,\n      this.originX_ * this.imagePixelRatio_,\n      this.originY_ * this.imagePixelRatio_,\n      this.rotateWithView_,\n      this.rotation_,\n      [\n        (this.scale_[0] * this.pixelRatio) / this.imagePixelRatio_,\n        (this.scale_[1] * this.pixelRatio) / this.imagePixelRatio_,\n      ],\n      Math.ceil(this.width_ * this.imagePixelRatio_),\n      this.declutterMode_,\n      this.declutterImageWithText_,\n    ]);\n    this.hitDetectionInstructions.push([\n      CanvasInstruction.DRAW_IMAGE,\n      myBegin,\n      myEnd,\n      this.hitDetectionImage_,\n      // Remaining arguments to DRAW_IMAGE are in alphabetical order\n      this.anchorX_,\n      this.anchorY_,\n      this.height_,\n      1,\n      this.originX_,\n      this.originY_,\n      this.rotateWithView_,\n      this.rotation_,\n      this.scale_,\n      this.width_,\n      this.declutterMode_,\n      this.declutterImageWithText_,\n    ]);\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @return {import(\"../canvas.js\").SerializableInstructions} the serializable instructions.\n   */\n  finish() {\n    this.reverseHitDetectionInstructions();\n    // FIXME this doesn't really protect us against further calls to draw*Geometry\n    this.anchorX_ = undefined;\n    this.anchorY_ = undefined;\n    this.hitDetectionImage_ = null;\n    this.image_ = null;\n    this.imagePixelRatio_ = undefined;\n    this.height_ = undefined;\n    this.scale_ = undefined;\n    this.opacity_ = undefined;\n    this.originX_ = undefined;\n    this.originY_ = undefined;\n    this.rotateWithView_ = undefined;\n    this.rotation_ = undefined;\n    this.width_ = undefined;\n    return super.finish();\n  }\n\n  /**\n   * @param {import(\"../../style/Image.js\").default} imageStyle Image style.\n   * @param {Object} [sharedData] Shared data.\n   */\n  setImageStyle(imageStyle, sharedData) {\n    const anchor = imageStyle.getAnchor();\n    const size = imageStyle.getSize();\n    const origin = imageStyle.getOrigin();\n    this.imagePixelRatio_ = imageStyle.getPixelRatio(this.pixelRatio);\n    this.anchorX_ = anchor[0];\n    this.anchorY_ = anchor[1];\n    this.hitDetectionImage_ = imageStyle.getHitDetectionImage();\n    this.image_ = imageStyle.getImage(this.pixelRatio);\n    this.height_ = size[1];\n    this.opacity_ = imageStyle.getOpacity();\n    this.originX_ = origin[0];\n    this.originY_ = origin[1];\n    this.rotateWithView_ = imageStyle.getRotateWithView();\n    this.rotation_ = imageStyle.getRotation();\n    this.scale_ = imageStyle.getScaleArray();\n    this.width_ = size[0];\n    this.declutterMode_ = imageStyle.getDeclutterMode();\n    this.declutterImageWithText_ = sharedData;\n  }\n}\n\nexport default CanvasImageBuilder;\n", "/**\n * @module ol/render/canvas/LineStringBuilder\n */\nimport CanvasBuilder from './Builder.js';\nimport CanvasInstruction, {\n  beginPathInstruction,\n  strokeInstruction,\n} from './Instruction.js';\nimport {defaultLineDash, defaultLineDashOffset} from '../canvas.js';\n\nclass CanvasLineStringBuilder extends CanvasBuilder {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Maximum extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    super(tolerance, maxExtent, resolution, pixelRatio);\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {number} end End.\n   * @param {number} stride Stride.\n   * @private\n   * @return {number} end.\n   */\n  drawFlatCoordinates_(flatCoordinates, offset, end, stride) {\n    const myBegin = this.coordinates.length;\n    const myEnd = this.appendFlatLineCoordinates(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      false,\n      false\n    );\n    const moveToLineToInstruction = [\n      CanvasInstruction.MOVE_TO_LINE_TO,\n      myBegin,\n      myEnd,\n    ];\n    this.instructions.push(moveToLineToInstruction);\n    this.hitDetectionInstructions.push(moveToLineToInstruction);\n    return end;\n  }\n\n  /**\n   * @param {import(\"../../geom/LineString.js\").default|import(\"../Feature.js\").default} lineStringGeometry Line string geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawLineString(lineStringGeometry, feature) {\n    const state = this.state;\n    const strokeStyle = state.strokeStyle;\n    const lineWidth = state.lineWidth;\n    if (strokeStyle === undefined || lineWidth === undefined) {\n      return;\n    }\n    this.updateStrokeStyle(state, this.applyStroke);\n    this.beginGeometry(lineStringGeometry, feature);\n    this.hitDetectionInstructions.push(\n      [\n        CanvasInstruction.SET_STROKE_STYLE,\n        state.strokeStyle,\n        state.lineWidth,\n        state.lineCap,\n        state.lineJoin,\n        state.miterLimit,\n        defaultLineDash,\n        defaultLineDashOffset,\n      ],\n      beginPathInstruction\n    );\n    const flatCoordinates = lineStringGeometry.getFlatCoordinates();\n    const stride = lineStringGeometry.getStride();\n    this.drawFlatCoordinates_(\n      flatCoordinates,\n      0,\n      flatCoordinates.length,\n      stride\n    );\n    this.hitDetectionInstructions.push(strokeInstruction);\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @param {import(\"../../geom/MultiLineString.js\").default|import(\"../Feature.js\").default} multiLineStringGeometry MultiLineString geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiLineString(multiLineStringGeometry, feature) {\n    const state = this.state;\n    const strokeStyle = state.strokeStyle;\n    const lineWidth = state.lineWidth;\n    if (strokeStyle === undefined || lineWidth === undefined) {\n      return;\n    }\n    this.updateStrokeStyle(state, this.applyStroke);\n    this.beginGeometry(multiLineStringGeometry, feature);\n    this.hitDetectionInstructions.push(\n      [\n        CanvasInstruction.SET_STROKE_STYLE,\n        state.strokeStyle,\n        state.lineWidth,\n        state.lineCap,\n        state.lineJoin,\n        state.miterLimit,\n        defaultLineDash,\n        defaultLineDashOffset,\n      ],\n      beginPathInstruction\n    );\n    const ends = multiLineStringGeometry.getEnds();\n    const flatCoordinates = multiLineStringGeometry.getFlatCoordinates();\n    const stride = multiLineStringGeometry.getStride();\n    let offset = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      offset = this.drawFlatCoordinates_(\n        flatCoordinates,\n        offset,\n        /** @type {number} */ (ends[i]),\n        stride\n      );\n    }\n    this.hitDetectionInstructions.push(strokeInstruction);\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @return {import(\"../canvas.js\").SerializableInstructions} the serializable instructions.\n   */\n  finish() {\n    const state = this.state;\n    if (\n      state.lastStroke != undefined &&\n      state.lastStroke != this.coordinates.length\n    ) {\n      this.instructions.push(strokeInstruction);\n    }\n    this.reverseHitDetectionInstructions();\n    this.state = null;\n    return super.finish();\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillStrokeState} state State.\n   */\n  applyStroke(state) {\n    if (\n      state.lastStroke != undefined &&\n      state.lastStroke != this.coordinates.length\n    ) {\n      this.instructions.push(strokeInstruction);\n      state.lastStroke = this.coordinates.length;\n    }\n    state.lastStroke = 0;\n    super.applyStroke(state);\n    this.instructions.push(beginPathInstruction);\n  }\n}\n\nexport default CanvasLineStringBuilder;\n", "/**\n * @module ol/render/canvas/PolygonBuilder\n */\nimport CanvasBuilder from './Builder.js';\nimport CanvasInstruction, {\n  beginPathInstruction,\n  closePathInstruction,\n  fillInstruction,\n  strokeInstruction,\n} from './Instruction.js';\nimport {\n  defaultFillStyle,\n  defaultLineDash,\n  defaultLineDashOffset,\n} from '../canvas.js';\nimport {snap} from '../../geom/flat/simplify.js';\n\nclass CanvasPolygonBuilder extends CanvasBuilder {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Maximum extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    super(tolerance, maxExtent, resolution, pixelRatio);\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {Array<number>} ends Ends.\n   * @param {number} stride Stride.\n   * @private\n   * @return {number} End.\n   */\n  drawFlatCoordinatess_(flatCoordinates, offset, ends, stride) {\n    const state = this.state;\n    const fill = state.fillStyle !== undefined;\n    const stroke = state.strokeStyle !== undefined;\n    const numEnds = ends.length;\n    this.instructions.push(beginPathInstruction);\n    this.hitDetectionInstructions.push(beginPathInstruction);\n    for (let i = 0; i < numEnds; ++i) {\n      const end = ends[i];\n      const myBegin = this.coordinates.length;\n      const myEnd = this.appendFlatLineCoordinates(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        true,\n        !stroke\n      );\n      const moveToLineToInstruction = [\n        CanvasInstruction.MOVE_TO_LINE_TO,\n        myBegin,\n        myEnd,\n      ];\n      this.instructions.push(moveToLineToInstruction);\n      this.hitDetectionInstructions.push(moveToLineToInstruction);\n      if (stroke) {\n        // Performance optimization: only call closePath() when we have a stroke.\n        // Otherwise the ring is closed already (see appendFlatLineCoordinates above).\n        this.instructions.push(closePathInstruction);\n        this.hitDetectionInstructions.push(closePathInstruction);\n      }\n      offset = end;\n    }\n    if (fill) {\n      this.instructions.push(fillInstruction);\n      this.hitDetectionInstructions.push(fillInstruction);\n    }\n    if (stroke) {\n      this.instructions.push(strokeInstruction);\n      this.hitDetectionInstructions.push(strokeInstruction);\n    }\n    return offset;\n  }\n\n  /**\n   * @param {import(\"../../geom/Circle.js\").default} circleGeometry Circle geometry.\n   * @param {import(\"../../Feature.js\").default} feature Feature.\n   */\n  drawCircle(circleGeometry, feature) {\n    const state = this.state;\n    const fillStyle = state.fillStyle;\n    const strokeStyle = state.strokeStyle;\n    if (fillStyle === undefined && strokeStyle === undefined) {\n      return;\n    }\n    this.setFillStrokeStyles_();\n    this.beginGeometry(circleGeometry, feature);\n    if (state.fillStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_FILL_STYLE,\n        defaultFillStyle,\n      ]);\n    }\n    if (state.strokeStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_STROKE_STYLE,\n        state.strokeStyle,\n        state.lineWidth,\n        state.lineCap,\n        state.lineJoin,\n        state.miterLimit,\n        defaultLineDash,\n        defaultLineDashOffset,\n      ]);\n    }\n    const flatCoordinates = circleGeometry.getFlatCoordinates();\n    const stride = circleGeometry.getStride();\n    const myBegin = this.coordinates.length;\n    this.appendFlatLineCoordinates(\n      flatCoordinates,\n      0,\n      flatCoordinates.length,\n      stride,\n      false,\n      false\n    );\n    const circleInstruction = [CanvasInstruction.CIRCLE, myBegin];\n    this.instructions.push(beginPathInstruction, circleInstruction);\n    this.hitDetectionInstructions.push(beginPathInstruction, circleInstruction);\n    if (state.fillStyle !== undefined) {\n      this.instructions.push(fillInstruction);\n      this.hitDetectionInstructions.push(fillInstruction);\n    }\n    if (state.strokeStyle !== undefined) {\n      this.instructions.push(strokeInstruction);\n      this.hitDetectionInstructions.push(strokeInstruction);\n    }\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @param {import(\"../../geom/Polygon.js\").default|import(\"../Feature.js\").default} polygonGeometry Polygon geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawPolygon(polygonGeometry, feature) {\n    const state = this.state;\n    const fillStyle = state.fillStyle;\n    const strokeStyle = state.strokeStyle;\n    if (fillStyle === undefined && strokeStyle === undefined) {\n      return;\n    }\n    this.setFillStrokeStyles_();\n    this.beginGeometry(polygonGeometry, feature);\n    if (state.fillStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_FILL_STYLE,\n        defaultFillStyle,\n      ]);\n    }\n    if (state.strokeStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_STROKE_STYLE,\n        state.strokeStyle,\n        state.lineWidth,\n        state.lineCap,\n        state.lineJoin,\n        state.miterLimit,\n        defaultLineDash,\n        defaultLineDashOffset,\n      ]);\n    }\n    const ends = polygonGeometry.getEnds();\n    const flatCoordinates = polygonGeometry.getOrientedFlatCoordinates();\n    const stride = polygonGeometry.getStride();\n    this.drawFlatCoordinatess_(\n      flatCoordinates,\n      0,\n      /** @type {Array<number>} */ (ends),\n      stride\n    );\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @param {import(\"../../geom/MultiPolygon.js\").default} multiPolygonGeometry MultiPolygon geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawMultiPolygon(multiPolygonGeometry, feature) {\n    const state = this.state;\n    const fillStyle = state.fillStyle;\n    const strokeStyle = state.strokeStyle;\n    if (fillStyle === undefined && strokeStyle === undefined) {\n      return;\n    }\n    this.setFillStrokeStyles_();\n    this.beginGeometry(multiPolygonGeometry, feature);\n    if (state.fillStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_FILL_STYLE,\n        defaultFillStyle,\n      ]);\n    }\n    if (state.strokeStyle !== undefined) {\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.SET_STROKE_STYLE,\n        state.strokeStyle,\n        state.lineWidth,\n        state.lineCap,\n        state.lineJoin,\n        state.miterLimit,\n        defaultLineDash,\n        defaultLineDashOffset,\n      ]);\n    }\n    const endss = multiPolygonGeometry.getEndss();\n    const flatCoordinates = multiPolygonGeometry.getOrientedFlatCoordinates();\n    const stride = multiPolygonGeometry.getStride();\n    let offset = 0;\n    for (let i = 0, ii = endss.length; i < ii; ++i) {\n      offset = this.drawFlatCoordinatess_(\n        flatCoordinates,\n        offset,\n        endss[i],\n        stride\n      );\n    }\n    this.endGeometry(feature);\n  }\n\n  /**\n   * @return {import(\"../canvas.js\").SerializableInstructions} the serializable instructions.\n   */\n  finish() {\n    this.reverseHitDetectionInstructions();\n    this.state = null;\n    // We want to preserve topology when drawing polygons.  Polygons are\n    // simplified using quantization and point elimination. However, we might\n    // have received a mix of quantized and non-quantized geometries, so ensure\n    // that all are quantized by quantizing all coordinates in the batch.\n    const tolerance = this.tolerance;\n    if (tolerance !== 0) {\n      const coordinates = this.coordinates;\n      for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n        coordinates[i] = snap(coordinates[i], tolerance);\n      }\n    }\n    return super.finish();\n  }\n\n  /**\n   * @private\n   */\n  setFillStrokeStyles_() {\n    const state = this.state;\n    const fillStyle = state.fillStyle;\n    if (fillStyle !== undefined) {\n      this.updateFillStyle(state, this.createFill);\n    }\n    if (state.strokeStyle !== undefined) {\n      this.updateStrokeStyle(state, this.applyStroke);\n    }\n  }\n}\n\nexport default CanvasPolygonBuilder;\n", "import {lerp} from '../../math.js';\n\n/**\n * Creates chunks of equal length from a linestring\n * @param {number} chunkLength Length of each chunk.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Start offset of the `flatCoordinates`.\n * @param {number} end End offset of the `flatCoordinates`.\n * @param {number} stride Stride.\n * @return {Array<Array<number>>} Chunks of linestrings with stride 2.\n */\nexport function lineChunk(chunkLength, flatCoordinates, offset, end, stride) {\n  const chunks = [];\n  let cursor = offset;\n  let chunkM = 0;\n  let currentChunk = flatCoordinates.slice(offset, 2);\n  while (chunkM < chunkLength && cursor + stride < end) {\n    const [x1, y1] = currentChunk.slice(-2);\n    const x2 = flatCoordinates[cursor + stride];\n    const y2 = flatCoordinates[cursor + stride + 1];\n    const segmentLength = Math.sqrt(\n      (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1)\n    );\n    chunkM += segmentLength;\n    if (chunkM >= chunkLength) {\n      const m = (chunkLength - chunkM + segmentLength) / segmentLength;\n      const x = lerp(x1, x2, m);\n      const y = lerp(y1, y2, m);\n      currentChunk.push(x, y);\n      chunks.push(currentChunk);\n      currentChunk = [x, y];\n      if (chunkM == chunkLength) {\n        cursor += stride;\n      }\n      chunkM = 0;\n    } else if (chunkM < chunkLength) {\n      currentChunk.push(\n        flatCoordinates[cursor + stride],\n        flatCoordinates[cursor + stride + 1]\n      );\n      cursor += stride;\n    } else {\n      const missing = segmentLength - chunkM;\n      const x = lerp(x1, x2, missing / segmentLength);\n      const y = lerp(y1, y2, missing / segmentLength);\n      currentChunk.push(x, y);\n      chunks.push(currentChunk);\n      currentChunk = [x, y];\n      chunkM = 0;\n      cursor += stride;\n    }\n  }\n  if (chunkM > 0) {\n    chunks.push(currentChunk);\n  }\n  return chunks;\n}\n", "/**\n * @module ol/geom/flat/straightchunk\n */\n\n/**\n * @param {number} maxAngle Maximum acceptable angle delta between segments.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {Array<number>} Start and end of the first suitable chunk of the\n * given `flatCoordinates`.\n */\nexport function matchingChunk(maxAngle, flatCoordinates, offset, end, stride) {\n  let chunkStart = offset;\n  let chunkEnd = offset;\n  let chunkM = 0;\n  let m = 0;\n  let start = offset;\n  let acos, i, m12, m23, x1, y1, x12, y12, x23, y23;\n  for (i = offset; i < end; i += stride) {\n    const x2 = flatCoordinates[i];\n    const y2 = flatCoordinates[i + 1];\n    if (x1 !== undefined) {\n      x23 = x2 - x1;\n      y23 = y2 - y1;\n      m23 = Math.sqrt(x23 * x23 + y23 * y23);\n      if (x12 !== undefined) {\n        m += m12;\n        acos = Math.acos((x12 * x23 + y12 * y23) / (m12 * m23));\n        if (acos > maxAngle) {\n          if (m > chunkM) {\n            chunkM = m;\n            chunkStart = start;\n            chunkEnd = i;\n          }\n          m = 0;\n          start = i - stride;\n        }\n      }\n      m12 = m23;\n      x12 = x23;\n      y12 = y23;\n    }\n    x1 = x2;\n    y1 = y2;\n  }\n  m += m23;\n  return m > chunkM ? [start, i] : [chunkStart, chunkEnd];\n}\n", "/**\n * @module ol/render/canvas/TextBuilder\n */\nimport CanvasBuilder from './Builder.js';\nimport CanvasInstruction from './Instruction.js';\nimport {asColorLike} from '../../colorlike.js';\nimport {\n  defaultFillStyle,\n  defaultFont,\n  defaultLineCap,\n  defaultLineDash,\n  defaultLineDashOffset,\n  defaultLineJoin,\n  defaultLineWidth,\n  defaultMiterLimit,\n  defaultPadding,\n  defaultStrokeStyle,\n  defaultTextAlign,\n  defaultTextBaseline,\n  registerFont,\n} from '../canvas.js';\nimport {getUid} from '../../util.js';\nimport {intersects} from '../../extent.js';\nimport {lineChunk} from '../../geom/flat/linechunk.js';\nimport {matchingChunk} from '../../geom/flat/straightchunk.js';\n/**\n * @const\n * @type {{left: 0, center: 0.5, right: 1, top: 0, middle: 0.5, hanging: 0.2, alphabetic: 0.8, ideographic: 0.8, bottom: 1}}\n */\nexport const TEXT_ALIGN = {\n  'left': 0,\n  'center': 0.5,\n  'right': 1,\n  'top': 0,\n  'middle': 0.5,\n  'hanging': 0.2,\n  'alphabetic': 0.8,\n  'ideographic': 0.8,\n  'bottom': 1,\n};\n\nclass CanvasTextBuilder extends CanvasBuilder {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Maximum extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    super(tolerance, maxExtent, resolution, pixelRatio);\n\n    /**\n     * @private\n     * @type {Array<HTMLCanvasElement>}\n     */\n    this.labels_ = null;\n\n    /**\n     * @private\n     * @type {string|Array<string>}\n     */\n    this.text_ = '';\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textOffsetX_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textOffsetY_ = 0;\n\n    /**\n     * @private\n     * @type {boolean|undefined}\n     */\n    this.textRotateWithView_ = undefined;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textRotation_ = 0;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").FillState}\n     */\n    this.textFillState_ = null;\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").FillState>}\n     */\n    this.fillStates = {};\n    this.fillStates[defaultFillStyle] = {fillStyle: defaultFillStyle};\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").StrokeState}\n     */\n    this.textStrokeState_ = null;\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").StrokeState>}\n     */\n    this.strokeStates = {};\n\n    /**\n     * @private\n     * @type {import(\"../canvas.js\").TextState}\n     */\n    this.textState_ = /** @type {import(\"../canvas.js\").TextState} */ ({});\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").TextState>}\n     */\n    this.textStates = {};\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.textKey_ = '';\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.fillKey_ = '';\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.strokeKey_ = '';\n\n    /**\n     * Data shared with an image builder for combined decluttering.\n     * @private\n     * @type {import(\"../canvas.js\").DeclutterImageWithText}\n     */\n    this.declutterImageWithText_ = undefined;\n  }\n\n  /**\n   * @return {import(\"../canvas.js\").SerializableInstructions} the serializable instructions.\n   */\n  finish() {\n    const instructions = super.finish();\n    instructions.textStates = this.textStates;\n    instructions.fillStates = this.fillStates;\n    instructions.strokeStates = this.strokeStates;\n    return instructions;\n  }\n\n  /**\n   * @param {import(\"../../geom/SimpleGeometry.js\").default|import(\"../Feature.js\").default} geometry Geometry.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   */\n  drawText(geometry, feature) {\n    const fillState = this.textFillState_;\n    const strokeState = this.textStrokeState_;\n    const textState = this.textState_;\n    if (this.text_ === '' || !textState || (!fillState && !strokeState)) {\n      return;\n    }\n\n    const coordinates = this.coordinates;\n    let begin = coordinates.length;\n\n    const geometryType = geometry.getType();\n    let flatCoordinates = null;\n    let stride = geometry.getStride();\n\n    if (\n      textState.placement === 'line' &&\n      (geometryType == 'LineString' ||\n        geometryType == 'MultiLineString' ||\n        geometryType == 'Polygon' ||\n        geometryType == 'MultiPolygon')\n    ) {\n      if (!intersects(this.getBufferedMaxExtent(), geometry.getExtent())) {\n        return;\n      }\n      let ends;\n      flatCoordinates = geometry.getFlatCoordinates();\n      if (geometryType == 'LineString') {\n        ends = [flatCoordinates.length];\n      } else if (geometryType == 'MultiLineString') {\n        ends = /** @type {import(\"../../geom/MultiLineString.js\").default} */ (\n          geometry\n        ).getEnds();\n      } else if (geometryType == 'Polygon') {\n        ends = /** @type {import(\"../../geom/Polygon.js\").default} */ (geometry)\n          .getEnds()\n          .slice(0, 1);\n      } else if (geometryType == 'MultiPolygon') {\n        const endss =\n          /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (\n            geometry\n          ).getEndss();\n        ends = [];\n        for (let i = 0, ii = endss.length; i < ii; ++i) {\n          ends.push(endss[i][0]);\n        }\n      }\n      this.beginGeometry(geometry, feature);\n      const repeat = textState.repeat;\n      const textAlign = repeat ? undefined : textState.textAlign;\n      // No `justify` support for line placement.\n      let flatOffset = 0;\n      for (let o = 0, oo = ends.length; o < oo; ++o) {\n        let chunks;\n        if (repeat) {\n          chunks = lineChunk(\n            repeat * this.resolution,\n            flatCoordinates,\n            flatOffset,\n            ends[o],\n            stride\n          );\n        } else {\n          chunks = [flatCoordinates.slice(flatOffset, ends[o])];\n        }\n        for (let c = 0, cc = chunks.length; c < cc; ++c) {\n          const chunk = chunks[c];\n          let chunkBegin = 0;\n          let chunkEnd = chunk.length;\n          if (textAlign == undefined) {\n            const range = matchingChunk(\n              textState.maxAngle,\n              chunk,\n              0,\n              chunk.length,\n              2\n            );\n            chunkBegin = range[0];\n            chunkEnd = range[1];\n          }\n          for (let i = chunkBegin; i < chunkEnd; i += stride) {\n            coordinates.push(chunk[i], chunk[i + 1]);\n          }\n          const end = coordinates.length;\n          flatOffset = ends[o];\n          this.drawChars_(begin, end);\n          begin = end;\n        }\n      }\n      this.endGeometry(feature);\n    } else {\n      let geometryWidths = textState.overflow ? null : [];\n      switch (geometryType) {\n        case 'Point':\n        case 'MultiPoint':\n          flatCoordinates =\n            /** @type {import(\"../../geom/MultiPoint.js\").default} */ (\n              geometry\n            ).getFlatCoordinates();\n          break;\n        case 'LineString':\n          flatCoordinates =\n            /** @type {import(\"../../geom/LineString.js\").default} */ (\n              geometry\n            ).getFlatMidpoint();\n          break;\n        case 'Circle':\n          flatCoordinates =\n            /** @type {import(\"../../geom/Circle.js\").default} */ (\n              geometry\n            ).getCenter();\n          break;\n        case 'MultiLineString':\n          flatCoordinates =\n            /** @type {import(\"../../geom/MultiLineString.js\").default} */ (\n              geometry\n            ).getFlatMidpoints();\n          stride = 2;\n          break;\n        case 'Polygon':\n          flatCoordinates =\n            /** @type {import(\"../../geom/Polygon.js\").default} */ (\n              geometry\n            ).getFlatInteriorPoint();\n          if (!textState.overflow) {\n            geometryWidths.push(flatCoordinates[2] / this.resolution);\n          }\n          stride = 3;\n          break;\n        case 'MultiPolygon':\n          const interiorPoints =\n            /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (\n              geometry\n            ).getFlatInteriorPoints();\n          flatCoordinates = [];\n          for (let i = 0, ii = interiorPoints.length; i < ii; i += 3) {\n            if (!textState.overflow) {\n              geometryWidths.push(interiorPoints[i + 2] / this.resolution);\n            }\n            flatCoordinates.push(interiorPoints[i], interiorPoints[i + 1]);\n          }\n          if (flatCoordinates.length === 0) {\n            return;\n          }\n          stride = 2;\n          break;\n        default:\n      }\n      const end = this.appendFlatPointCoordinates(flatCoordinates, stride);\n      if (end === begin) {\n        return;\n      }\n      if (\n        geometryWidths &&\n        (end - begin) / 2 !== flatCoordinates.length / stride\n      ) {\n        let beg = begin / 2;\n        geometryWidths = geometryWidths.filter((w, i) => {\n          const keep =\n            coordinates[(beg + i) * 2] === flatCoordinates[i * stride] &&\n            coordinates[(beg + i) * 2 + 1] === flatCoordinates[i * stride + 1];\n          if (!keep) {\n            --beg;\n          }\n          return keep;\n        });\n      }\n\n      this.saveTextStates_();\n\n      if (textState.backgroundFill || textState.backgroundStroke) {\n        this.setFillStrokeStyle(\n          textState.backgroundFill,\n          textState.backgroundStroke\n        );\n        if (textState.backgroundFill) {\n          this.updateFillStyle(this.state, this.createFill);\n        }\n        if (textState.backgroundStroke) {\n          this.updateStrokeStyle(this.state, this.applyStroke);\n          this.hitDetectionInstructions.push(this.createStroke(this.state));\n        }\n      }\n\n      this.beginGeometry(geometry, feature);\n\n      // adjust padding for negative scale\n      let padding = textState.padding;\n      if (\n        padding != defaultPadding &&\n        (textState.scale[0] < 0 || textState.scale[1] < 0)\n      ) {\n        let p0 = textState.padding[0];\n        let p1 = textState.padding[1];\n        let p2 = textState.padding[2];\n        let p3 = textState.padding[3];\n        if (textState.scale[0] < 0) {\n          p1 = -p1;\n          p3 = -p3;\n        }\n        if (textState.scale[1] < 0) {\n          p0 = -p0;\n          p2 = -p2;\n        }\n        padding = [p0, p1, p2, p3];\n      }\n\n      // The image is unknown at this stage so we pass null; it will be computed at render time.\n      // For clarity, we pass NaN for offsetX, offsetY, width and height, which will be computed at\n      // render time.\n      const pixelRatio = this.pixelRatio;\n      this.instructions.push([\n        CanvasInstruction.DRAW_IMAGE,\n        begin,\n        end,\n        null,\n        NaN,\n        NaN,\n        NaN,\n        1,\n        0,\n        0,\n        this.textRotateWithView_,\n        this.textRotation_,\n        [1, 1],\n        NaN,\n        undefined,\n        this.declutterImageWithText_,\n        padding == defaultPadding\n          ? defaultPadding\n          : padding.map(function (p) {\n              return p * pixelRatio;\n            }),\n        !!textState.backgroundFill,\n        !!textState.backgroundStroke,\n        this.text_,\n        this.textKey_,\n        this.strokeKey_,\n        this.fillKey_,\n        this.textOffsetX_,\n        this.textOffsetY_,\n        geometryWidths,\n      ]);\n      const scale = 1 / pixelRatio;\n      // Set default fill for hit detection background\n      const currentFillStyle = this.state.fillStyle;\n      if (textState.backgroundFill) {\n        this.state.fillStyle = defaultFillStyle;\n        this.hitDetectionInstructions.push(this.createFill(this.state));\n      }\n      this.hitDetectionInstructions.push([\n        CanvasInstruction.DRAW_IMAGE,\n        begin,\n        end,\n        null,\n        NaN,\n        NaN,\n        NaN,\n        1,\n        0,\n        0,\n        this.textRotateWithView_,\n        this.textRotation_,\n        [scale, scale],\n        NaN,\n        undefined,\n        this.declutterImageWithText_,\n        padding,\n        !!textState.backgroundFill,\n        !!textState.backgroundStroke,\n        this.text_,\n        this.textKey_,\n        this.strokeKey_,\n        this.fillKey_ ? defaultFillStyle : this.fillKey_,\n        this.textOffsetX_,\n        this.textOffsetY_,\n        geometryWidths,\n      ]);\n      // Reset previous fill\n      if (textState.backgroundFill) {\n        this.state.fillStyle = currentFillStyle;\n        this.hitDetectionInstructions.push(this.createFill(this.state));\n      }\n\n      this.endGeometry(feature);\n    }\n  }\n\n  /**\n   * @private\n   */\n  saveTextStates_() {\n    const strokeState = this.textStrokeState_;\n    const textState = this.textState_;\n    const fillState = this.textFillState_;\n\n    const strokeKey = this.strokeKey_;\n    if (strokeState) {\n      if (!(strokeKey in this.strokeStates)) {\n        this.strokeStates[strokeKey] = {\n          strokeStyle: strokeState.strokeStyle,\n          lineCap: strokeState.lineCap,\n          lineDashOffset: strokeState.lineDashOffset,\n          lineWidth: strokeState.lineWidth,\n          lineJoin: strokeState.lineJoin,\n          miterLimit: strokeState.miterLimit,\n          lineDash: strokeState.lineDash,\n        };\n      }\n    }\n    const textKey = this.textKey_;\n    if (!(textKey in this.textStates)) {\n      this.textStates[textKey] = {\n        font: textState.font,\n        textAlign: textState.textAlign || defaultTextAlign,\n        justify: textState.justify,\n        textBaseline: textState.textBaseline || defaultTextBaseline,\n        scale: textState.scale,\n      };\n    }\n    const fillKey = this.fillKey_;\n    if (fillState) {\n      if (!(fillKey in this.fillStates)) {\n        this.fillStates[fillKey] = {\n          fillStyle: fillState.fillStyle,\n        };\n      }\n    }\n  }\n\n  /**\n   * @private\n   * @param {number} begin Begin.\n   * @param {number} end End.\n   */\n  drawChars_(begin, end) {\n    const strokeState = this.textStrokeState_;\n    const textState = this.textState_;\n\n    const strokeKey = this.strokeKey_;\n    const textKey = this.textKey_;\n    const fillKey = this.fillKey_;\n    this.saveTextStates_();\n\n    const pixelRatio = this.pixelRatio;\n    const baseline = TEXT_ALIGN[textState.textBaseline];\n\n    const offsetY = this.textOffsetY_ * pixelRatio;\n    const text = this.text_;\n    const strokeWidth = strokeState\n      ? (strokeState.lineWidth * Math.abs(textState.scale[0])) / 2\n      : 0;\n\n    this.instructions.push([\n      CanvasInstruction.DRAW_CHARS,\n      begin,\n      end,\n      baseline,\n      textState.overflow,\n      fillKey,\n      textState.maxAngle,\n      pixelRatio,\n      offsetY,\n      strokeKey,\n      strokeWidth * pixelRatio,\n      text,\n      textKey,\n      1,\n    ]);\n    this.hitDetectionInstructions.push([\n      CanvasInstruction.DRAW_CHARS,\n      begin,\n      end,\n      baseline,\n      textState.overflow,\n      fillKey ? defaultFillStyle : fillKey,\n      textState.maxAngle,\n      1,\n      offsetY,\n      strokeKey,\n      strokeWidth,\n      text,\n      textKey,\n      1 / pixelRatio,\n    ]);\n  }\n\n  /**\n   * @param {import(\"../../style/Text.js\").default} textStyle Text style.\n   * @param {Object} [sharedData] Shared data.\n   */\n  setTextStyle(textStyle, sharedData) {\n    let textState, fillState, strokeState;\n    if (!textStyle) {\n      this.text_ = '';\n    } else {\n      const textFillStyle = textStyle.getFill();\n      if (!textFillStyle) {\n        fillState = null;\n        this.textFillState_ = fillState;\n      } else {\n        fillState = this.textFillState_;\n        if (!fillState) {\n          fillState = /** @type {import(\"../canvas.js\").FillState} */ ({});\n          this.textFillState_ = fillState;\n        }\n        fillState.fillStyle = asColorLike(\n          textFillStyle.getColor() || defaultFillStyle\n        );\n      }\n\n      const textStrokeStyle = textStyle.getStroke();\n      if (!textStrokeStyle) {\n        strokeState = null;\n        this.textStrokeState_ = strokeState;\n      } else {\n        strokeState = this.textStrokeState_;\n        if (!strokeState) {\n          strokeState = /** @type {import(\"../canvas.js\").StrokeState} */ ({});\n          this.textStrokeState_ = strokeState;\n        }\n        const lineDash = textStrokeStyle.getLineDash();\n        const lineDashOffset = textStrokeStyle.getLineDashOffset();\n        const lineWidth = textStrokeStyle.getWidth();\n        const miterLimit = textStrokeStyle.getMiterLimit();\n        strokeState.lineCap = textStrokeStyle.getLineCap() || defaultLineCap;\n        strokeState.lineDash = lineDash ? lineDash.slice() : defaultLineDash;\n        strokeState.lineDashOffset =\n          lineDashOffset === undefined ? defaultLineDashOffset : lineDashOffset;\n        strokeState.lineJoin = textStrokeStyle.getLineJoin() || defaultLineJoin;\n        strokeState.lineWidth =\n          lineWidth === undefined ? defaultLineWidth : lineWidth;\n        strokeState.miterLimit =\n          miterLimit === undefined ? defaultMiterLimit : miterLimit;\n        strokeState.strokeStyle = asColorLike(\n          textStrokeStyle.getColor() || defaultStrokeStyle\n        );\n      }\n\n      textState = this.textState_;\n      const font = textStyle.getFont() || defaultFont;\n      registerFont(font);\n      const textScale = textStyle.getScaleArray();\n      textState.overflow = textStyle.getOverflow();\n      textState.font = font;\n      textState.maxAngle = textStyle.getMaxAngle();\n      textState.placement = textStyle.getPlacement();\n      textState.textAlign = textStyle.getTextAlign();\n      textState.repeat = textStyle.getRepeat();\n      textState.justify = textStyle.getJustify();\n      textState.textBaseline =\n        textStyle.getTextBaseline() || defaultTextBaseline;\n      textState.backgroundFill = textStyle.getBackgroundFill();\n      textState.backgroundStroke = textStyle.getBackgroundStroke();\n      textState.padding = textStyle.getPadding() || defaultPadding;\n      textState.scale = textScale === undefined ? [1, 1] : textScale;\n\n      const textOffsetX = textStyle.getOffsetX();\n      const textOffsetY = textStyle.getOffsetY();\n      const textRotateWithView = textStyle.getRotateWithView();\n      const textRotation = textStyle.getRotation();\n      this.text_ = textStyle.getText() || '';\n      this.textOffsetX_ = textOffsetX === undefined ? 0 : textOffsetX;\n      this.textOffsetY_ = textOffsetY === undefined ? 0 : textOffsetY;\n      this.textRotateWithView_ =\n        textRotateWithView === undefined ? false : textRotateWithView;\n      this.textRotation_ = textRotation === undefined ? 0 : textRotation;\n\n      this.strokeKey_ = strokeState\n        ? (typeof strokeState.strokeStyle == 'string'\n            ? strokeState.strokeStyle\n            : getUid(strokeState.strokeStyle)) +\n          strokeState.lineCap +\n          strokeState.lineDashOffset +\n          '|' +\n          strokeState.lineWidth +\n          strokeState.lineJoin +\n          strokeState.miterLimit +\n          '[' +\n          strokeState.lineDash.join() +\n          ']'\n        : '';\n      this.textKey_ =\n        textState.font +\n        textState.scale +\n        (textState.textAlign || '?') +\n        (textState.repeat || '?') +\n        (textState.justify || '?') +\n        (textState.textBaseline || '?');\n      this.fillKey_ = fillState\n        ? typeof fillState.fillStyle == 'string'\n          ? fillState.fillStyle\n          : '|' + getUid(fillState.fillStyle)\n        : '';\n    }\n    this.declutterImageWithText_ = sharedData;\n  }\n}\n\nexport default CanvasTextBuilder;\n", "/**\n * @module ol/render/canvas/BuilderGroup\n */\n\nimport Builder from './Builder.js';\nimport ImageBuilder from './ImageBuilder.js';\nimport LineStringBuilder from './LineStringBuilder.js';\nimport PolygonBuilder from './PolygonBuilder.js';\nimport TextBuilder from './TextBuilder.js';\n\n/**\n * @type {Object<import(\"../canvas.js\").BuilderType, typeof Builder>}\n */\nconst BATCH_CONSTRUCTORS = {\n  'Circle': PolygonBuilder,\n  'Default': Builder,\n  'Image': ImageBuilder,\n  'LineString': LineStringBuilder,\n  'Polygon': PolygonBuilder,\n  'Text': TextBuilder,\n};\n\nclass BuilderGroup {\n  /**\n   * @param {number} tolerance Tolerance.\n   * @param {import(\"../../extent.js\").Extent} maxExtent Max extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   */\n  constructor(tolerance, maxExtent, resolution, pixelRatio) {\n    /**\n     * @private\n     * @type {number}\n     */\n    this.tolerance_ = tolerance;\n\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.maxExtent_ = maxExtent;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = pixelRatio;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.resolution_ = resolution;\n\n    /**\n     * @private\n     * @type {!Object<string, !Object<import(\"../canvas.js\").BuilderType, Builder>>}\n     */\n    this.buildersByZIndex_ = {};\n  }\n\n  /**\n   * @return {!Object<string, !Object<import(\"../canvas.js\").BuilderType, import(\"./Builder.js\").SerializableInstructions>>} The serializable instructions\n   */\n  finish() {\n    const builderInstructions = {};\n    for (const zKey in this.buildersByZIndex_) {\n      builderInstructions[zKey] = builderInstructions[zKey] || {};\n      const builders = this.buildersByZIndex_[zKey];\n      for (const builderKey in builders) {\n        const builderInstruction = builders[builderKey].finish();\n        builderInstructions[zKey][builderKey] = builderInstruction;\n      }\n    }\n    return builderInstructions;\n  }\n\n  /**\n   * @param {number|undefined} zIndex Z index.\n   * @param {import(\"../canvas.js\").BuilderType} builderType Replay type.\n   * @return {import(\"../VectorContext.js\").default} Replay.\n   */\n  getBuilder(zIndex, builderType) {\n    const zIndexKey = zIndex !== undefined ? zIndex.toString() : '0';\n    let replays = this.buildersByZIndex_[zIndexKey];\n    if (replays === undefined) {\n      replays = {};\n      this.buildersByZIndex_[zIndexKey] = replays;\n    }\n    let replay = replays[builderType];\n    if (replay === undefined) {\n      const Constructor = BATCH_CONSTRUCTORS[builderType];\n      replay = new Constructor(\n        this.tolerance_,\n        this.maxExtent_,\n        this.resolution_,\n        this.pixelRatio_\n      );\n      replays[builderType] = replay;\n    }\n    return replay;\n  }\n}\n\nexport default BuilderGroup;\n", "/**\n * @module ol/geom/flat/textpath\n */\nimport {lerp} from '../../math.js';\nimport {rotate} from './transform.js';\n\n/**\n * @param {Array<number>} flatCoordinates Path to put text on.\n * @param {number} offset Start offset of the `flatCoordinates`.\n * @param {number} end End offset of the `flatCoordinates`.\n * @param {number} stride Stride.\n * @param {string} text Text to place on the path.\n * @param {number} startM m along the path where the text starts.\n * @param {number} maxAngle Max angle between adjacent chars in radians.\n * @param {number} scale The product of the text scale and the device pixel ratio.\n * @param {function(string, string, Object<string, number>):number} measureAndCacheTextWidth Measure and cache text width.\n * @param {string} font The font.\n * @param {Object<string, number>} cache A cache of measured widths.\n * @param {number} rotation Rotation to apply to the flatCoordinates to determine whether text needs to be reversed.\n * @return {Array<Array<*>>|null} The result array (or null if `maxAngle` was\n * exceeded). Entries of the array are x, y, anchorX, angle, chunk.\n */\nexport function drawTextOnPath(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  text,\n  startM,\n  maxAngle,\n  scale,\n  measureAndCacheTextWidth,\n  font,\n  cache,\n  rotation\n) {\n  let x2 = flatCoordinates[offset];\n  let y2 = flatCoordinates[offset + 1];\n  let x1 = 0;\n  let y1 = 0;\n  let segmentLength = 0;\n  let segmentM = 0;\n\n  function advance() {\n    x1 = x2;\n    y1 = y2;\n    offset += stride;\n    x2 = flatCoordinates[offset];\n    y2 = flatCoordinates[offset + 1];\n    segmentM += segmentLength;\n    segmentLength = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n  do {\n    advance();\n  } while (offset < end - stride && segmentM + segmentLength < startM);\n\n  let interpolate =\n    segmentLength === 0 ? 0 : (startM - segmentM) / segmentLength;\n  const beginX = lerp(x1, x2, interpolate);\n  const beginY = lerp(y1, y2, interpolate);\n\n  const startOffset = offset - stride;\n  const startLength = segmentM;\n  const endM = startM + scale * measureAndCacheTextWidth(font, text, cache);\n  while (offset < end - stride && segmentM + segmentLength < endM) {\n    advance();\n  }\n  interpolate = segmentLength === 0 ? 0 : (endM - segmentM) / segmentLength;\n  const endX = lerp(x1, x2, interpolate);\n  const endY = lerp(y1, y2, interpolate);\n\n  // Keep text upright\n  let reverse;\n  if (rotation) {\n    const flat = [beginX, beginY, endX, endY];\n    rotate(flat, 0, 4, 2, rotation, flat, flat);\n    reverse = flat[0] > flat[2];\n  } else {\n    reverse = beginX > endX;\n  }\n\n  const PI = Math.PI;\n  const result = [];\n  const singleSegment = startOffset + stride === offset;\n\n  offset = startOffset;\n  segmentLength = 0;\n  segmentM = startLength;\n  x2 = flatCoordinates[offset];\n  y2 = flatCoordinates[offset + 1];\n\n  let previousAngle;\n  // All on the same segment\n  if (singleSegment) {\n    advance();\n\n    previousAngle = Math.atan2(y2 - y1, x2 - x1);\n    if (reverse) {\n      previousAngle += previousAngle > 0 ? -PI : PI;\n    }\n    const x = (endX + beginX) / 2;\n    const y = (endY + beginY) / 2;\n    result[0] = [x, y, (endM - startM) / 2, previousAngle, text];\n    return result;\n  }\n\n  // rendering across line segments\n  text = text.replace(/\\n/g, ' '); // ensure rendering in single-line as all calculations below don't handle multi-lines\n\n  for (let i = 0, ii = text.length; i < ii; ) {\n    advance();\n    let angle = Math.atan2(y2 - y1, x2 - x1);\n    if (reverse) {\n      angle += angle > 0 ? -PI : PI;\n    }\n    if (previousAngle !== undefined) {\n      let delta = angle - previousAngle;\n      delta += delta > PI ? -2 * PI : delta < -PI ? 2 * PI : 0;\n      if (Math.abs(delta) > maxAngle) {\n        return null;\n      }\n    }\n    previousAngle = angle;\n\n    const iStart = i;\n    let charLength = 0;\n    for (; i < ii; ++i) {\n      const index = reverse ? ii - i - 1 : i;\n      const len = scale * measureAndCacheTextWidth(font, text[index], cache);\n      if (\n        offset + stride < end &&\n        segmentM + segmentLength < startM + charLength + len / 2\n      ) {\n        break;\n      }\n      charLength += len;\n    }\n    if (i === iStart) {\n      continue;\n    }\n    const chars = reverse\n      ? text.substring(ii - iStart, ii - i)\n      : text.substring(iStart, i);\n    interpolate =\n      segmentLength === 0\n        ? 0\n        : (startM + charLength / 2 - segmentM) / segmentLength;\n    const x = lerp(x1, x2, interpolate);\n    const y = lerp(y1, y2, interpolate);\n    result.push([x, y, charLength / 2, angle, chars]);\n    startM += charLength;\n  }\n  return result;\n}\n", "/**\n * @module ol/render/canvas/Executor\n */\nimport CanvasInstruction from './Instruction.js';\nimport {TEXT_ALIGN} from './TextBuilder.js';\nimport {\n  apply as applyTransform,\n  compose as composeTransform,\n  create as createTransform,\n  setFromArray as transformSetFromArray,\n} from '../../transform.js';\nimport {createEmpty, createOrUpdate, intersects} from '../../extent.js';\nimport {\n  defaultPadding,\n  defaultTextAlign,\n  defaultTextBaseline,\n  drawImageOrLabel,\n  getTextDimensions,\n  measureAndCacheTextWidth,\n} from '../canvas.js';\nimport {drawTextOnPath} from '../../geom/flat/textpath.js';\nimport {equals} from '../../array.js';\nimport {lineStringLength} from '../../geom/flat/length.js';\nimport {transform2D} from '../../geom/flat/transform.js';\n\n/**\n * @typedef {Object} BBox\n * @property {number} minX Minimal x.\n * @property {number} minY Minimal y.\n * @property {number} maxX Maximal x.\n * @property {number} maxY Maximal y\n * @property {*} value Value.\n */\n\n/**\n * @typedef {Object} ImageOrLabelDimensions\n * @property {number} drawImageX DrawImageX.\n * @property {number} drawImageY DrawImageY.\n * @property {number} drawImageW DrawImageW.\n * @property {number} drawImageH DrawImageH.\n * @property {number} originX OriginX.\n * @property {number} originY OriginY.\n * @property {Array<number>} scale Scale.\n * @property {BBox} declutterBox DeclutterBox.\n * @property {import(\"../../transform.js\").Transform} canvasTransform CanvasTransform.\n */\n\n/**\n * @typedef {{0: CanvasRenderingContext2D, 1: number, 2: import(\"../canvas.js\").Label|HTMLImageElement|HTMLCanvasElement|HTMLVideoElement, 3: ImageOrLabelDimensions, 4: number, 5: Array<*>, 6: Array<*>}} ReplayImageOrLabelArgs\n */\n\n/**\n * @template T\n * @typedef {function(import(\"../../Feature.js\").FeatureLike, import(\"../../geom/SimpleGeometry.js\").default): T} FeatureCallback\n */\n\n/**\n * @type {import(\"../../extent.js\").Extent}\n */\nconst tmpExtent = createEmpty();\n\n/** @type {import(\"../../coordinate.js\").Coordinate} */\nconst p1 = [];\n/** @type {import(\"../../coordinate.js\").Coordinate} */\nconst p2 = [];\n/** @type {import(\"../../coordinate.js\").Coordinate} */\nconst p3 = [];\n/** @type {import(\"../../coordinate.js\").Coordinate} */\nconst p4 = [];\n\n/**\n * @param {ReplayImageOrLabelArgs} replayImageOrLabelArgs Arguments to replayImageOrLabel\n * @return {BBox} Declutter bbox.\n */\nfunction getDeclutterBox(replayImageOrLabelArgs) {\n  return replayImageOrLabelArgs[3].declutterBox;\n}\n\nconst rtlRegEx = new RegExp(\n  /* eslint-disable prettier/prettier */\n  '[' +\n    String.fromCharCode(0x00591) + '-' + String.fromCharCode(0x008ff) +\n    String.fromCharCode(0x0fb1d) + '-' + String.fromCharCode(0x0fdff) +\n    String.fromCharCode(0x0fe70) + '-' + String.fromCharCode(0x0fefc) +\n    String.fromCharCode(0x10800) + '-' + String.fromCharCode(0x10fff) +\n    String.fromCharCode(0x1e800) + '-' + String.fromCharCode(0x1efff) +\n  ']'\n  /* eslint-enable prettier/prettier */\n);\n\n/**\n * @param {string} text Text.\n * @param {CanvasTextAlign} align Alignment.\n * @return {number} Text alignment.\n */\nfunction horizontalTextAlign(text, align) {\n  if (align === 'start') {\n    align = rtlRegEx.test(text) ? 'right' : 'left';\n  } else if (align === 'end') {\n    align = rtlRegEx.test(text) ? 'left' : 'right';\n  }\n  return TEXT_ALIGN[align];\n}\n\n/**\n * @param {Array<string>} acc Accumulator.\n * @param {string} line Line of text.\n * @param {number} i Index\n * @return {Array<string>} Accumulator.\n */\nfunction createTextChunks(acc, line, i) {\n  if (i > 0) {\n    acc.push('\\n', '');\n  }\n  acc.push(line, '');\n  return acc;\n}\n\nclass Executor {\n  /**\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {boolean} overlaps The replay can have overlapping geometries.\n   * @param {import(\"../canvas.js\").SerializableInstructions} instructions The serializable instructions\n   */\n  constructor(resolution, pixelRatio, overlaps, instructions) {\n    /**\n     * @protected\n     * @type {boolean}\n     */\n    this.overlaps = overlaps;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.pixelRatio = pixelRatio;\n\n    /**\n     * @protected\n     * @const\n     * @type {number}\n     */\n    this.resolution = resolution;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.alignFill_;\n\n    /**\n     * @protected\n     * @type {Array<*>}\n     */\n    this.instructions = instructions.instructions;\n\n    /**\n     * @protected\n     * @type {Array<number>}\n     */\n    this.coordinates = instructions.coordinates;\n\n    /**\n     * @private\n     * @type {!Object<number,import(\"../../coordinate.js\").Coordinate|Array<import(\"../../coordinate.js\").Coordinate>|Array<Array<import(\"../../coordinate.js\").Coordinate>>>}\n     */\n    this.coordinateCache_ = {};\n\n    /**\n     * @private\n     * @type {!import(\"../../transform.js\").Transform}\n     */\n    this.renderedTransform_ = createTransform();\n\n    /**\n     * @protected\n     * @type {Array<*>}\n     */\n    this.hitDetectionInstructions = instructions.hitDetectionInstructions;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.pixelCoordinates_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.viewRotation_ = 0;\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").FillState>}\n     */\n    this.fillStates = instructions.fillStates || {};\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").StrokeState>}\n     */\n    this.strokeStates = instructions.strokeStates || {};\n\n    /**\n     * @type {!Object<string, import(\"../canvas.js\").TextState>}\n     */\n    this.textStates = instructions.textStates || {};\n\n    /**\n     * @private\n     * @type {Object<string, Object<string, number>>}\n     */\n    this.widths_ = {};\n\n    /**\n     * @private\n     * @type {Object<string, import(\"../canvas.js\").Label>}\n     */\n    this.labels_ = {};\n  }\n\n  /**\n   * @param {string|Array<string>} text Text.\n   * @param {string} textKey Text style key.\n   * @param {string} fillKey Fill style key.\n   * @param {string} strokeKey Stroke style key.\n   * @return {import(\"../canvas.js\").Label} Label.\n   */\n  createLabel(text, textKey, fillKey, strokeKey) {\n    const key = text + textKey + fillKey + strokeKey;\n    if (this.labels_[key]) {\n      return this.labels_[key];\n    }\n    const strokeState = strokeKey ? this.strokeStates[strokeKey] : null;\n    const fillState = fillKey ? this.fillStates[fillKey] : null;\n    const textState = this.textStates[textKey];\n    const pixelRatio = this.pixelRatio;\n    const scale = [\n      textState.scale[0] * pixelRatio,\n      textState.scale[1] * pixelRatio,\n    ];\n    const textIsArray = Array.isArray(text);\n    const align = textState.justify\n      ? TEXT_ALIGN[textState.justify]\n      : horizontalTextAlign(\n          Array.isArray(text) ? text[0] : text,\n          textState.textAlign || defaultTextAlign\n        );\n    const strokeWidth =\n      strokeKey && strokeState.lineWidth ? strokeState.lineWidth : 0;\n\n    const chunks = textIsArray\n      ? text\n      : text.split('\\n').reduce(createTextChunks, []);\n\n    const {width, height, widths, heights, lineWidths} = getTextDimensions(\n      textState,\n      chunks\n    );\n    const renderWidth = width + strokeWidth;\n    const contextInstructions = [];\n    // make canvas 2 pixels wider to account for italic text width measurement errors\n    const w = (renderWidth + 2) * scale[0];\n    const h = (height + strokeWidth) * scale[1];\n    /** @type {import(\"../canvas.js\").Label} */\n    const label = {\n      width: w < 0 ? Math.floor(w) : Math.ceil(w),\n      height: h < 0 ? Math.floor(h) : Math.ceil(h),\n      contextInstructions: contextInstructions,\n    };\n    if (scale[0] != 1 || scale[1] != 1) {\n      contextInstructions.push('scale', scale);\n    }\n    if (strokeKey) {\n      contextInstructions.push('strokeStyle', strokeState.strokeStyle);\n      contextInstructions.push('lineWidth', strokeWidth);\n      contextInstructions.push('lineCap', strokeState.lineCap);\n      contextInstructions.push('lineJoin', strokeState.lineJoin);\n      contextInstructions.push('miterLimit', strokeState.miterLimit);\n      contextInstructions.push('setLineDash', [strokeState.lineDash]);\n      contextInstructions.push('lineDashOffset', strokeState.lineDashOffset);\n    }\n    if (fillKey) {\n      contextInstructions.push('fillStyle', fillState.fillStyle);\n    }\n    contextInstructions.push('textBaseline', 'middle');\n    contextInstructions.push('textAlign', 'center');\n    const leftRight = 0.5 - align;\n    let x = align * renderWidth + leftRight * strokeWidth;\n    const strokeInstructions = [];\n    const fillInstructions = [];\n    let lineHeight = 0;\n    let lineOffset = 0;\n    let widthHeightIndex = 0;\n    let lineWidthIndex = 0;\n    let previousFont;\n    for (let i = 0, ii = chunks.length; i < ii; i += 2) {\n      const text = chunks[i];\n      if (text === '\\n') {\n        lineOffset += lineHeight;\n        lineHeight = 0;\n        x = align * renderWidth + leftRight * strokeWidth;\n        ++lineWidthIndex;\n        continue;\n      }\n      const font = chunks[i + 1] || textState.font;\n      if (font !== previousFont) {\n        if (strokeKey) {\n          strokeInstructions.push('font', font);\n        }\n        if (fillKey) {\n          fillInstructions.push('font', font);\n        }\n        previousFont = font;\n      }\n      lineHeight = Math.max(lineHeight, heights[widthHeightIndex]);\n      const fillStrokeArgs = [\n        text,\n        x +\n          leftRight * widths[widthHeightIndex] +\n          align * (widths[widthHeightIndex] - lineWidths[lineWidthIndex]),\n        0.5 * (strokeWidth + lineHeight) + lineOffset,\n      ];\n      x += widths[widthHeightIndex];\n      if (strokeKey) {\n        strokeInstructions.push('strokeText', fillStrokeArgs);\n      }\n      if (fillKey) {\n        fillInstructions.push('fillText', fillStrokeArgs);\n      }\n      ++widthHeightIndex;\n    }\n    Array.prototype.push.apply(contextInstructions, strokeInstructions);\n    Array.prototype.push.apply(contextInstructions, fillInstructions);\n    this.labels_[key] = label;\n    return label;\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../coordinate.js\").Coordinate} p1 1st point of the background box.\n   * @param {import(\"../../coordinate.js\").Coordinate} p2 2nd point of the background box.\n   * @param {import(\"../../coordinate.js\").Coordinate} p3 3rd point of the background box.\n   * @param {import(\"../../coordinate.js\").Coordinate} p4 4th point of the background box.\n   * @param {Array<*>} fillInstruction Fill instruction.\n   * @param {Array<*>} strokeInstruction Stroke instruction.\n   */\n  replayTextBackground_(\n    context,\n    p1,\n    p2,\n    p3,\n    p4,\n    fillInstruction,\n    strokeInstruction\n  ) {\n    context.beginPath();\n    context.moveTo.apply(context, p1);\n    context.lineTo.apply(context, p2);\n    context.lineTo.apply(context, p3);\n    context.lineTo.apply(context, p4);\n    context.lineTo.apply(context, p1);\n    if (fillInstruction) {\n      this.alignFill_ = /** @type {boolean} */ (fillInstruction[2]);\n      this.fill_(context);\n    }\n    if (strokeInstruction) {\n      this.setStrokeStyle_(\n        context,\n        /** @type {Array<*>} */ (strokeInstruction)\n      );\n      context.stroke();\n    }\n  }\n\n  /**\n   * @private\n   * @param {number} sheetWidth Width of the sprite sheet.\n   * @param {number} sheetHeight Height of the sprite sheet.\n   * @param {number} centerX X.\n   * @param {number} centerY Y.\n   * @param {number} width Width.\n   * @param {number} height Height.\n   * @param {number} anchorX Anchor X.\n   * @param {number} anchorY Anchor Y.\n   * @param {number} originX Origin X.\n   * @param {number} originY Origin Y.\n   * @param {number} rotation Rotation.\n   * @param {import(\"../../size.js\").Size} scale Scale.\n   * @param {boolean} snapToPixel Snap to pixel.\n   * @param {Array<number>} padding Padding.\n   * @param {boolean} fillStroke Background fill or stroke.\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   * @return {ImageOrLabelDimensions} Dimensions for positioning and decluttering the image or label.\n   */\n  calculateImageOrLabelDimensions_(\n    sheetWidth,\n    sheetHeight,\n    centerX,\n    centerY,\n    width,\n    height,\n    anchorX,\n    anchorY,\n    originX,\n    originY,\n    rotation,\n    scale,\n    snapToPixel,\n    padding,\n    fillStroke,\n    feature\n  ) {\n    anchorX *= scale[0];\n    anchorY *= scale[1];\n    let x = centerX - anchorX;\n    let y = centerY - anchorY;\n\n    const w = width + originX > sheetWidth ? sheetWidth - originX : width;\n    const h = height + originY > sheetHeight ? sheetHeight - originY : height;\n    const boxW = padding[3] + w * scale[0] + padding[1];\n    const boxH = padding[0] + h * scale[1] + padding[2];\n    const boxX = x - padding[3];\n    const boxY = y - padding[0];\n\n    if (fillStroke || rotation !== 0) {\n      p1[0] = boxX;\n      p4[0] = boxX;\n      p1[1] = boxY;\n      p2[1] = boxY;\n      p2[0] = boxX + boxW;\n      p3[0] = p2[0];\n      p3[1] = boxY + boxH;\n      p4[1] = p3[1];\n    }\n\n    let transform;\n    if (rotation !== 0) {\n      transform = composeTransform(\n        createTransform(),\n        centerX,\n        centerY,\n        1,\n        1,\n        rotation,\n        -centerX,\n        -centerY\n      );\n\n      applyTransform(transform, p1);\n      applyTransform(transform, p2);\n      applyTransform(transform, p3);\n      applyTransform(transform, p4);\n      createOrUpdate(\n        Math.min(p1[0], p2[0], p3[0], p4[0]),\n        Math.min(p1[1], p2[1], p3[1], p4[1]),\n        Math.max(p1[0], p2[0], p3[0], p4[0]),\n        Math.max(p1[1], p2[1], p3[1], p4[1]),\n        tmpExtent\n      );\n    } else {\n      createOrUpdate(\n        Math.min(boxX, boxX + boxW),\n        Math.min(boxY, boxY + boxH),\n        Math.max(boxX, boxX + boxW),\n        Math.max(boxY, boxY + boxH),\n        tmpExtent\n      );\n    }\n    if (snapToPixel) {\n      x = Math.round(x);\n      y = Math.round(y);\n    }\n    return {\n      drawImageX: x,\n      drawImageY: y,\n      drawImageW: w,\n      drawImageH: h,\n      originX: originX,\n      originY: originY,\n      declutterBox: {\n        minX: tmpExtent[0],\n        minY: tmpExtent[1],\n        maxX: tmpExtent[2],\n        maxY: tmpExtent[3],\n        value: feature,\n      },\n      canvasTransform: transform,\n      scale: scale,\n    };\n  }\n\n  /**\n   * @private\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {number} contextScale Scale of the context.\n   * @param {import(\"../canvas.js\").Label|HTMLImageElement|HTMLCanvasElement|HTMLVideoElement} imageOrLabel Image.\n   * @param {ImageOrLabelDimensions} dimensions Dimensions.\n   * @param {number} opacity Opacity.\n   * @param {Array<*>} fillInstruction Fill instruction.\n   * @param {Array<*>} strokeInstruction Stroke instruction.\n   * @return {boolean} The image or label was rendered.\n   */\n  replayImageOrLabel_(\n    context,\n    contextScale,\n    imageOrLabel,\n    dimensions,\n    opacity,\n    fillInstruction,\n    strokeInstruction\n  ) {\n    const fillStroke = !!(fillInstruction || strokeInstruction);\n\n    const box = dimensions.declutterBox;\n    const canvas = context.canvas;\n    const strokePadding = strokeInstruction\n      ? (strokeInstruction[2] * dimensions.scale[0]) / 2\n      : 0;\n    const intersects =\n      box.minX - strokePadding <= canvas.width / contextScale &&\n      box.maxX + strokePadding >= 0 &&\n      box.minY - strokePadding <= canvas.height / contextScale &&\n      box.maxY + strokePadding >= 0;\n\n    if (intersects) {\n      if (fillStroke) {\n        this.replayTextBackground_(\n          context,\n          p1,\n          p2,\n          p3,\n          p4,\n          /** @type {Array<*>} */ (fillInstruction),\n          /** @type {Array<*>} */ (strokeInstruction)\n        );\n      }\n      drawImageOrLabel(\n        context,\n        dimensions.canvasTransform,\n        opacity,\n        imageOrLabel,\n        dimensions.originX,\n        dimensions.originY,\n        dimensions.drawImageW,\n        dimensions.drawImageH,\n        dimensions.drawImageX,\n        dimensions.drawImageY,\n        dimensions.scale\n      );\n    }\n    return true;\n  }\n\n  /**\n   * @private\n   * @param {CanvasRenderingContext2D} context Context.\n   */\n  fill_(context) {\n    if (this.alignFill_) {\n      const origin = applyTransform(this.renderedTransform_, [0, 0]);\n      const repeatSize = 512 * this.pixelRatio;\n      context.save();\n      context.translate(origin[0] % repeatSize, origin[1] % repeatSize);\n      context.rotate(this.viewRotation_);\n    }\n    context.fill();\n    if (this.alignFill_) {\n      context.restore();\n    }\n  }\n\n  /**\n   * @private\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {Array<*>} instruction Instruction.\n   */\n  setStrokeStyle_(context, instruction) {\n    context['strokeStyle'] =\n      /** @type {import(\"../../colorlike.js\").ColorLike} */ (instruction[1]);\n    context.lineWidth = /** @type {number} */ (instruction[2]);\n    context.lineCap = /** @type {CanvasLineCap} */ (instruction[3]);\n    context.lineJoin = /** @type {CanvasLineJoin} */ (instruction[4]);\n    context.miterLimit = /** @type {number} */ (instruction[5]);\n    context.lineDashOffset = /** @type {number} */ (instruction[7]);\n    context.setLineDash(/** @type {Array<number>} */ (instruction[6]));\n  }\n\n  /**\n   * @private\n   * @param {string|Array<string>} text The text to draw.\n   * @param {string} textKey The key of the text state.\n   * @param {string} strokeKey The key for the stroke state.\n   * @param {string} fillKey The key for the fill state.\n   * @return {{label: import(\"../canvas.js\").Label, anchorX: number, anchorY: number}} The text image and its anchor.\n   */\n  drawLabelWithPointPlacement_(text, textKey, strokeKey, fillKey) {\n    const textState = this.textStates[textKey];\n\n    const label = this.createLabel(text, textKey, fillKey, strokeKey);\n\n    const strokeState = this.strokeStates[strokeKey];\n    const pixelRatio = this.pixelRatio;\n    const align = horizontalTextAlign(\n      Array.isArray(text) ? text[0] : text,\n      textState.textAlign || defaultTextAlign\n    );\n    const baseline = TEXT_ALIGN[textState.textBaseline || defaultTextBaseline];\n    const strokeWidth =\n      strokeState && strokeState.lineWidth ? strokeState.lineWidth : 0;\n\n    // Remove the 2 pixels we added in createLabel() for the anchor\n    const width = label.width / pixelRatio - 2 * textState.scale[0];\n    const anchorX = align * width + 2 * (0.5 - align) * strokeWidth;\n    const anchorY =\n      (baseline * label.height) / pixelRatio +\n      2 * (0.5 - baseline) * strokeWidth;\n\n    return {\n      label: label,\n      anchorX: anchorX,\n      anchorY: anchorY,\n    };\n  }\n\n  /**\n   * @private\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {number} contextScale Scale of the context.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @param {Array<*>} instructions Instructions array.\n   * @param {boolean} snapToPixel Snap point symbols and text to integer pixels.\n   * @param {FeatureCallback<T>} [featureCallback] Feature callback.\n   * @param {import(\"../../extent.js\").Extent} [hitExtent] Only check\n   *     features that intersect this extent.\n   * @param {import(\"rbush\").default} [declutterTree] Declutter tree.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  execute_(\n    context,\n    contextScale,\n    transform,\n    instructions,\n    snapToPixel,\n    featureCallback,\n    hitExtent,\n    declutterTree\n  ) {\n    /** @type {Array<number>} */\n    let pixelCoordinates;\n    if (this.pixelCoordinates_ && equals(transform, this.renderedTransform_)) {\n      pixelCoordinates = this.pixelCoordinates_;\n    } else {\n      if (!this.pixelCoordinates_) {\n        this.pixelCoordinates_ = [];\n      }\n      pixelCoordinates = transform2D(\n        this.coordinates,\n        0,\n        this.coordinates.length,\n        2,\n        transform,\n        this.pixelCoordinates_\n      );\n      transformSetFromArray(this.renderedTransform_, transform);\n    }\n    let i = 0; // instruction index\n    const ii = instructions.length; // end of instructions\n    let d = 0; // data index\n    let dd; // end of per-instruction data\n    let anchorX,\n      anchorY,\n      prevX,\n      prevY,\n      roundX,\n      roundY,\n      image,\n      text,\n      textKey,\n      strokeKey,\n      fillKey;\n    let pendingFill = 0;\n    let pendingStroke = 0;\n    let lastFillInstruction = null;\n    let lastStrokeInstruction = null;\n    const coordinateCache = this.coordinateCache_;\n    const viewRotation = this.viewRotation_;\n    const viewRotationFromTransform =\n      Math.round(Math.atan2(-transform[1], transform[0]) * 1e12) / 1e12;\n\n    const state = /** @type {import(\"../../render.js\").State} */ ({\n      context: context,\n      pixelRatio: this.pixelRatio,\n      resolution: this.resolution,\n      rotation: viewRotation,\n    });\n\n    // When the batch size gets too big, performance decreases. 200 is a good\n    // balance between batch size and number of fill/stroke instructions.\n    const batchSize =\n      this.instructions != instructions || this.overlaps ? 0 : 200;\n    let /** @type {import(\"../../Feature.js\").FeatureLike} */ feature;\n    let x, y, currentGeometry;\n    while (i < ii) {\n      const instruction = instructions[i];\n      const type = /** @type {import(\"./Instruction.js\").default} */ (\n        instruction[0]\n      );\n      switch (type) {\n        case CanvasInstruction.BEGIN_GEOMETRY:\n          feature = /** @type {import(\"../../Feature.js\").FeatureLike} */ (\n            instruction[1]\n          );\n          currentGeometry = instruction[3];\n          if (!feature.getGeometry()) {\n            i = /** @type {number} */ (instruction[2]);\n          } else if (\n            hitExtent !== undefined &&\n            !intersects(hitExtent, currentGeometry.getExtent())\n          ) {\n            i = /** @type {number} */ (instruction[2]) + 1;\n          } else {\n            ++i;\n          }\n          break;\n        case CanvasInstruction.BEGIN_PATH:\n          if (pendingFill > batchSize) {\n            this.fill_(context);\n            pendingFill = 0;\n          }\n          if (pendingStroke > batchSize) {\n            context.stroke();\n            pendingStroke = 0;\n          }\n          if (!pendingFill && !pendingStroke) {\n            context.beginPath();\n            prevX = NaN;\n            prevY = NaN;\n          }\n          ++i;\n          break;\n        case CanvasInstruction.CIRCLE:\n          d = /** @type {number} */ (instruction[1]);\n          const x1 = pixelCoordinates[d];\n          const y1 = pixelCoordinates[d + 1];\n          const x2 = pixelCoordinates[d + 2];\n          const y2 = pixelCoordinates[d + 3];\n          const dx = x2 - x1;\n          const dy = y2 - y1;\n          const r = Math.sqrt(dx * dx + dy * dy);\n          context.moveTo(x1 + r, y1);\n          context.arc(x1, y1, r, 0, 2 * Math.PI, true);\n          ++i;\n          break;\n        case CanvasInstruction.CLOSE_PATH:\n          context.closePath();\n          ++i;\n          break;\n        case CanvasInstruction.CUSTOM:\n          d = /** @type {number} */ (instruction[1]);\n          dd = instruction[2];\n          const geometry =\n            /** @type {import(\"../../geom/SimpleGeometry.js\").default} */ (\n              instruction[3]\n            );\n          const renderer = instruction[4];\n          const fn = instruction.length == 6 ? instruction[5] : undefined;\n          state.geometry = geometry;\n          state.feature = feature;\n          if (!(i in coordinateCache)) {\n            coordinateCache[i] = [];\n          }\n          const coords = coordinateCache[i];\n          if (fn) {\n            fn(pixelCoordinates, d, dd, 2, coords);\n          } else {\n            coords[0] = pixelCoordinates[d];\n            coords[1] = pixelCoordinates[d + 1];\n            coords.length = 2;\n          }\n          renderer(coords, state);\n          ++i;\n          break;\n        case CanvasInstruction.DRAW_IMAGE:\n          d = /** @type {number} */ (instruction[1]);\n          dd = /** @type {number} */ (instruction[2]);\n          image =\n            /** @type {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement} */ (\n              instruction[3]\n            );\n\n          // Remaining arguments in DRAW_IMAGE are in alphabetical order\n          anchorX = /** @type {number} */ (instruction[4]);\n          anchorY = /** @type {number} */ (instruction[5]);\n          let height = /** @type {number} */ (instruction[6]);\n          const opacity = /** @type {number} */ (instruction[7]);\n          const originX = /** @type {number} */ (instruction[8]);\n          const originY = /** @type {number} */ (instruction[9]);\n          const rotateWithView = /** @type {boolean} */ (instruction[10]);\n          let rotation = /** @type {number} */ (instruction[11]);\n          const scale = /** @type {import(\"../../size.js\").Size} */ (\n            instruction[12]\n          );\n          let width = /** @type {number} */ (instruction[13]);\n          const declutterMode =\n            /** @type {\"declutter\"|\"obstacle\"|\"none\"|undefined} */ (\n              instruction[14]\n            );\n          const declutterImageWithText =\n            /** @type {import(\"../canvas.js\").DeclutterImageWithText} */ (\n              instruction[15]\n            );\n\n          if (!image && instruction.length >= 20) {\n            // create label images\n            text = /** @type {string} */ (instruction[19]);\n            textKey = /** @type {string} */ (instruction[20]);\n            strokeKey = /** @type {string} */ (instruction[21]);\n            fillKey = /** @type {string} */ (instruction[22]);\n            const labelWithAnchor = this.drawLabelWithPointPlacement_(\n              text,\n              textKey,\n              strokeKey,\n              fillKey\n            );\n            image = labelWithAnchor.label;\n            instruction[3] = image;\n            const textOffsetX = /** @type {number} */ (instruction[23]);\n            anchorX = (labelWithAnchor.anchorX - textOffsetX) * this.pixelRatio;\n            instruction[4] = anchorX;\n            const textOffsetY = /** @type {number} */ (instruction[24]);\n            anchorY = (labelWithAnchor.anchorY - textOffsetY) * this.pixelRatio;\n            instruction[5] = anchorY;\n            height = image.height;\n            instruction[6] = height;\n            width = image.width;\n            instruction[13] = width;\n          }\n\n          let geometryWidths;\n          if (instruction.length > 25) {\n            geometryWidths = /** @type {number} */ (instruction[25]);\n          }\n\n          let padding, backgroundFill, backgroundStroke;\n          if (instruction.length > 17) {\n            padding = /** @type {Array<number>} */ (instruction[16]);\n            backgroundFill = /** @type {boolean} */ (instruction[17]);\n            backgroundStroke = /** @type {boolean} */ (instruction[18]);\n          } else {\n            padding = defaultPadding;\n            backgroundFill = false;\n            backgroundStroke = false;\n          }\n\n          if (rotateWithView && viewRotationFromTransform) {\n            // Canvas is expected to be rotated to reverse view rotation.\n            rotation += viewRotation;\n          } else if (!rotateWithView && !viewRotationFromTransform) {\n            // Canvas is not rotated, images need to be rotated back to be north-up.\n            rotation -= viewRotation;\n          }\n          let widthIndex = 0;\n          for (; d < dd; d += 2) {\n            if (\n              geometryWidths &&\n              geometryWidths[widthIndex++] < width / this.pixelRatio\n            ) {\n              continue;\n            }\n            const dimensions = this.calculateImageOrLabelDimensions_(\n              image.width,\n              image.height,\n              pixelCoordinates[d],\n              pixelCoordinates[d + 1],\n              width,\n              height,\n              anchorX,\n              anchorY,\n              originX,\n              originY,\n              rotation,\n              scale,\n              snapToPixel,\n              padding,\n              backgroundFill || backgroundStroke,\n              feature\n            );\n            /** @type {ReplayImageOrLabelArgs} */\n            const args = [\n              context,\n              contextScale,\n              image,\n              dimensions,\n              opacity,\n              backgroundFill\n                ? /** @type {Array<*>} */ (lastFillInstruction)\n                : null,\n              backgroundStroke\n                ? /** @type {Array<*>} */ (lastStrokeInstruction)\n                : null,\n            ];\n            if (declutterTree) {\n              if (declutterMode === 'none') {\n                // not rendered in declutter group\n                continue;\n              } else if (declutterMode === 'obstacle') {\n                // will always be drawn, thus no collision detection, but insert as obstacle\n                declutterTree.insert(dimensions.declutterBox);\n                continue;\n              } else {\n                let imageArgs;\n                let imageDeclutterBox;\n                if (declutterImageWithText) {\n                  const index = dd - d;\n                  if (!declutterImageWithText[index]) {\n                    // We now have the image for an image+text combination.\n                    declutterImageWithText[index] = args;\n                    // Don't render anything for now, wait for the text.\n                    continue;\n                  }\n                  imageArgs = declutterImageWithText[index];\n                  delete declutterImageWithText[index];\n                  imageDeclutterBox = getDeclutterBox(imageArgs);\n                  if (declutterTree.collides(imageDeclutterBox)) {\n                    continue;\n                  }\n                }\n                if (declutterTree.collides(dimensions.declutterBox)) {\n                  continue;\n                }\n                if (imageArgs) {\n                  // We now have image and text for an image+text combination.\n                  declutterTree.insert(imageDeclutterBox);\n                  // Render the image before we render the text.\n                  this.replayImageOrLabel_.apply(this, imageArgs);\n                }\n                declutterTree.insert(dimensions.declutterBox);\n              }\n            }\n            this.replayImageOrLabel_.apply(this, args);\n          }\n          ++i;\n          break;\n        case CanvasInstruction.DRAW_CHARS:\n          const begin = /** @type {number} */ (instruction[1]);\n          const end = /** @type {number} */ (instruction[2]);\n          const baseline = /** @type {number} */ (instruction[3]);\n          const overflow = /** @type {number} */ (instruction[4]);\n          fillKey = /** @type {string} */ (instruction[5]);\n          const maxAngle = /** @type {number} */ (instruction[6]);\n          const measurePixelRatio = /** @type {number} */ (instruction[7]);\n          const offsetY = /** @type {number} */ (instruction[8]);\n          strokeKey = /** @type {string} */ (instruction[9]);\n          const strokeWidth = /** @type {number} */ (instruction[10]);\n          text = /** @type {string} */ (instruction[11]);\n          textKey = /** @type {string} */ (instruction[12]);\n          const pixelRatioScale = [\n            /** @type {number} */ (instruction[13]),\n            /** @type {number} */ (instruction[13]),\n          ];\n\n          const textState = this.textStates[textKey];\n          const font = textState.font;\n          const textScale = [\n            textState.scale[0] * measurePixelRatio,\n            textState.scale[1] * measurePixelRatio,\n          ];\n\n          let cachedWidths;\n          if (font in this.widths_) {\n            cachedWidths = this.widths_[font];\n          } else {\n            cachedWidths = {};\n            this.widths_[font] = cachedWidths;\n          }\n\n          const pathLength = lineStringLength(pixelCoordinates, begin, end, 2);\n          const textLength =\n            Math.abs(textScale[0]) *\n            measureAndCacheTextWidth(font, text, cachedWidths);\n          if (overflow || textLength <= pathLength) {\n            const textAlign = this.textStates[textKey].textAlign;\n            const startM =\n              (pathLength - textLength) * horizontalTextAlign(text, textAlign);\n            const parts = drawTextOnPath(\n              pixelCoordinates,\n              begin,\n              end,\n              2,\n              text,\n              startM,\n              maxAngle,\n              Math.abs(textScale[0]),\n              measureAndCacheTextWidth,\n              font,\n              cachedWidths,\n              viewRotationFromTransform ? 0 : this.viewRotation_\n            );\n            drawChars: if (parts) {\n              /** @type {Array<ReplayImageOrLabelArgs>} */\n              const replayImageOrLabelArgs = [];\n              let c, cc, chars, label, part;\n              if (strokeKey) {\n                for (c = 0, cc = parts.length; c < cc; ++c) {\n                  part = parts[c]; // x, y, anchorX, rotation, chunk\n                  chars = /** @type {string} */ (part[4]);\n                  label = this.createLabel(chars, textKey, '', strokeKey);\n                  anchorX =\n                    /** @type {number} */ (part[2]) +\n                    (textScale[0] < 0 ? -strokeWidth : strokeWidth);\n                  anchorY =\n                    baseline * label.height +\n                    ((0.5 - baseline) * 2 * strokeWidth * textScale[1]) /\n                      textScale[0] -\n                    offsetY;\n                  const dimensions = this.calculateImageOrLabelDimensions_(\n                    label.width,\n                    label.height,\n                    part[0],\n                    part[1],\n                    label.width,\n                    label.height,\n                    anchorX,\n                    anchorY,\n                    0,\n                    0,\n                    part[3],\n                    pixelRatioScale,\n                    false,\n                    defaultPadding,\n                    false,\n                    feature\n                  );\n                  if (\n                    declutterTree &&\n                    declutterTree.collides(dimensions.declutterBox)\n                  ) {\n                    break drawChars;\n                  }\n                  replayImageOrLabelArgs.push([\n                    context,\n                    contextScale,\n                    label,\n                    dimensions,\n                    1,\n                    null,\n                    null,\n                  ]);\n                }\n              }\n              if (fillKey) {\n                for (c = 0, cc = parts.length; c < cc; ++c) {\n                  part = parts[c]; // x, y, anchorX, rotation, chunk\n                  chars = /** @type {string} */ (part[4]);\n                  label = this.createLabel(chars, textKey, fillKey, '');\n                  anchorX = /** @type {number} */ (part[2]);\n                  anchorY = baseline * label.height - offsetY;\n                  const dimensions = this.calculateImageOrLabelDimensions_(\n                    label.width,\n                    label.height,\n                    part[0],\n                    part[1],\n                    label.width,\n                    label.height,\n                    anchorX,\n                    anchorY,\n                    0,\n                    0,\n                    part[3],\n                    pixelRatioScale,\n                    false,\n                    defaultPadding,\n                    false,\n                    feature\n                  );\n                  if (\n                    declutterTree &&\n                    declutterTree.collides(dimensions.declutterBox)\n                  ) {\n                    break drawChars;\n                  }\n                  replayImageOrLabelArgs.push([\n                    context,\n                    contextScale,\n                    label,\n                    dimensions,\n                    1,\n                    null,\n                    null,\n                  ]);\n                }\n              }\n              if (declutterTree) {\n                declutterTree.load(replayImageOrLabelArgs.map(getDeclutterBox));\n              }\n              for (let i = 0, ii = replayImageOrLabelArgs.length; i < ii; ++i) {\n                this.replayImageOrLabel_.apply(this, replayImageOrLabelArgs[i]);\n              }\n            }\n          }\n          ++i;\n          break;\n        case CanvasInstruction.END_GEOMETRY:\n          if (featureCallback !== undefined) {\n            feature = /** @type {import(\"../../Feature.js\").FeatureLike} */ (\n              instruction[1]\n            );\n            const result = featureCallback(feature, currentGeometry);\n            if (result) {\n              return result;\n            }\n          }\n          ++i;\n          break;\n        case CanvasInstruction.FILL:\n          if (batchSize) {\n            pendingFill++;\n          } else {\n            this.fill_(context);\n          }\n          ++i;\n          break;\n        case CanvasInstruction.MOVE_TO_LINE_TO:\n          d = /** @type {number} */ (instruction[1]);\n          dd = /** @type {number} */ (instruction[2]);\n          x = pixelCoordinates[d];\n          y = pixelCoordinates[d + 1];\n          roundX = (x + 0.5) | 0;\n          roundY = (y + 0.5) | 0;\n          if (roundX !== prevX || roundY !== prevY) {\n            context.moveTo(x, y);\n            prevX = roundX;\n            prevY = roundY;\n          }\n          for (d += 2; d < dd; d += 2) {\n            x = pixelCoordinates[d];\n            y = pixelCoordinates[d + 1];\n            roundX = (x + 0.5) | 0;\n            roundY = (y + 0.5) | 0;\n            if (d == dd - 2 || roundX !== prevX || roundY !== prevY) {\n              context.lineTo(x, y);\n              prevX = roundX;\n              prevY = roundY;\n            }\n          }\n          ++i;\n          break;\n        case CanvasInstruction.SET_FILL_STYLE:\n          lastFillInstruction = instruction;\n          this.alignFill_ = instruction[2];\n\n          if (pendingFill) {\n            this.fill_(context);\n            pendingFill = 0;\n            if (pendingStroke) {\n              context.stroke();\n              pendingStroke = 0;\n            }\n          }\n\n          context.fillStyle =\n            /** @type {import(\"../../colorlike.js\").ColorLike} */ (\n              instruction[1]\n            );\n          ++i;\n          break;\n        case CanvasInstruction.SET_STROKE_STYLE:\n          lastStrokeInstruction = instruction;\n          if (pendingStroke) {\n            context.stroke();\n            pendingStroke = 0;\n          }\n          this.setStrokeStyle_(context, /** @type {Array<*>} */ (instruction));\n          ++i;\n          break;\n        case CanvasInstruction.STROKE:\n          if (batchSize) {\n            pendingStroke++;\n          } else {\n            context.stroke();\n          }\n          ++i;\n          break;\n        default: // consume the instruction anyway, to avoid an infinite loop\n          ++i;\n          break;\n      }\n    }\n    if (pendingFill) {\n      this.fill_(context);\n    }\n    if (pendingStroke) {\n      context.stroke();\n    }\n    return undefined;\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {number} contextScale Scale of the context.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @param {number} viewRotation View rotation.\n   * @param {boolean} snapToPixel Snap point symbols and text to integer pixels.\n   * @param {import(\"rbush\").default} [declutterTree] Declutter tree.\n   */\n  execute(\n    context,\n    contextScale,\n    transform,\n    viewRotation,\n    snapToPixel,\n    declutterTree\n  ) {\n    this.viewRotation_ = viewRotation;\n    this.execute_(\n      context,\n      contextScale,\n      transform,\n      this.instructions,\n      snapToPixel,\n      undefined,\n      undefined,\n      declutterTree\n    );\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @param {number} viewRotation View rotation.\n   * @param {FeatureCallback<T>} [featureCallback] Feature callback.\n   * @param {import(\"../../extent.js\").Extent} [hitExtent] Only check\n   *     features that intersect this extent.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  executeHitDetection(\n    context,\n    transform,\n    viewRotation,\n    featureCallback,\n    hitExtent\n  ) {\n    this.viewRotation_ = viewRotation;\n    return this.execute_(\n      context,\n      1,\n      transform,\n      this.hitDetectionInstructions,\n      true,\n      featureCallback,\n      hitExtent\n    );\n  }\n}\n\nexport default Executor;\n", "/**\n * @module ol/render/canvas/ExecutorGroup\n */\n\nimport Executor from './Executor.js';\nimport {ascending} from '../../array.js';\nimport {buffer, createEmpty, extendCoordinate} from '../../extent.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../../transform.js';\nimport {createCanvasContext2D} from '../../dom.js';\nimport {isEmpty} from '../../obj.js';\nimport {transform2D} from '../../geom/flat/transform.js';\n\n/**\n * @const\n * @type {Array<import(\"../canvas.js\").BuilderType>}\n */\nconst ORDER = ['Polygon', 'Circle', 'LineString', 'Image', 'Text', 'Default'];\n\nclass ExecutorGroup {\n  /**\n   * @param {import(\"../../extent.js\").Extent} maxExtent Max extent for clipping. When a\n   * `maxExtent` was set on the Builder for this executor group, the same `maxExtent`\n   * should be set here, unless the target context does not exceed that extent (which\n   * can be the case when rendering to tiles).\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {boolean} overlaps The executor group can have overlapping geometries.\n   * @param {!Object<string, !Object<import(\"../canvas.js\").BuilderType, import(\"../canvas.js\").SerializableInstructions>>} allInstructions\n   * The serializable instructions.\n   * @param {number} [renderBuffer] Optional rendering buffer.\n   */\n  constructor(\n    maxExtent,\n    resolution,\n    pixelRatio,\n    overlaps,\n    allInstructions,\n    renderBuffer\n  ) {\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.maxExtent_ = maxExtent;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overlaps_ = overlaps;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = pixelRatio;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.resolution_ = resolution;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.renderBuffer_ = renderBuffer;\n\n    /**\n     * @private\n     * @type {!Object<string, !Object<import(\"../canvas.js\").BuilderType, import(\"./Executor\").default>>}\n     */\n    this.executorsByZIndex_ = {};\n\n    /**\n     * @private\n     * @type {CanvasRenderingContext2D}\n     */\n    this.hitDetectionContext_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.hitDetectionTransform_ = createTransform();\n\n    this.createExecutors_(allInstructions);\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   */\n  clip(context, transform) {\n    const flatClipCoords = this.getClipCoords(transform);\n    context.beginPath();\n    context.moveTo(flatClipCoords[0], flatClipCoords[1]);\n    context.lineTo(flatClipCoords[2], flatClipCoords[3]);\n    context.lineTo(flatClipCoords[4], flatClipCoords[5]);\n    context.lineTo(flatClipCoords[6], flatClipCoords[7]);\n    context.clip();\n  }\n\n  /**\n   * Create executors and populate them using the provided instructions.\n   * @private\n   * @param {!Object<string, !Object<import(\"../canvas.js\").BuilderType, import(\"../canvas.js\").SerializableInstructions>>} allInstructions The serializable instructions\n   */\n  createExecutors_(allInstructions) {\n    for (const zIndex in allInstructions) {\n      let executors = this.executorsByZIndex_[zIndex];\n      if (executors === undefined) {\n        executors = {};\n        this.executorsByZIndex_[zIndex] = executors;\n      }\n      const instructionByZindex = allInstructions[zIndex];\n      for (const builderType in instructionByZindex) {\n        const instructions = instructionByZindex[builderType];\n        executors[builderType] = new Executor(\n          this.resolution_,\n          this.pixelRatio_,\n          this.overlaps_,\n          instructions\n        );\n      }\n    }\n  }\n\n  /**\n   * @param {Array<import(\"../canvas.js\").BuilderType>} executors Executors.\n   * @return {boolean} Has executors of the provided types.\n   */\n  hasExecutors(executors) {\n    for (const zIndex in this.executorsByZIndex_) {\n      const candidates = this.executorsByZIndex_[zIndex];\n      for (let i = 0, ii = executors.length; i < ii; ++i) {\n        if (executors[i] in candidates) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {number} resolution Resolution.\n   * @param {number} rotation Rotation.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {function(import(\"../../Feature.js\").FeatureLike, import(\"../../geom/SimpleGeometry.js\").default, number): T} callback Feature callback.\n   * @param {Array<import(\"../../Feature.js\").FeatureLike>} declutteredFeatures Decluttered features.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  forEachFeatureAtCoordinate(\n    coordinate,\n    resolution,\n    rotation,\n    hitTolerance,\n    callback,\n    declutteredFeatures\n  ) {\n    hitTolerance = Math.round(hitTolerance);\n    const contextSize = hitTolerance * 2 + 1;\n    const transform = composeTransform(\n      this.hitDetectionTransform_,\n      hitTolerance + 0.5,\n      hitTolerance + 0.5,\n      1 / resolution,\n      -1 / resolution,\n      -rotation,\n      -coordinate[0],\n      -coordinate[1]\n    );\n\n    const newContext = !this.hitDetectionContext_;\n    if (newContext) {\n      this.hitDetectionContext_ = createCanvasContext2D(\n        contextSize,\n        contextSize,\n        undefined,\n        {willReadFrequently: true}\n      );\n    }\n    const context = this.hitDetectionContext_;\n\n    if (\n      context.canvas.width !== contextSize ||\n      context.canvas.height !== contextSize\n    ) {\n      context.canvas.width = contextSize;\n      context.canvas.height = contextSize;\n    } else if (!newContext) {\n      context.clearRect(0, 0, contextSize, contextSize);\n    }\n\n    /**\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    let hitExtent;\n    if (this.renderBuffer_ !== undefined) {\n      hitExtent = createEmpty();\n      extendCoordinate(hitExtent, coordinate);\n      buffer(\n        hitExtent,\n        resolution * (this.renderBuffer_ + hitTolerance),\n        hitExtent\n      );\n    }\n\n    const indexes = getPixelIndexArray(hitTolerance);\n\n    let builderType;\n\n    /**\n     * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n     * @param {import(\"../../geom/SimpleGeometry.js\").default} geometry Geometry.\n     * @return {T|undefined} Callback result.\n     */\n    function featureCallback(feature, geometry) {\n      const imageData = context.getImageData(\n        0,\n        0,\n        contextSize,\n        contextSize\n      ).data;\n      for (let i = 0, ii = indexes.length; i < ii; i++) {\n        if (imageData[indexes[i]] > 0) {\n          if (\n            !declutteredFeatures ||\n            (builderType !== 'Image' && builderType !== 'Text') ||\n            declutteredFeatures.includes(feature)\n          ) {\n            const idx = (indexes[i] - 3) / 4;\n            const x = hitTolerance - (idx % contextSize);\n            const y = hitTolerance - ((idx / contextSize) | 0);\n            const result = callback(feature, geometry, x * x + y * y);\n            if (result) {\n              return result;\n            }\n          }\n          context.clearRect(0, 0, contextSize, contextSize);\n          break;\n        }\n      }\n      return undefined;\n    }\n\n    /** @type {Array<number>} */\n    const zs = Object.keys(this.executorsByZIndex_).map(Number);\n    zs.sort(ascending);\n\n    let i, j, executors, executor, result;\n    for (i = zs.length - 1; i >= 0; --i) {\n      const zIndexKey = zs[i].toString();\n      executors = this.executorsByZIndex_[zIndexKey];\n      for (j = ORDER.length - 1; j >= 0; --j) {\n        builderType = ORDER[j];\n        executor = executors[builderType];\n        if (executor !== undefined) {\n          result = executor.executeHitDetection(\n            context,\n            transform,\n            rotation,\n            featureCallback,\n            hitExtent\n          );\n          if (result) {\n            return result;\n          }\n        }\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @return {Array<number>|null} Clip coordinates.\n   */\n  getClipCoords(transform) {\n    const maxExtent = this.maxExtent_;\n    if (!maxExtent) {\n      return null;\n    }\n    const minX = maxExtent[0];\n    const minY = maxExtent[1];\n    const maxX = maxExtent[2];\n    const maxY = maxExtent[3];\n    const flatClipCoords = [minX, minY, minX, maxY, maxX, maxY, maxX, minY];\n    transform2D(flatClipCoords, 0, 8, 2, transform, flatClipCoords);\n    return flatClipCoords;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return isEmpty(this.executorsByZIndex_);\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {number} contextScale Scale of the context.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @param {number} viewRotation View rotation.\n   * @param {boolean} snapToPixel Snap point symbols and test to integer pixel.\n   * @param {Array<import(\"../canvas.js\").BuilderType>} [builderTypes] Ordered replay types to replay.\n   *     Default is {@link module:ol/render/replay~ORDER}\n   * @param {import(\"rbush\").default} [declutterTree] Declutter tree.\n   */\n  execute(\n    context,\n    contextScale,\n    transform,\n    viewRotation,\n    snapToPixel,\n    builderTypes,\n    declutterTree\n  ) {\n    /** @type {Array<number>} */\n    const zs = Object.keys(this.executorsByZIndex_).map(Number);\n    zs.sort(ascending);\n\n    // setup clipping so that the parts of over-simplified geometries are not\n    // visible outside the current extent when panning\n    if (this.maxExtent_) {\n      context.save();\n      this.clip(context, transform);\n    }\n\n    builderTypes = builderTypes ? builderTypes : ORDER;\n    let i, ii, j, jj, replays, replay;\n    if (declutterTree) {\n      zs.reverse();\n    }\n    for (i = 0, ii = zs.length; i < ii; ++i) {\n      const zIndexKey = zs[i].toString();\n      replays = this.executorsByZIndex_[zIndexKey];\n      for (j = 0, jj = builderTypes.length; j < jj; ++j) {\n        const builderType = builderTypes[j];\n        replay = replays[builderType];\n        if (replay !== undefined) {\n          replay.execute(\n            context,\n            contextScale,\n            transform,\n            viewRotation,\n            snapToPixel,\n            declutterTree\n          );\n        }\n      }\n    }\n\n    if (this.maxExtent_) {\n      context.restore();\n    }\n  }\n}\n\n/**\n * This cache is used to store arrays of indexes for calculated pixel circles\n * to increase performance.\n * It is a static property to allow each Replaygroup to access it.\n * @type {Object<number, Array<number>>}\n */\nconst circlePixelIndexArrayCache = {};\n\n/**\n * This methods creates an array with indexes of all pixels within a circle,\n * ordered by how close they are to the center.\n * A cache is used to increase performance.\n * @param {number} radius Radius.\n * @return {Array<number>} An array with indexes within a circle.\n */\nexport function getPixelIndexArray(radius) {\n  if (circlePixelIndexArrayCache[radius] !== undefined) {\n    return circlePixelIndexArrayCache[radius];\n  }\n\n  const size = radius * 2 + 1;\n  const maxDistanceSq = radius * radius;\n  const distances = new Array(maxDistanceSq + 1);\n  for (let i = 0; i <= radius; ++i) {\n    for (let j = 0; j <= radius; ++j) {\n      const distanceSq = i * i + j * j;\n      if (distanceSq > maxDistanceSq) {\n        break;\n      }\n      let distance = distances[distanceSq];\n      if (!distance) {\n        distance = [];\n        distances[distanceSq] = distance;\n      }\n      distance.push(((radius + i) * size + (radius + j)) * 4 + 3);\n      if (i > 0) {\n        distance.push(((radius - i) * size + (radius + j)) * 4 + 3);\n      }\n      if (j > 0) {\n        distance.push(((radius + i) * size + (radius - j)) * 4 + 3);\n        if (i > 0) {\n          distance.push(((radius - i) * size + (radius - j)) * 4 + 3);\n        }\n      }\n    }\n  }\n\n  const pixelIndex = [];\n  for (let i = 0, ii = distances.length; i < ii; ++i) {\n    if (distances[i]) {\n      pixelIndex.push(...distances[i]);\n    }\n  }\n\n  circlePixelIndexArrayCache[radius] = pixelIndex;\n  return pixelIndex;\n}\n\nexport default ExecutorGroup;\n", "/**\n * @module ol/render/canvas/Immediate\n */\n// FIXME test, especially polygons with holes and multipolygons\n// FIXME need to handle large thick features (where pixel size matters)\n// FIXME add offset and end to ol/geom/flat/transform~transform2D?\n\nimport VectorContext from '../VectorContext.js';\nimport {asColorLike} from '../../colorlike.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../../transform.js';\nimport {\n  defaultFillStyle,\n  defaultFont,\n  defaultLineCap,\n  defaultLineDash,\n  defaultLineDashOffset,\n  defaultLineJoin,\n  defaultLineWidth,\n  defaultMiterLimit,\n  defaultStrokeStyle,\n  defaultTextAlign,\n  defaultTextBaseline,\n} from '../canvas.js';\nimport {equals} from '../../array.js';\nimport {intersects} from '../../extent.js';\nimport {toFixed} from '../../math.js';\nimport {transform2D} from '../../geom/flat/transform.js';\nimport {transformGeom2D} from '../../geom/SimpleGeometry.js';\n\n/**\n * @classdesc\n * A concrete subclass of {@link module:ol/render/VectorContext~VectorContext} that implements\n * direct rendering of features and geometries to an HTML5 Canvas context.\n * Instances of this class are created internally by the library and\n * provided to application code as vectorContext member of the\n * {@link module:ol/render/Event~RenderEvent} object associated with postcompose, precompose and\n * render events emitted by layers and maps.\n */\nclass CanvasImmediateRenderer extends VectorContext {\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../../extent.js\").Extent} extent Extent.\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   * @param {number} viewRotation View rotation.\n   * @param {number} [squaredTolerance] Optional squared tolerance for simplification.\n   * @param {import(\"../../proj.js\").TransformFunction} [userTransform] Transform from user to view projection.\n   */\n  constructor(\n    context,\n    pixelRatio,\n    extent,\n    transform,\n    viewRotation,\n    squaredTolerance,\n    userTransform\n  ) {\n    super();\n\n    /**\n     * @private\n     * @type {CanvasRenderingContext2D}\n     */\n    this.context_ = context;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = pixelRatio;\n\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.extent_ = extent;\n\n    /**\n     * @private\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.transform_ = transform;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.transformRotation_ = transform\n      ? toFixed(Math.atan2(transform[1], transform[0]), 10)\n      : 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.viewRotation_ = viewRotation;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.squaredTolerance_ = squaredTolerance;\n\n    /**\n     * @private\n     * @type {import(\"../../proj.js\").TransformFunction}\n     */\n    this.userTransform_ = userTransform;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").FillState}\n     */\n    this.contextFillState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").StrokeState}\n     */\n    this.contextStrokeState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").TextState}\n     */\n    this.contextTextState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").FillState}\n     */\n    this.fillState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").StrokeState}\n     */\n    this.strokeState_ = null;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement}\n     */\n    this.image_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageAnchorX_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageAnchorY_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageHeight_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageOpacity_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageOriginX_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageOriginY_ = 0;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.imageRotateWithView_ = false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageRotation_ = 0;\n\n    /**\n     * @private\n     * @type {import(\"../../size.js\").Size}\n     */\n    this.imageScale_ = [0, 0];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.imageWidth_ = 0;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.text_ = '';\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textOffsetX_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textOffsetY_ = 0;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.textRotateWithView_ = false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.textRotation_ = 0;\n\n    /**\n     * @private\n     * @type {import(\"../../size.js\").Size}\n     */\n    this.textScale_ = [0, 0];\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").FillState}\n     */\n    this.textFillState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").StrokeState}\n     */\n    this.textStrokeState_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../canvas.js\").TextState}\n     */\n    this.textState_ = null;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.pixelCoordinates_ = [];\n\n    /**\n     * @private\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.tmpLocalTransform_ = createTransform();\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {number} end End.\n   * @param {number} stride Stride.\n   * @private\n   */\n  drawImages_(flatCoordinates, offset, end, stride) {\n    if (!this.image_) {\n      return;\n    }\n    const pixelCoordinates = transform2D(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      this.transform_,\n      this.pixelCoordinates_\n    );\n    const context = this.context_;\n    const localTransform = this.tmpLocalTransform_;\n    const alpha = context.globalAlpha;\n    if (this.imageOpacity_ != 1) {\n      context.globalAlpha = alpha * this.imageOpacity_;\n    }\n    let rotation = this.imageRotation_;\n    if (this.transformRotation_ === 0) {\n      rotation -= this.viewRotation_;\n    }\n    if (this.imageRotateWithView_) {\n      rotation += this.viewRotation_;\n    }\n    for (let i = 0, ii = pixelCoordinates.length; i < ii; i += 2) {\n      const x = pixelCoordinates[i] - this.imageAnchorX_;\n      const y = pixelCoordinates[i + 1] - this.imageAnchorY_;\n      if (\n        rotation !== 0 ||\n        this.imageScale_[0] != 1 ||\n        this.imageScale_[1] != 1\n      ) {\n        const centerX = x + this.imageAnchorX_;\n        const centerY = y + this.imageAnchorY_;\n        composeTransform(\n          localTransform,\n          centerX,\n          centerY,\n          1,\n          1,\n          rotation,\n          -centerX,\n          -centerY\n        );\n        context.setTransform.apply(context, localTransform);\n        context.translate(centerX, centerY);\n        context.scale(this.imageScale_[0], this.imageScale_[1]);\n        context.drawImage(\n          this.image_,\n          this.imageOriginX_,\n          this.imageOriginY_,\n          this.imageWidth_,\n          this.imageHeight_,\n          -this.imageAnchorX_,\n          -this.imageAnchorY_,\n          this.imageWidth_,\n          this.imageHeight_\n        );\n        context.setTransform(1, 0, 0, 1, 0, 0);\n      } else {\n        context.drawImage(\n          this.image_,\n          this.imageOriginX_,\n          this.imageOriginY_,\n          this.imageWidth_,\n          this.imageHeight_,\n          x,\n          y,\n          this.imageWidth_,\n          this.imageHeight_\n        );\n      }\n    }\n    if (this.imageOpacity_ != 1) {\n      context.globalAlpha = alpha;\n    }\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {number} end End.\n   * @param {number} stride Stride.\n   * @private\n   */\n  drawText_(flatCoordinates, offset, end, stride) {\n    if (!this.textState_ || this.text_ === '') {\n      return;\n    }\n    if (this.textFillState_) {\n      this.setContextFillState_(this.textFillState_);\n    }\n    if (this.textStrokeState_) {\n      this.setContextStrokeState_(this.textStrokeState_);\n    }\n    this.setContextTextState_(this.textState_);\n    const pixelCoordinates = transform2D(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      this.transform_,\n      this.pixelCoordinates_\n    );\n    const context = this.context_;\n    let rotation = this.textRotation_;\n    if (this.transformRotation_ === 0) {\n      rotation -= this.viewRotation_;\n    }\n    if (this.textRotateWithView_) {\n      rotation += this.viewRotation_;\n    }\n    for (; offset < end; offset += stride) {\n      const x = pixelCoordinates[offset] + this.textOffsetX_;\n      const y = pixelCoordinates[offset + 1] + this.textOffsetY_;\n      if (\n        rotation !== 0 ||\n        this.textScale_[0] != 1 ||\n        this.textScale_[1] != 1\n      ) {\n        context.translate(x - this.textOffsetX_, y - this.textOffsetY_);\n        context.rotate(rotation);\n        context.translate(this.textOffsetX_, this.textOffsetY_);\n        context.scale(this.textScale_[0], this.textScale_[1]);\n        if (this.textStrokeState_) {\n          context.strokeText(this.text_, 0, 0);\n        }\n        if (this.textFillState_) {\n          context.fillText(this.text_, 0, 0);\n        }\n        context.setTransform(1, 0, 0, 1, 0, 0);\n      } else {\n        if (this.textStrokeState_) {\n          context.strokeText(this.text_, x, y);\n        }\n        if (this.textFillState_) {\n          context.fillText(this.text_, x, y);\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {number} end End.\n   * @param {number} stride Stride.\n   * @param {boolean} close Close.\n   * @private\n   * @return {number} end End.\n   */\n  moveToLineTo_(flatCoordinates, offset, end, stride, close) {\n    const context = this.context_;\n    const pixelCoordinates = transform2D(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      this.transform_,\n      this.pixelCoordinates_\n    );\n    context.moveTo(pixelCoordinates[0], pixelCoordinates[1]);\n    let length = pixelCoordinates.length;\n    if (close) {\n      length -= 2;\n    }\n    for (let i = 2; i < length; i += 2) {\n      context.lineTo(pixelCoordinates[i], pixelCoordinates[i + 1]);\n    }\n    if (close) {\n      context.closePath();\n    }\n    return end;\n  }\n\n  /**\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   * @param {number} offset Offset.\n   * @param {Array<number>} ends Ends.\n   * @param {number} stride Stride.\n   * @private\n   * @return {number} End.\n   */\n  drawRings_(flatCoordinates, offset, ends, stride) {\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      offset = this.moveToLineTo_(\n        flatCoordinates,\n        offset,\n        ends[i],\n        stride,\n        true\n      );\n    }\n    return offset;\n  }\n\n  /**\n   * Render a circle geometry into the canvas.  Rendering is immediate and uses\n   * the current fill and stroke styles.\n   *\n   * @param {import(\"../../geom/Circle.js\").default} geometry Circle geometry.\n   * @api\n   */\n  drawCircle(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/Circle.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    if (!intersects(this.extent_, geometry.getExtent())) {\n      return;\n    }\n    if (this.fillState_ || this.strokeState_) {\n      if (this.fillState_) {\n        this.setContextFillState_(this.fillState_);\n      }\n      if (this.strokeState_) {\n        this.setContextStrokeState_(this.strokeState_);\n      }\n      const pixelCoordinates = transformGeom2D(\n        geometry,\n        this.transform_,\n        this.pixelCoordinates_\n      );\n      const dx = pixelCoordinates[2] - pixelCoordinates[0];\n      const dy = pixelCoordinates[3] - pixelCoordinates[1];\n      const radius = Math.sqrt(dx * dx + dy * dy);\n      const context = this.context_;\n      context.beginPath();\n      context.arc(\n        pixelCoordinates[0],\n        pixelCoordinates[1],\n        radius,\n        0,\n        2 * Math.PI\n      );\n      if (this.fillState_) {\n        context.fill();\n      }\n      if (this.strokeState_) {\n        context.stroke();\n      }\n    }\n    if (this.text_ !== '') {\n      this.drawText_(geometry.getCenter(), 0, 2, 2);\n    }\n  }\n\n  /**\n   * Set the rendering style.  Note that since this is an immediate rendering API,\n   * any `zIndex` on the provided style will be ignored.\n   *\n   * @param {import(\"../../style/Style.js\").default} style The rendering style.\n   * @api\n   */\n  setStyle(style) {\n    this.setFillStrokeStyle(style.getFill(), style.getStroke());\n    this.setImageStyle(style.getImage());\n    this.setTextStyle(style.getText());\n  }\n\n  /**\n   * @param {import(\"../../transform.js\").Transform} transform Transform.\n   */\n  setTransform(transform) {\n    this.transform_ = transform;\n  }\n\n  /**\n   * Render a geometry into the canvas.  Call\n   * {@link module:ol/render/canvas/Immediate~CanvasImmediateRenderer#setStyle renderer.setStyle()} first to set the rendering style.\n   *\n   * @param {import(\"../../geom/Geometry.js\").default|import(\"../Feature.js\").default} geometry The geometry to render.\n   * @api\n   */\n  drawGeometry(geometry) {\n    const type = geometry.getType();\n    switch (type) {\n      case 'Point':\n        this.drawPoint(\n          /** @type {import(\"../../geom/Point.js\").default} */ (geometry)\n        );\n        break;\n      case 'LineString':\n        this.drawLineString(\n          /** @type {import(\"../../geom/LineString.js\").default} */ (geometry)\n        );\n        break;\n      case 'Polygon':\n        this.drawPolygon(\n          /** @type {import(\"../../geom/Polygon.js\").default} */ (geometry)\n        );\n        break;\n      case 'MultiPoint':\n        this.drawMultiPoint(\n          /** @type {import(\"../../geom/MultiPoint.js\").default} */ (geometry)\n        );\n        break;\n      case 'MultiLineString':\n        this.drawMultiLineString(\n          /** @type {import(\"../../geom/MultiLineString.js\").default} */ (\n            geometry\n          )\n        );\n        break;\n      case 'MultiPolygon':\n        this.drawMultiPolygon(\n          /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (geometry)\n        );\n        break;\n      case 'GeometryCollection':\n        this.drawGeometryCollection(\n          /** @type {import(\"../../geom/GeometryCollection.js\").default} */ (\n            geometry\n          )\n        );\n        break;\n      case 'Circle':\n        this.drawCircle(\n          /** @type {import(\"../../geom/Circle.js\").default} */ (geometry)\n        );\n        break;\n      default:\n    }\n  }\n\n  /**\n   * Render a feature into the canvas.  Note that any `zIndex` on the provided\n   * style will be ignored - features are rendered immediately in the order that\n   * this method is called.  If you need `zIndex` support, you should be using an\n   * {@link module:ol/layer/Vector~VectorLayer} instead.\n   *\n   * @param {import(\"../../Feature.js\").default} feature Feature.\n   * @param {import(\"../../style/Style.js\").default} style Style.\n   * @api\n   */\n  drawFeature(feature, style) {\n    const geometry = style.getGeometryFunction()(feature);\n    if (!geometry) {\n      return;\n    }\n    this.setStyle(style);\n    this.drawGeometry(geometry);\n  }\n\n  /**\n   * Render a GeometryCollection to the canvas.  Rendering is immediate and\n   * uses the current styles appropriate for each geometry in the collection.\n   *\n   * @param {import(\"../../geom/GeometryCollection.js\").default} geometry Geometry collection.\n   */\n  drawGeometryCollection(geometry) {\n    const geometries = geometry.getGeometriesArray();\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      this.drawGeometry(geometries[i]);\n    }\n  }\n\n  /**\n   * Render a Point geometry into the canvas.  Rendering is immediate and uses\n   * the current style.\n   *\n   * @param {import(\"../../geom/Point.js\").default|import(\"../Feature.js\").default} geometry Point geometry.\n   */\n  drawPoint(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/Point.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    const flatCoordinates = geometry.getFlatCoordinates();\n    const stride = geometry.getStride();\n    if (this.image_) {\n      this.drawImages_(flatCoordinates, 0, flatCoordinates.length, stride);\n    }\n    if (this.text_ !== '') {\n      this.drawText_(flatCoordinates, 0, flatCoordinates.length, stride);\n    }\n  }\n\n  /**\n   * Render a MultiPoint geometry  into the canvas.  Rendering is immediate and\n   * uses the current style.\n   *\n   * @param {import(\"../../geom/MultiPoint.js\").default|import(\"../Feature.js\").default} geometry MultiPoint geometry.\n   */\n  drawMultiPoint(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/MultiPoint.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    const flatCoordinates = geometry.getFlatCoordinates();\n    const stride = geometry.getStride();\n    if (this.image_) {\n      this.drawImages_(flatCoordinates, 0, flatCoordinates.length, stride);\n    }\n    if (this.text_ !== '') {\n      this.drawText_(flatCoordinates, 0, flatCoordinates.length, stride);\n    }\n  }\n\n  /**\n   * Render a LineString into the canvas.  Rendering is immediate and uses\n   * the current style.\n   *\n   * @param {import(\"../../geom/LineString.js\").default|import(\"../Feature.js\").default} geometry LineString geometry.\n   */\n  drawLineString(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/LineString.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    if (!intersects(this.extent_, geometry.getExtent())) {\n      return;\n    }\n    if (this.strokeState_) {\n      this.setContextStrokeState_(this.strokeState_);\n      const context = this.context_;\n      const flatCoordinates = geometry.getFlatCoordinates();\n      context.beginPath();\n      this.moveToLineTo_(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        geometry.getStride(),\n        false\n      );\n      context.stroke();\n    }\n    if (this.text_ !== '') {\n      const flatMidpoint = geometry.getFlatMidpoint();\n      this.drawText_(flatMidpoint, 0, 2, 2);\n    }\n  }\n\n  /**\n   * Render a MultiLineString geometry into the canvas.  Rendering is immediate\n   * and uses the current style.\n   *\n   * @param {import(\"../../geom/MultiLineString.js\").default|import(\"../Feature.js\").default} geometry MultiLineString geometry.\n   */\n  drawMultiLineString(geometry) {\n    if (this.squaredTolerance_) {\n      geometry =\n        /** @type {import(\"../../geom/MultiLineString.js\").default} */ (\n          geometry.simplifyTransformed(\n            this.squaredTolerance_,\n            this.userTransform_\n          )\n        );\n    }\n    const geometryExtent = geometry.getExtent();\n    if (!intersects(this.extent_, geometryExtent)) {\n      return;\n    }\n    if (this.strokeState_) {\n      this.setContextStrokeState_(this.strokeState_);\n      const context = this.context_;\n      const flatCoordinates = geometry.getFlatCoordinates();\n      let offset = 0;\n      const ends = /** @type {Array<number>} */ (geometry.getEnds());\n      const stride = geometry.getStride();\n      context.beginPath();\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        offset = this.moveToLineTo_(\n          flatCoordinates,\n          offset,\n          ends[i],\n          stride,\n          false\n        );\n      }\n      context.stroke();\n    }\n    if (this.text_ !== '') {\n      const flatMidpoints = geometry.getFlatMidpoints();\n      this.drawText_(flatMidpoints, 0, flatMidpoints.length, 2);\n    }\n  }\n\n  /**\n   * Render a Polygon geometry into the canvas.  Rendering is immediate and uses\n   * the current style.\n   *\n   * @param {import(\"../../geom/Polygon.js\").default|import(\"../Feature.js\").default} geometry Polygon geometry.\n   */\n  drawPolygon(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/Polygon.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    if (!intersects(this.extent_, geometry.getExtent())) {\n      return;\n    }\n    if (this.strokeState_ || this.fillState_) {\n      if (this.fillState_) {\n        this.setContextFillState_(this.fillState_);\n      }\n      if (this.strokeState_) {\n        this.setContextStrokeState_(this.strokeState_);\n      }\n      const context = this.context_;\n      context.beginPath();\n      this.drawRings_(\n        geometry.getOrientedFlatCoordinates(),\n        0,\n        /** @type {Array<number>} */ (geometry.getEnds()),\n        geometry.getStride()\n      );\n      if (this.fillState_) {\n        context.fill();\n      }\n      if (this.strokeState_) {\n        context.stroke();\n      }\n    }\n    if (this.text_ !== '') {\n      const flatInteriorPoint = geometry.getFlatInteriorPoint();\n      this.drawText_(flatInteriorPoint, 0, 2, 2);\n    }\n  }\n\n  /**\n   * Render MultiPolygon geometry into the canvas.  Rendering is immediate and\n   * uses the current style.\n   * @param {import(\"../../geom/MultiPolygon.js\").default} geometry MultiPolygon geometry.\n   */\n  drawMultiPolygon(geometry) {\n    if (this.squaredTolerance_) {\n      geometry = /** @type {import(\"../../geom/MultiPolygon.js\").default} */ (\n        geometry.simplifyTransformed(\n          this.squaredTolerance_,\n          this.userTransform_\n        )\n      );\n    }\n    if (!intersects(this.extent_, geometry.getExtent())) {\n      return;\n    }\n    if (this.strokeState_ || this.fillState_) {\n      if (this.fillState_) {\n        this.setContextFillState_(this.fillState_);\n      }\n      if (this.strokeState_) {\n        this.setContextStrokeState_(this.strokeState_);\n      }\n      const context = this.context_;\n      const flatCoordinates = geometry.getOrientedFlatCoordinates();\n      let offset = 0;\n      const endss = geometry.getEndss();\n      const stride = geometry.getStride();\n      context.beginPath();\n      for (let i = 0, ii = endss.length; i < ii; ++i) {\n        const ends = endss[i];\n        offset = this.drawRings_(flatCoordinates, offset, ends, stride);\n      }\n      if (this.fillState_) {\n        context.fill();\n      }\n      if (this.strokeState_) {\n        context.stroke();\n      }\n    }\n    if (this.text_ !== '') {\n      const flatInteriorPoints = geometry.getFlatInteriorPoints();\n      this.drawText_(flatInteriorPoints, 0, flatInteriorPoints.length, 2);\n    }\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").FillState} fillState Fill state.\n   * @private\n   */\n  setContextFillState_(fillState) {\n    const context = this.context_;\n    const contextFillState = this.contextFillState_;\n    if (!contextFillState) {\n      context.fillStyle = fillState.fillStyle;\n      this.contextFillState_ = {\n        fillStyle: fillState.fillStyle,\n      };\n    } else {\n      if (contextFillState.fillStyle != fillState.fillStyle) {\n        contextFillState.fillStyle = fillState.fillStyle;\n        context.fillStyle = fillState.fillStyle;\n      }\n    }\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").StrokeState} strokeState Stroke state.\n   * @private\n   */\n  setContextStrokeState_(strokeState) {\n    const context = this.context_;\n    const contextStrokeState = this.contextStrokeState_;\n    if (!contextStrokeState) {\n      context.lineCap = strokeState.lineCap;\n      context.setLineDash(strokeState.lineDash);\n      context.lineDashOffset = strokeState.lineDashOffset;\n      context.lineJoin = strokeState.lineJoin;\n      context.lineWidth = strokeState.lineWidth;\n      context.miterLimit = strokeState.miterLimit;\n      context.strokeStyle = strokeState.strokeStyle;\n      this.contextStrokeState_ = {\n        lineCap: strokeState.lineCap,\n        lineDash: strokeState.lineDash,\n        lineDashOffset: strokeState.lineDashOffset,\n        lineJoin: strokeState.lineJoin,\n        lineWidth: strokeState.lineWidth,\n        miterLimit: strokeState.miterLimit,\n        strokeStyle: strokeState.strokeStyle,\n      };\n    } else {\n      if (contextStrokeState.lineCap != strokeState.lineCap) {\n        contextStrokeState.lineCap = strokeState.lineCap;\n        context.lineCap = strokeState.lineCap;\n      }\n      if (!equals(contextStrokeState.lineDash, strokeState.lineDash)) {\n        context.setLineDash(\n          (contextStrokeState.lineDash = strokeState.lineDash)\n        );\n      }\n      if (contextStrokeState.lineDashOffset != strokeState.lineDashOffset) {\n        contextStrokeState.lineDashOffset = strokeState.lineDashOffset;\n        context.lineDashOffset = strokeState.lineDashOffset;\n      }\n      if (contextStrokeState.lineJoin != strokeState.lineJoin) {\n        contextStrokeState.lineJoin = strokeState.lineJoin;\n        context.lineJoin = strokeState.lineJoin;\n      }\n      if (contextStrokeState.lineWidth != strokeState.lineWidth) {\n        contextStrokeState.lineWidth = strokeState.lineWidth;\n        context.lineWidth = strokeState.lineWidth;\n      }\n      if (contextStrokeState.miterLimit != strokeState.miterLimit) {\n        contextStrokeState.miterLimit = strokeState.miterLimit;\n        context.miterLimit = strokeState.miterLimit;\n      }\n      if (contextStrokeState.strokeStyle != strokeState.strokeStyle) {\n        contextStrokeState.strokeStyle = strokeState.strokeStyle;\n        context.strokeStyle = strokeState.strokeStyle;\n      }\n    }\n  }\n\n  /**\n   * @param {import(\"../canvas.js\").TextState} textState Text state.\n   * @private\n   */\n  setContextTextState_(textState) {\n    const context = this.context_;\n    const contextTextState = this.contextTextState_;\n    const textAlign = textState.textAlign\n      ? textState.textAlign\n      : defaultTextAlign;\n    if (!contextTextState) {\n      context.font = textState.font;\n      context.textAlign = textAlign;\n      context.textBaseline = textState.textBaseline;\n      this.contextTextState_ = {\n        font: textState.font,\n        textAlign: textAlign,\n        textBaseline: textState.textBaseline,\n      };\n    } else {\n      if (contextTextState.font != textState.font) {\n        contextTextState.font = textState.font;\n        context.font = textState.font;\n      }\n      if (contextTextState.textAlign != textAlign) {\n        contextTextState.textAlign = textAlign;\n        context.textAlign = textAlign;\n      }\n      if (contextTextState.textBaseline != textState.textBaseline) {\n        contextTextState.textBaseline = textState.textBaseline;\n        context.textBaseline = textState.textBaseline;\n      }\n    }\n  }\n\n  /**\n   * Set the fill and stroke style for subsequent draw operations.  To clear\n   * either fill or stroke styles, pass null for the appropriate parameter.\n   *\n   * @param {import(\"../../style/Fill.js\").default} fillStyle Fill style.\n   * @param {import(\"../../style/Stroke.js\").default} strokeStyle Stroke style.\n   */\n  setFillStrokeStyle(fillStyle, strokeStyle) {\n    if (!fillStyle) {\n      this.fillState_ = null;\n    } else {\n      const fillStyleColor = fillStyle.getColor();\n      this.fillState_ = {\n        fillStyle: asColorLike(\n          fillStyleColor ? fillStyleColor : defaultFillStyle\n        ),\n      };\n    }\n    if (!strokeStyle) {\n      this.strokeState_ = null;\n    } else {\n      const strokeStyleColor = strokeStyle.getColor();\n      const strokeStyleLineCap = strokeStyle.getLineCap();\n      const strokeStyleLineDash = strokeStyle.getLineDash();\n      const strokeStyleLineDashOffset = strokeStyle.getLineDashOffset();\n      const strokeStyleLineJoin = strokeStyle.getLineJoin();\n      const strokeStyleWidth = strokeStyle.getWidth();\n      const strokeStyleMiterLimit = strokeStyle.getMiterLimit();\n      const lineDash = strokeStyleLineDash\n        ? strokeStyleLineDash\n        : defaultLineDash;\n      this.strokeState_ = {\n        lineCap:\n          strokeStyleLineCap !== undefined\n            ? strokeStyleLineCap\n            : defaultLineCap,\n        lineDash:\n          this.pixelRatio_ === 1\n            ? lineDash\n            : lineDash.map((n) => n * this.pixelRatio_),\n        lineDashOffset:\n          (strokeStyleLineDashOffset\n            ? strokeStyleLineDashOffset\n            : defaultLineDashOffset) * this.pixelRatio_,\n        lineJoin:\n          strokeStyleLineJoin !== undefined\n            ? strokeStyleLineJoin\n            : defaultLineJoin,\n        lineWidth:\n          (strokeStyleWidth !== undefined\n            ? strokeStyleWidth\n            : defaultLineWidth) * this.pixelRatio_,\n        miterLimit:\n          strokeStyleMiterLimit !== undefined\n            ? strokeStyleMiterLimit\n            : defaultMiterLimit,\n        strokeStyle: asColorLike(\n          strokeStyleColor ? strokeStyleColor : defaultStrokeStyle\n        ),\n      };\n    }\n  }\n\n  /**\n   * Set the image style for subsequent draw operations.  Pass null to remove\n   * the image style.\n   *\n   * @param {import(\"../../style/Image.js\").default} imageStyle Image style.\n   */\n  setImageStyle(imageStyle) {\n    let imageSize;\n    if (!imageStyle || !(imageSize = imageStyle.getSize())) {\n      this.image_ = null;\n      return;\n    }\n    const imagePixelRatio = imageStyle.getPixelRatio(this.pixelRatio_);\n    const imageAnchor = imageStyle.getAnchor();\n    const imageOrigin = imageStyle.getOrigin();\n    this.image_ = imageStyle.getImage(this.pixelRatio_);\n    this.imageAnchorX_ = imageAnchor[0] * imagePixelRatio;\n    this.imageAnchorY_ = imageAnchor[1] * imagePixelRatio;\n    this.imageHeight_ = imageSize[1] * imagePixelRatio;\n    this.imageOpacity_ = imageStyle.getOpacity();\n    this.imageOriginX_ = imageOrigin[0];\n    this.imageOriginY_ = imageOrigin[1];\n    this.imageRotateWithView_ = imageStyle.getRotateWithView();\n    this.imageRotation_ = imageStyle.getRotation();\n    const imageScale = imageStyle.getScaleArray();\n    this.imageScale_ = [\n      (imageScale[0] * this.pixelRatio_) / imagePixelRatio,\n      (imageScale[1] * this.pixelRatio_) / imagePixelRatio,\n    ];\n    this.imageWidth_ = imageSize[0] * imagePixelRatio;\n  }\n\n  /**\n   * Set the text style for subsequent draw operations.  Pass null to\n   * remove the text style.\n   *\n   * @param {import(\"../../style/Text.js\").default} textStyle Text style.\n   */\n  setTextStyle(textStyle) {\n    if (!textStyle) {\n      this.text_ = '';\n    } else {\n      const textFillStyle = textStyle.getFill();\n      if (!textFillStyle) {\n        this.textFillState_ = null;\n      } else {\n        const textFillStyleColor = textFillStyle.getColor();\n        this.textFillState_ = {\n          fillStyle: asColorLike(\n            textFillStyleColor ? textFillStyleColor : defaultFillStyle\n          ),\n        };\n      }\n      const textStrokeStyle = textStyle.getStroke();\n      if (!textStrokeStyle) {\n        this.textStrokeState_ = null;\n      } else {\n        const textStrokeStyleColor = textStrokeStyle.getColor();\n        const textStrokeStyleLineCap = textStrokeStyle.getLineCap();\n        const textStrokeStyleLineDash = textStrokeStyle.getLineDash();\n        const textStrokeStyleLineDashOffset =\n          textStrokeStyle.getLineDashOffset();\n        const textStrokeStyleLineJoin = textStrokeStyle.getLineJoin();\n        const textStrokeStyleWidth = textStrokeStyle.getWidth();\n        const textStrokeStyleMiterLimit = textStrokeStyle.getMiterLimit();\n        this.textStrokeState_ = {\n          lineCap:\n            textStrokeStyleLineCap !== undefined\n              ? textStrokeStyleLineCap\n              : defaultLineCap,\n          lineDash: textStrokeStyleLineDash\n            ? textStrokeStyleLineDash\n            : defaultLineDash,\n          lineDashOffset: textStrokeStyleLineDashOffset\n            ? textStrokeStyleLineDashOffset\n            : defaultLineDashOffset,\n          lineJoin:\n            textStrokeStyleLineJoin !== undefined\n              ? textStrokeStyleLineJoin\n              : defaultLineJoin,\n          lineWidth:\n            textStrokeStyleWidth !== undefined\n              ? textStrokeStyleWidth\n              : defaultLineWidth,\n          miterLimit:\n            textStrokeStyleMiterLimit !== undefined\n              ? textStrokeStyleMiterLimit\n              : defaultMiterLimit,\n          strokeStyle: asColorLike(\n            textStrokeStyleColor ? textStrokeStyleColor : defaultStrokeStyle\n          ),\n        };\n      }\n      const textFont = textStyle.getFont();\n      const textOffsetX = textStyle.getOffsetX();\n      const textOffsetY = textStyle.getOffsetY();\n      const textRotateWithView = textStyle.getRotateWithView();\n      const textRotation = textStyle.getRotation();\n      const textScale = textStyle.getScaleArray();\n      const textText = textStyle.getText();\n      const textTextAlign = textStyle.getTextAlign();\n      const textTextBaseline = textStyle.getTextBaseline();\n      this.textState_ = {\n        font: textFont !== undefined ? textFont : defaultFont,\n        textAlign:\n          textTextAlign !== undefined ? textTextAlign : defaultTextAlign,\n        textBaseline:\n          textTextBaseline !== undefined\n            ? textTextBaseline\n            : defaultTextBaseline,\n      };\n      this.text_ =\n        textText !== undefined\n          ? Array.isArray(textText)\n            ? textText.reduce((acc, t, i) => (acc += i % 2 ? ' ' : t), '')\n            : textText\n          : '';\n      this.textOffsetX_ =\n        textOffsetX !== undefined ? this.pixelRatio_ * textOffsetX : 0;\n      this.textOffsetY_ =\n        textOffsetY !== undefined ? this.pixelRatio_ * textOffsetY : 0;\n      this.textRotateWithView_ =\n        textRotateWithView !== undefined ? textRotateWithView : false;\n      this.textRotation_ = textRotation !== undefined ? textRotation : 0;\n      this.textScale_ = [\n        this.pixelRatio_ * textScale[0],\n        this.pixelRatio_ * textScale[1],\n      ];\n    }\n  }\n}\n\nexport default CanvasImmediateRenderer;\n", "/**\n * @module ol/render/canvas/hitdetect\n */\n\nimport CanvasImmediateRenderer from './Immediate.js';\nimport {Icon} from '../../style.js';\nimport {ascending} from '../../array.js';\nimport {clamp} from '../../math.js';\nimport {createCanvasContext2D} from '../../dom.js';\nimport {intersects} from '../../extent.js';\n\nexport const HIT_DETECT_RESOLUTION = 0.5;\n\n/**\n * @param {import(\"../../size.js\").Size} size Canvas size in css pixels.\n * @param {Array<import(\"../../transform.js\").Transform>} transforms Transforms\n * for rendering features to all worlds of the viewport, from coordinates to css\n * pixels.\n * @param {Array<import(\"../../Feature.js\").FeatureLike>} features\n * Features to consider for hit detection.\n * @param {import(\"../../style/Style.js\").StyleFunction|undefined} styleFunction\n * Layer style function.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @return {ImageData} Hit detection image data.\n */\nexport function createHitDetectionImageData(\n  size,\n  transforms,\n  features,\n  styleFunction,\n  extent,\n  resolution,\n  rotation\n) {\n  const width = size[0] * HIT_DETECT_RESOLUTION;\n  const height = size[1] * HIT_DETECT_RESOLUTION;\n  const context = createCanvasContext2D(width, height);\n  context.imageSmoothingEnabled = false;\n  const canvas = context.canvas;\n  const renderer = new CanvasImmediateRenderer(\n    context,\n    HIT_DETECT_RESOLUTION,\n    extent,\n    null,\n    rotation\n  );\n  const featureCount = features.length;\n  // Stretch hit detection index to use the whole available color range\n  const indexFactor = Math.floor((256 * 256 * 256 - 1) / featureCount);\n  const featuresByZIndex = {};\n  for (let i = 1; i <= featureCount; ++i) {\n    const feature = features[i - 1];\n    const featureStyleFunction = feature.getStyleFunction() || styleFunction;\n    if (!featureStyleFunction) {\n      continue;\n    }\n    let styles = featureStyleFunction(feature, resolution);\n    if (!styles) {\n      continue;\n    }\n    if (!Array.isArray(styles)) {\n      styles = [styles];\n    }\n    const index = i * indexFactor;\n    const color = index.toString(16).padStart(7, '#00000');\n    for (let j = 0, jj = styles.length; j < jj; ++j) {\n      const originalStyle = styles[j];\n      const geometry = originalStyle.getGeometryFunction()(feature);\n      if (!geometry || !intersects(extent, geometry.getExtent())) {\n        continue;\n      }\n      const style = originalStyle.clone();\n      const fill = style.getFill();\n      if (fill) {\n        fill.setColor(color);\n      }\n      const stroke = style.getStroke();\n      if (stroke) {\n        stroke.setColor(color);\n        stroke.setLineDash(null);\n      }\n      style.setText(undefined);\n      const image = originalStyle.getImage();\n      if (image) {\n        const imgSize = image.getImageSize();\n        if (!imgSize) {\n          continue;\n        }\n\n        const imgContext = createCanvasContext2D(\n          imgSize[0],\n          imgSize[1],\n          undefined,\n          {alpha: false}\n        );\n        const img = imgContext.canvas;\n        imgContext.fillStyle = color;\n        imgContext.fillRect(0, 0, img.width, img.height);\n        style.setImage(\n          new Icon({\n            img: img,\n            imgSize: imgSize,\n            anchor: image.getAnchor(),\n            anchorXUnits: 'pixels',\n            anchorYUnits: 'pixels',\n            offset: image.getOrigin(),\n            opacity: 1,\n            size: image.getSize(),\n            scale: image.getScale(),\n            rotation: image.getRotation(),\n            rotateWithView: image.getRotateWithView(),\n          })\n        );\n      }\n      const zIndex = style.getZIndex() || 0;\n      let byGeometryType = featuresByZIndex[zIndex];\n      if (!byGeometryType) {\n        byGeometryType = {};\n        featuresByZIndex[zIndex] = byGeometryType;\n        byGeometryType['Polygon'] = [];\n        byGeometryType['Circle'] = [];\n        byGeometryType['LineString'] = [];\n        byGeometryType['Point'] = [];\n      }\n      const type = geometry.getType();\n      if (type === 'GeometryCollection') {\n        const geometries =\n          /** @type {import(\"../../geom/GeometryCollection.js\").default} */ (\n            geometry\n          ).getGeometriesArrayRecursive();\n        for (let i = 0, ii = geometries.length; i < ii; ++i) {\n          const geometry = geometries[i];\n          byGeometryType[geometry.getType().replace('Multi', '')].push(\n            geometry,\n            style\n          );\n        }\n      } else {\n        byGeometryType[type.replace('Multi', '')].push(geometry, style);\n      }\n    }\n  }\n\n  const zIndexKeys = Object.keys(featuresByZIndex).map(Number).sort(ascending);\n  for (let i = 0, ii = zIndexKeys.length; i < ii; ++i) {\n    const byGeometryType = featuresByZIndex[zIndexKeys[i]];\n    for (const type in byGeometryType) {\n      const geomAndStyle = byGeometryType[type];\n      for (let j = 0, jj = geomAndStyle.length; j < jj; j += 2) {\n        renderer.setStyle(geomAndStyle[j + 1]);\n        for (let k = 0, kk = transforms.length; k < kk; ++k) {\n          renderer.setTransform(transforms[k]);\n          renderer.drawGeometry(geomAndStyle[j]);\n        }\n      }\n    }\n  }\n  return context.getImageData(0, 0, canvas.width, canvas.height);\n}\n\n/**\n * @param {import(\"../../pixel\").Pixel} pixel Pixel coordinate on the hit\n * detection canvas in css pixels.\n * @param {Array<F>} features Features. Has to\n * match the `features` array that was passed to `createHitDetectionImageData()`.\n * @param {ImageData} imageData Hit detection image data generated by\n * `createHitDetectionImageData()`.\n * @return {Array<F>} Features.\n * @template {import(\"../../Feature.js\").FeatureLike} F\n */\nexport function hitDetect(pixel, features, imageData) {\n  const resultFeatures = [];\n  if (imageData) {\n    const x = Math.floor(Math.round(pixel[0]) * HIT_DETECT_RESOLUTION);\n    const y = Math.floor(Math.round(pixel[1]) * HIT_DETECT_RESOLUTION);\n    // The pixel coordinate is clamped down to the hit-detect canvas' size to account\n    // for browsers returning coordinates slightly larger than the actual canvas size\n    // due to a non-integer pixel ratio.\n    const index =\n      (clamp(x, 0, imageData.width - 1) +\n        clamp(y, 0, imageData.height - 1) * imageData.width) *\n      4;\n    const r = imageData.data[index];\n    const g = imageData.data[index + 1];\n    const b = imageData.data[index + 2];\n    const i = b + 256 * (g + 256 * r);\n    const indexFactor = Math.floor((256 * 256 * 256 - 1) / features.length);\n    if (i && i % indexFactor === 0) {\n      resultFeatures.push(features[i / indexFactor - 1]);\n    }\n  }\n  // @ts-ignore Features are copied from `features` to `resultFeatures` so the type should be the same\n  return resultFeatures;\n}\n", "/**\n * @module ol/renderer/vector\n */\nimport ImageState from '../ImageState.js';\nimport {getUid} from '../util.js';\n\n/**\n * Feature callback. The callback will be called with three arguments. The first\n * argument is one {@link module:ol/Feature~Feature feature} or {@link module:ol/render/Feature~RenderFeature render feature}\n * at the pixel, the second is the {@link module:ol/layer/Layer~Layer layer} of the feature and will be null for\n * unmanaged layers. The third is the {@link module:ol/geom/SimpleGeometry~SimpleGeometry} of the feature. For features\n * with a GeometryCollection geometry, it will be the first detected geometry from the collection.\n * @template T\n * @typedef {function(import(\"../Feature.js\").FeatureLike, import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>, import(\"../geom/SimpleGeometry.js\").default): T} FeatureCallback\n */\n\n/**\n * Tolerance for geometry simplification in device pixels.\n * @type {number}\n */\nconst SIMPLIFY_TOLERANCE = 0.5;\n\n/**\n * @const\n * @type {Object<import(\"../geom/Geometry.js\").Type,\n *                function(import(\"../render/canvas/BuilderGroup.js\").default, import(\"../geom/Geometry.js\").default,\n *                         import(\"../style/Style.js\").default, Object): void>}\n */\nconst GEOMETRY_RENDERERS = {\n  'Point': renderPointGeometry,\n  'LineString': renderLineStringGeometry,\n  'Polygon': renderPolygonGeometry,\n  'MultiPoint': renderMultiPointGeometry,\n  'MultiLineString': renderMultiLineStringGeometry,\n  'MultiPolygon': renderMultiPolygonGeometry,\n  'GeometryCollection': renderGeometryCollectionGeometry,\n  'Circle': renderCircleGeometry,\n};\n\n/**\n * @param {import(\"../Feature.js\").FeatureLike} feature1 Feature 1.\n * @param {import(\"../Feature.js\").FeatureLike} feature2 Feature 2.\n * @return {number} Order.\n */\nexport function defaultOrder(feature1, feature2) {\n  return parseInt(getUid(feature1), 10) - parseInt(getUid(feature2), 10);\n}\n\n/**\n * @param {number} resolution Resolution.\n * @param {number} pixelRatio Pixel ratio.\n * @return {number} Squared pixel tolerance.\n */\nexport function getSquaredTolerance(resolution, pixelRatio) {\n  const tolerance = getTolerance(resolution, pixelRatio);\n  return tolerance * tolerance;\n}\n\n/**\n * @param {number} resolution Resolution.\n * @param {number} pixelRatio Pixel ratio.\n * @return {number} Pixel tolerance.\n */\nexport function getTolerance(resolution, pixelRatio) {\n  return (SIMPLIFY_TOLERANCE * resolution) / pixelRatio;\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Builder group.\n * @param {import(\"../geom/Circle.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").default} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderCircleGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const fillStyle = style.getFill();\n  const strokeStyle = style.getStroke();\n  if (fillStyle || strokeStyle) {\n    const circleReplay = builderGroup.getBuilder(style.getZIndex(), 'Circle');\n    circleReplay.setFillStrokeStyle(fillStyle, strokeStyle);\n    circleReplay.drawCircle(geometry, feature);\n  }\n  const textStyle = style.getText();\n  if (textStyle && textStyle.getText()) {\n    const textReplay = (declutterBuilderGroup || builderGroup).getBuilder(\n      style.getZIndex(),\n      'Text'\n    );\n    textReplay.setTextStyle(textStyle);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} replayGroup Replay group.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n * @param {import(\"../proj.js\").TransformFunction} [transform] Transform from user to view projection.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n * @return {boolean} `true` if style is loading.\n */\nexport function renderFeature(\n  replayGroup,\n  feature,\n  style,\n  squaredTolerance,\n  listener,\n  transform,\n  declutterBuilderGroup\n) {\n  let loading = false;\n  const imageStyle = style.getImage();\n  if (imageStyle) {\n    const imageState = imageStyle.getImageState();\n    if (imageState == ImageState.LOADED || imageState == ImageState.ERROR) {\n      imageStyle.unlistenImageChange(listener);\n    } else {\n      if (imageState == ImageState.IDLE) {\n        imageStyle.load();\n      }\n      imageStyle.listenImageChange(listener);\n      loading = true;\n    }\n  }\n  renderFeatureInternal(\n    replayGroup,\n    feature,\n    style,\n    squaredTolerance,\n    transform,\n    declutterBuilderGroup\n  );\n\n  return loading;\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} replayGroup Replay group.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderFeatureInternal(\n  replayGroup,\n  feature,\n  style,\n  squaredTolerance,\n  transform,\n  declutterBuilderGroup\n) {\n  const geometry = style.getGeometryFunction()(feature);\n  if (!geometry) {\n    return;\n  }\n  const simplifiedGeometry = geometry.simplifyTransformed(\n    squaredTolerance,\n    transform\n  );\n  const renderer = style.getRenderer();\n  if (renderer) {\n    renderGeometry(replayGroup, simplifiedGeometry, style, feature);\n  } else {\n    const geometryRenderer = GEOMETRY_RENDERERS[simplifiedGeometry.getType()];\n    geometryRenderer(\n      replayGroup,\n      simplifiedGeometry,\n      style,\n      feature,\n      declutterBuilderGroup\n    );\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} replayGroup Replay group.\n * @param {import(\"../geom/Geometry.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n */\nfunction renderGeometry(replayGroup, geometry, style, feature) {\n  if (geometry.getType() == 'GeometryCollection') {\n    const geometries =\n      /** @type {import(\"../geom/GeometryCollection.js\").default} */ (\n        geometry\n      ).getGeometries();\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      renderGeometry(replayGroup, geometries[i], style, feature);\n    }\n    return;\n  }\n  const replay = replayGroup.getBuilder(style.getZIndex(), 'Default');\n  replay.drawCustom(\n    /** @type {import(\"../geom/SimpleGeometry.js\").default} */ (geometry),\n    feature,\n    style.getRenderer(),\n    style.getHitDetectionRenderer()\n  );\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} replayGroup Replay group.\n * @param {import(\"../geom/GeometryCollection.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").default} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderGeometryCollectionGeometry(\n  replayGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const geometries = geometry.getGeometriesArray();\n  let i, ii;\n  for (i = 0, ii = geometries.length; i < ii; ++i) {\n    const geometryRenderer = GEOMETRY_RENDERERS[geometries[i].getType()];\n    geometryRenderer(\n      replayGroup,\n      geometries[i],\n      style,\n      feature,\n      declutterBuilderGroup\n    );\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/LineString.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderLineStringGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const strokeStyle = style.getStroke();\n  if (strokeStyle) {\n    const lineStringReplay = builderGroup.getBuilder(\n      style.getZIndex(),\n      'LineString'\n    );\n    lineStringReplay.setFillStrokeStyle(null, strokeStyle);\n    lineStringReplay.drawLineString(geometry, feature);\n  }\n  const textStyle = style.getText();\n  if (textStyle && textStyle.getText()) {\n    const textReplay = (declutterBuilderGroup || builderGroup).getBuilder(\n      style.getZIndex(),\n      'Text'\n    );\n    textReplay.setTextStyle(textStyle);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/MultiLineString.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderMultiLineStringGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const strokeStyle = style.getStroke();\n  if (strokeStyle) {\n    const lineStringReplay = builderGroup.getBuilder(\n      style.getZIndex(),\n      'LineString'\n    );\n    lineStringReplay.setFillStrokeStyle(null, strokeStyle);\n    lineStringReplay.drawMultiLineString(geometry, feature);\n  }\n  const textStyle = style.getText();\n  if (textStyle && textStyle.getText()) {\n    const textReplay = (declutterBuilderGroup || builderGroup).getBuilder(\n      style.getZIndex(),\n      'Text'\n    );\n    textReplay.setTextStyle(textStyle);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/MultiPolygon.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").default} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderMultiPolygonGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const fillStyle = style.getFill();\n  const strokeStyle = style.getStroke();\n  if (strokeStyle || fillStyle) {\n    const polygonReplay = builderGroup.getBuilder(style.getZIndex(), 'Polygon');\n    polygonReplay.setFillStrokeStyle(fillStyle, strokeStyle);\n    polygonReplay.drawMultiPolygon(geometry, feature);\n  }\n  const textStyle = style.getText();\n  if (textStyle && textStyle.getText()) {\n    const textReplay = (declutterBuilderGroup || builderGroup).getBuilder(\n      style.getZIndex(),\n      'Text'\n    );\n    textReplay.setTextStyle(textStyle);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/Point.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderPointGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const imageStyle = style.getImage();\n  const textStyle = style.getText();\n  /** @type {import(\"../render/canvas.js\").DeclutterImageWithText} */\n  let declutterImageWithText;\n  if (imageStyle) {\n    if (imageStyle.getImageState() != ImageState.LOADED) {\n      return;\n    }\n    let imageBuilderGroup = builderGroup;\n    if (declutterBuilderGroup) {\n      const declutterMode = imageStyle.getDeclutterMode();\n      if (declutterMode !== 'none') {\n        imageBuilderGroup = declutterBuilderGroup;\n        if (declutterMode === 'obstacle') {\n          // draw in non-declutter group:\n          const imageReplay = builderGroup.getBuilder(\n            style.getZIndex(),\n            'Image'\n          );\n          imageReplay.setImageStyle(imageStyle, declutterImageWithText);\n          imageReplay.drawPoint(geometry, feature);\n        } else if (textStyle && textStyle.getText()) {\n          declutterImageWithText = {};\n        }\n      }\n    }\n    const imageReplay = imageBuilderGroup.getBuilder(\n      style.getZIndex(),\n      'Image'\n    );\n    imageReplay.setImageStyle(imageStyle, declutterImageWithText);\n    imageReplay.drawPoint(geometry, feature);\n  }\n  if (textStyle && textStyle.getText()) {\n    let textBuilderGroup = builderGroup;\n    if (declutterBuilderGroup) {\n      textBuilderGroup = declutterBuilderGroup;\n    }\n    const textReplay = textBuilderGroup.getBuilder(style.getZIndex(), 'Text');\n    textReplay.setTextStyle(textStyle, declutterImageWithText);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/MultiPoint.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderMultiPointGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const imageStyle = style.getImage();\n  const textStyle = style.getText();\n  /** @type {import(\"../render/canvas.js\").DeclutterImageWithText} */\n  let declutterImageWithText;\n  if (imageStyle) {\n    if (imageStyle.getImageState() != ImageState.LOADED) {\n      return;\n    }\n    let imageBuilderGroup = builderGroup;\n    if (declutterBuilderGroup) {\n      const declutterMode = imageStyle.getDeclutterMode();\n      if (declutterMode !== 'none') {\n        imageBuilderGroup = declutterBuilderGroup;\n        if (declutterMode === 'obstacle') {\n          // draw in non-declutter group:\n          const imageReplay = builderGroup.getBuilder(\n            style.getZIndex(),\n            'Image'\n          );\n          imageReplay.setImageStyle(imageStyle, declutterImageWithText);\n          imageReplay.drawMultiPoint(geometry, feature);\n        } else if (textStyle && textStyle.getText()) {\n          declutterImageWithText = {};\n        }\n      }\n    }\n    const imageReplay = imageBuilderGroup.getBuilder(\n      style.getZIndex(),\n      'Image'\n    );\n    imageReplay.setImageStyle(imageStyle, declutterImageWithText);\n    imageReplay.drawMultiPoint(geometry, feature);\n  }\n  if (textStyle && textStyle.getText()) {\n    let textBuilderGroup = builderGroup;\n    if (declutterBuilderGroup) {\n      textBuilderGroup = declutterBuilderGroup;\n    }\n    const textReplay = textBuilderGroup.getBuilder(style.getZIndex(), 'Text');\n    textReplay.setTextStyle(textStyle, declutterImageWithText);\n    textReplay.drawText(geometry, feature);\n  }\n}\n\n/**\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n * @param {import(\"../geom/Polygon.js\").default|import(\"../render/Feature.js\").default} geometry Geometry.\n * @param {import(\"../style/Style.js\").default} style Style.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {import(\"../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n */\nfunction renderPolygonGeometry(\n  builderGroup,\n  geometry,\n  style,\n  feature,\n  declutterBuilderGroup\n) {\n  const fillStyle = style.getFill();\n  const strokeStyle = style.getStroke();\n  if (fillStyle || strokeStyle) {\n    const polygonReplay = builderGroup.getBuilder(style.getZIndex(), 'Polygon');\n    polygonReplay.setFillStrokeStyle(fillStyle, strokeStyle);\n    polygonReplay.drawPolygon(geometry, feature);\n  }\n  const textStyle = style.getText();\n  if (textStyle && textStyle.getText()) {\n    const textReplay = (declutterBuilderGroup || builderGroup).getBuilder(\n      style.getZIndex(),\n      'Text'\n    );\n    textReplay.setTextStyle(textStyle);\n    textReplay.drawText(geometry, feature);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmMO,SAAS,QAAQ,WAAW;AACjC,QAAM,QAAQ,IAAI,cAAM;AAAA,IACtB,MAAM,QAAQ,WAAW,EAAE;AAAA,IAC3B,QAAQ,UAAU,WAAW,EAAE;AAAA,IAC/B,MAAM,QAAQ,SAAS;AAAA,IACvB,OAAO,SAAS,SAAS;AAAA,EAC3B,CAAC;AAED,SAAO;AACT;AAOA,SAAS,QAAQ,WAAW,QAAQ;AAClC,QAAM,QAAQ,UAAU,SAAS,YAAY;AAC7C,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,UAAU,QAAQ;AACpB,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,aAAK,EAAC,MAAY,CAAC;AAChC;AAOA,SAAS,UAAU,WAAW,QAAQ;AACpC,QAAM,QAAQ,UAAU,SAAS,cAAc;AAC/C,QAAM,QAAQ,UAAU,SAAS,cAAc;AAC/C,MAAI,CAAC,SAAS,CAAC,OAAO;AACpB;AAAA,EACF;AAEA,SAAO,IAAI,eAAO;AAAA,IAChB;AAAA,IACA;AAAA,IACA,SAAS,UAAU,SAAS,iBAAiB;AAAA,IAC7C,UAAU,UAAU,SAAS,kBAAkB;AAAA,IAC/C,UAAU,UAAU,SAAS,kBAAkB;AAAA,IAC/C,gBAAgB,UAAU,SAAS,yBAAyB;AAAA,IAC5D,YAAY,UAAU,SAAS,oBAAoB;AAAA,EACrD,CAAC;AACH;AAMA,SAAS,QAAQ,WAAW;AAC1B,QAAM,QAAQ,UAAU,YAAY;AACpC,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA,QAAM,OAAO,IAAI,aAAK;AAAA,IACpB,MAAM;AAAA,IACN,MAAM,UAAU,WAAW;AAAA,IAC3B,UAAU,UAAU,gBAAgB;AAAA,IACpC,SAAS,UAAU,eAAe;AAAA,IAClC,SAAS,UAAU,eAAe;AAAA,IAClC,UAAU,UAAU,eAAe;AAAA,IACnC,WAAW,UAAU,gBAAgB;AAAA,IACrC,QAAQ,UAAU,aAAa;AAAA,IAC/B,OAAO,UAAU,YAAY;AAAA,IAC7B,gBAAgB,UAAU,uBAAuB;AAAA,IACjD,UAAU,UAAU,eAAe;AAAA,IACnC,WAAW,UAAU,YAAY;AAAA,IACjC,SAAS,UAAU,cAAc;AAAA,IACjC,cAAc,UAAU,eAAe;AAAA,IACvC,SAAS,UAAU,cAAc;AAAA,IACjC,MAAM,QAAQ,WAAW,OAAO;AAAA,IAChC,gBAAgB,QAAQ,WAAW,kBAAkB;AAAA,IACrD,QAAQ,UAAU,WAAW,OAAO;AAAA,IACpC,kBAAkB,UAAU,WAAW,kBAAkB;AAAA,EAC3D,CAAC;AAED,SAAO;AACT;AAMA,SAAS,SAAS,WAAW;AAC3B,QAAM,UAAU,UAAU,UAAU;AACpC,QAAM,UAAU,UAAU,UAAU;AACpC,MAAI,WAAW,SAAS;AACtB,UAAM,OAAO,IAAI,aAAK;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS,UAAU,eAAe;AAAA,MAClC,QAAQ,UAAU,aAAa;AAAA,MAC/B,cAAc,UAAU,oBAAoB;AAAA,MAC5C,cAAc,UAAU,qBAAqB;AAAA,MAC7C,cAAc,UAAU,qBAAqB;AAAA,MAC7C,OAAO,UAAU,YAAY;AAAA,MAC7B,aAAa,UAAU,mBAAmB;AAAA,MAC1C,QAAQ,UAAU,aAAa;AAAA,MAC/B,cAAc,UAAU,mBAAmB;AAAA,MAC3C,SAAS,UAAU,cAAc;AAAA,MACjC,OAAO,UAAU,YAAY;AAAA,MAC7B,OAAO,UAAU,YAAY;AAAA,MAC7B,QAAQ,UAAU,aAAa;AAAA,MAC/B,UAAU,UAAU,eAAe;AAAA,MACnC,gBAAgB,UAAU,uBAAuB;AAAA,MACjD,MAAM,UAAU,WAAW;AAAA,MAC3B,eAAe,UAAU,qBAAqB;AAAA,IAChD,CAAC;AACD,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,UAAU,cAAc;AAC5C,MAAI,aAAa;AACf,UAAM,SAAS;AACf,UAAM,QAAQ,IAAI,qBAAa;AAAA,MAC7B,QAAQ;AAAA,MACR,MAAM,QAAQ,WAAW,MAAM;AAAA,MAC/B,QAAQ,UAAU,WAAW,MAAM;AAAA,MACnC,QAAQ,UAAU,cAAc;AAAA,MAChC,SAAS,UAAU,eAAe;AAAA,MAClC,SAAS,UAAU,eAAe;AAAA,MAClC,OAAO,UAAU,aAAa;AAAA,MAC9B,cAAc,UAAU,oBAAoB;AAAA,MAC5C,UAAU,UAAU,gBAAgB;AAAA,MACpC,gBAAgB,UAAU,wBAAwB;AAAA,MAClD,OAAO,UAAU,aAAa;AAAA,MAC9B,eAAe,UAAU,sBAAsB;AAAA,IACjD,CAAC;AAED,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,UAAU,eAAe;AAC9C,MAAI,cAAc;AAChB,UAAM,SAAS;AACf,UAAM,SAAS,IAAI,eAAO;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,QAAQ,WAAW,MAAM;AAAA,MAC/B,QAAQ,UAAU,WAAW,MAAM;AAAA,MACnC,cAAc,UAAU,qBAAqB;AAAA,MAC7C,OAAO,UAAU,cAAc;AAAA,MAC/B,UAAU,UAAU,iBAAiB;AAAA,MACrC,gBAAgB,UAAU,yBAAyB;AAAA,MACnD,eAAe,UAAU,uBAAuB;AAAA,IAClD,CAAC;AAED,WAAO;AAAA,EACT;AAEA;AACF;AAKO,SAASA,sBAAqB;AACnC,SAAO;AAAA,IACL,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACzB;AACF;;;ACzSA,IAAM,WAAW;AAAA,EACf,cAAc;AAChB;AAcA,IAAM,kBAAN,cAA8B,cAAM;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,OAAO;AAE7C,WAAO,YAAY;AACnB,WAAO,YAAY;AACnB,WAAO,YAAY;AACnB,WAAO,YAAY;AACnB,UAAM,WAAW;AAMjB,SAAK,aACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,gBACH,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAO9D,SAAK,SAAS;AAOd,SAAK,iBAAiB;AAEtB,SAAK,SAAS,QAAQ,KAAK;AAM3B,SAAK,wBACH,QAAQ,yBAAyB,SAC7B,QAAQ,uBACR;AAMN,SAAK,0BACH,QAAQ,2BAA2B,SAC/B,QAAQ,yBACR;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,YAAY,OAAO;AACjB,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf;AAAA;AAAA,MACE,KAAK,IAAI,SAAS,YAAY;AAAA;AAAA,EAElC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,4BAA4B;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,YAAY;AAC1B,QAAI,CAAC,WAAW,eAAe;AAC7B,iBAAW,gBAAgB,IAAI,MAAM,CAAC;AAAA,IACxC;AACiB,IAAC,KAAK,YAAY,EAAG,gBAAgB,UAAU;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,aAAa;AAC1B,SAAK,IAAI,SAAS,cAAc,WAAW;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,SAAS,OAAO;AAId,QAAI;AAEJ,QAAI,UAAU,QAAW;AACvB,kBAAY;AAAA,IACd,WAAW,UAAU,MAAM;AACzB,kBAAY;AAAA,IACd,WAAW,OAAO,UAAU,YAAY;AACtC,kBAAY;AAAA,IACd,WAAW,iBAAiB,eAAO;AACjC,kBAAY;AAAA,IACd,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,YAAM,MAAM,MAAM;AAKlB,YAAM,SAAS,IAAI,MAAM,GAAG;AAE5B,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,cAAM,IAAI,MAAM,CAAC;AACjB,YAAI,aAAa,eAAO;AACtB,iBAAO,CAAC,IAAI;AAAA,QACd,OAAO;AACL,iBAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,QACvB;AAAA,MACF;AACA,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,QAAQ,KAAK;AAAA,IAC3B;AAEA,SAAK,SAAS;AACd,SAAK,iBACH,UAAU,OAAO,SAAY,WAAgB,KAAK,MAAM;AAC1D,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;AC9Sf,IAAM,cAAc;AAAA,EAClB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,QAAQ;AACV;AAKO,IAAM,kBAAkB,CAAC,YAAY,IAAI;AAKzC,IAAM,oBAAoB,CAAC,YAAY,MAAM;AAK7C,IAAM,uBAAuB,CAAC,YAAY,UAAU;AAKpD,IAAM,uBAAuB,CAAC,YAAY,UAAU;AAE3D,IAAO,sBAAQ;;;ACjCf,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,WAAW,UAAU,SAAS,UAAU,sBAAsB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/D,aAAa,UAAU;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,SAAS,OAAO;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,WAAW,gBAAgB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC,YAAY,SAAS,OAAO;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,uBAAuB,4BAA4B,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7D,eAAe,oBAAoB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,oBAAoB,yBAAyB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvD,eAAe,oBAAoB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,iBAAiB,sBAAsB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjD,UAAU,eAAe,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,YAAY,iBAAiB,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,SAAS,UAAU,SAAS;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,mBAAmB,WAAW,aAAa;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,cAAc,YAAY,wBAAwB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnD,aAAa,WAAW,wBAAwB;AAAA,EAAC;AACnD;AAEA,IAAO,wBAAQ;;;ACpFf,IAAM,gBAAN,cAA4B,sBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxC,YAAY,WAAW,WAAW,YAAY,YAAY;AACxD,UAAM;AAMN,SAAK,YAAY;AAOjB,SAAK,YAAY;AAMjB,SAAK,aAAa;AAMlB,SAAK,eAAe;AAOpB,SAAK,aAAa;AAMlB,SAAK,6BAA6B;AAMlC,SAAK,6BAA6B;AAMlC,SAAK,qBAAqB;AAM1B,SAAK,eAAe,CAAC;AAMrB,SAAK,cAAc,CAAC;AAMpB,SAAK,iBAAiB,CAAC;AAMvB,SAAK,2BAA2B,CAAC;AAMjC,SAAK;AAAA,IAA+D,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,WAAW;AACzB,UAAM,aAAa,KAAK;AACxB,WAAO,cAAc,IACjB,YACA,UAAU,IAAI,SAAU,MAAM;AAC5B,aAAO,OAAO;AAAA,IAChB,CAAC;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B,iBAAiB,QAAQ;AAClD,UAAM,SAAS,KAAK,qBAAqB;AACzC,UAAM,WAAW,KAAK;AACtB,UAAM,cAAc,KAAK;AACzB,QAAI,QAAQ,YAAY;AACxB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,eAAS,CAAC,IAAI,gBAAgB,CAAC;AAC/B,eAAS,CAAC,IAAI,gBAAgB,IAAI,CAAC;AACnC,UAAI,mBAAmB,QAAQ,QAAQ,GAAG;AACxC,oBAAY,OAAO,IAAI,SAAS,CAAC;AACjC,oBAAY,OAAO,IAAI,SAAS,CAAC;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,0BACE,iBACA,QACA,KACA,QACA,QACA,WACA;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,QAAQ,YAAY;AACxB,UAAM,SAAS,KAAK,qBAAqB;AACzC,QAAI,WAAW;AACb,gBAAU;AAAA,IACZ;AACA,QAAI,aAAa,gBAAgB,MAAM;AACvC,QAAI,aAAa,gBAAgB,SAAS,CAAC;AAC3C,UAAM,YAAY,KAAK;AACvB,QAAI,UAAU;AAEd,QAAI,GAAG,SAAS;AAChB,SAAK,IAAI,SAAS,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC9C,gBAAU,CAAC,IAAI,gBAAgB,CAAC;AAChC,gBAAU,CAAC,IAAI,gBAAgB,IAAI,CAAC;AACpC,gBAAU,uBAAuB,QAAQ,SAAS;AAClD,UAAI,YAAY,SAAS;AACvB,YAAI,SAAS;AACX,sBAAY,OAAO,IAAI;AACvB,sBAAY,OAAO,IAAI;AACvB,oBAAU;AAAA,QACZ;AACA,oBAAY,OAAO,IAAI,UAAU,CAAC;AAClC,oBAAY,OAAO,IAAI,UAAU,CAAC;AAAA,MACpC,WAAW,YAAY,qBAAa,cAAc;AAChD,oBAAY,OAAO,IAAI,UAAU,CAAC;AAClC,oBAAY,OAAO,IAAI,UAAU,CAAC;AAClC,kBAAU;AAAA,MACZ,OAAO;AACL,kBAAU;AAAA,MACZ;AACA,mBAAa,UAAU,CAAC;AACxB,mBAAa,UAAU,CAAC;AACxB,gBAAU;AAAA,IACZ;AAGA,QAAK,UAAU,WAAY,MAAM,SAAS,QAAQ;AAChD,kBAAY,OAAO,IAAI;AACvB,kBAAY,OAAO,IAAI;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,uBAAuB,iBAAiB,QAAQ,MAAM,QAAQ,aAAa;AACzE,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,aAAa,KAAK;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,kBAAY,KAAK,UAAU;AAC3B,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU,SAAS,UAAU,sBAAsB;AAC5D,SAAK,cAAc,UAAU,OAAO;AAEpC,UAAM,OAAO,SAAS,QAAQ;AAC9B,UAAM,SAAS,SAAS,UAAU;AAClC,UAAM,eAAe,KAAK,YAAY;AAEtC,QAAI,iBAAiB,YAAY,aAAa;AAC9C,QAAI;AAEJ,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH;AAAA,QAEI,SACA,2BAA2B;AAC/B,uBAAe,CAAC;AAChB,cAAM;AAAA;AAAA,UAEF,SACA,SAAS;AAAA;AACb,iBAAS;AACT,iBAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,gBAAM,SAAS,CAAC;AAChB,mBAAS,KAAK;AAAA,YACZ;AAAA,YACA;AAAA,YACA,MAAM,CAAC;AAAA,YACP;AAAA,YACA;AAAA,UACF;AACA,uBAAa,KAAK,MAAM;AAAA,QAC1B;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,yBAAyB,KAAK;AAAA,UACjC,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB;AAAA,UACxB;AAAA,QACF,CAAC;AACD;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,sBAAc,CAAC;AACf,0BACE,QAAQ;AAAA;AAAA,UAEF,SACA,2BAA2B;AAAA,YAC7B,SAAS,mBAAmB;AAClC,iBAAS,KAAK;AAAA,UACZ;AAAA,UACA;AAAA;AAAA,UAEE,SACA,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,QACF;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,yBAAyB,KAAK;AAAA,UACjC,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB;AAAA,UACxB;AAAA,QACF,CAAC;AACD;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,0BAAkB,SAAS,mBAAmB;AAC9C,qBAAa,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,UACA,gBAAgB;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,yBAAyB,KAAK;AAAA,UACjC,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB;AAAA,UACxB;AAAA,QACF,CAAC;AACD;AAAA,MACF,KAAK;AACH,0BAAkB,SAAS,mBAAmB;AAC9C,qBAAa,KAAK,2BAA2B,iBAAiB,MAAM;AAEpE,YAAI,aAAa,cAAc;AAC7B,eAAK,aAAa,KAAK;AAAA,YACrB,oBAAkB;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,eAAK,yBAAyB,KAAK;AAAA,YACjC,oBAAkB;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA,wBAAwB;AAAA,YACxB;AAAA,UACF,CAAC;AAAA,QACH;AACA;AAAA,MACF,KAAK;AACH,0BAAkB,SAAS,mBAAmB;AAC9C,aAAK,YAAY,KAAK,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,CAAC;AAC5D,qBAAa,KAAK,YAAY;AAE9B,aAAK,aAAa,KAAK;AAAA,UACrB,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,yBAAyB,KAAK;AAAA,UACjC,oBAAkB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB;AAAA,QAC1B,CAAC;AACD;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,UAAU,SAAS;AAC/B,SAAK,6BAA6B;AAAA,MAChC,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,aAAa,KAAK,KAAK,0BAA0B;AACtD,SAAK,6BAA6B;AAAA,MAChC,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,yBAAyB,KAAK,KAAK,0BAA0B;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO;AAAA,MACL,cAAc,KAAK;AAAA,MACnB,0BAA0B,KAAK;AAAA,MAC/B,aAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kCAAkC;AAChC,UAAM,2BAA2B,KAAK;AAEtC,6BAAyB,QAAQ;AAEjC,QAAI;AACJ,UAAM,IAAI,yBAAyB;AACnC,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ;AACZ,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,oBAAc,yBAAyB,CAAC;AACxC;AAAA,MAA0D,YAAY,CAAC;AACvE,UAAI,QAAQ,oBAAkB,cAAc;AAC1C,gBAAQ;AAAA,MACV,WAAW,QAAQ,oBAAkB,gBAAgB;AACnD,oBAAY,CAAC,IAAI;AACjB,wBAAgB,KAAK,0BAA0B,OAAO,CAAC;AACvD,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,WAAW,aAAa;AACzC,UAAM,QAAQ,KAAK;AACnB,QAAI,WAAW;AACb,YAAM,iBAAiB,UAAU,SAAS;AAC1C,YAAM,YAAY;AAAA,QAChB,iBAAiB,iBAAiB;AAAA,MACpC;AAAA,IACF,OAAO;AACL,YAAM,YAAY;AAAA,IACpB;AACA,QAAI,aAAa;AACf,YAAM,mBAAmB,YAAY,SAAS;AAC9C,YAAM,cAAc;AAAA,QAClB,mBAAmB,mBAAmB;AAAA,MACxC;AACA,YAAM,qBAAqB,YAAY,WAAW;AAClD,YAAM,UACJ,uBAAuB,SAAY,qBAAqB;AAC1D,YAAM,sBAAsB,YAAY,YAAY;AACpD,YAAM,WAAW,sBACb,oBAAoB,MAAM,IAC1B;AACJ,YAAM,4BAA4B,YAAY,kBAAkB;AAChE,YAAM,iBAAiB,4BACnB,4BACA;AACJ,YAAM,sBAAsB,YAAY,YAAY;AACpD,YAAM,WACJ,wBAAwB,SACpB,sBACA;AACN,YAAM,mBAAmB,YAAY,SAAS;AAC9C,YAAM,YACJ,qBAAqB,SAAY,mBAAmB;AACtD,YAAM,wBAAwB,YAAY,cAAc;AACxD,YAAM,aACJ,0BAA0B,SACtB,wBACA;AAEN,UAAI,MAAM,YAAY,KAAK,cAAc;AACvC,aAAK,eAAe,MAAM;AAE1B,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,YAAM,cAAc;AACpB,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,iBAAiB;AACvB,YAAM,WAAW;AACjB,YAAM,YAAY;AAClB,YAAM,aAAa;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,UAAM,YAAY,MAAM;AAExB,UAAMC,mBAAkB,CAAC,oBAAkB,gBAAgB,SAAS;AACpE,QAAI,OAAO,cAAc,UAAU;AAEjC,MAAAA,iBAAgB,KAAK,IAAI;AAAA,IAC3B;AACA,WAAOA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,SAAK,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,WAAO;AAAA,MACL,oBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,YAAY,KAAK;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK,gBAAgB,MAAM,QAAQ;AAAA,MACnC,MAAM,iBAAiB,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO,YAAY;AACjC,UAAM,YAAY,MAAM;AACxB,QAAI,OAAO,cAAc,YAAY,MAAM,oBAAoB,WAAW;AACxE,UAAI,cAAc,QAAW;AAC3B,aAAK,aAAa,KAAK,WAAW,KAAK,MAAM,KAAK,CAAC;AAAA,MACrD;AACA,YAAM,mBAAmB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,OAAO,aAAa;AACpC,UAAM,cAAc,MAAM;AAC1B,UAAM,UAAU,MAAM;AACtB,UAAM,WAAW,MAAM;AACvB,UAAM,iBAAiB,MAAM;AAC7B,UAAM,WAAW,MAAM;AACvB,UAAM,YAAY,MAAM;AACxB,UAAM,aAAa,MAAM;AACzB,QACE,MAAM,sBAAsB,eAC5B,MAAM,kBAAkB,WACvB,YAAY,MAAM,mBACjB,CAAC,OAAO,MAAM,iBAAiB,QAAQ,KACzC,MAAM,yBAAyB,kBAC/B,MAAM,mBAAmB,YACzB,MAAM,oBAAoB,aAC1B,MAAM,qBAAqB,YAC3B;AACA,UAAI,gBAAgB,QAAW;AAC7B,oBAAY,KAAK,MAAM,KAAK;AAAA,MAC9B;AACA,YAAM,qBAAqB;AAC3B,YAAM,iBAAiB;AACvB,YAAM,kBAAkB;AACxB,YAAM,wBAAwB;AAC9B,YAAM,kBAAkB;AACxB,YAAM,mBAAmB;AACzB,YAAM,oBAAoB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AACnB,SAAK,2BAA2B,CAAC,IAAI,KAAK,aAAa;AACvD,SAAK,6BAA6B;AAClC,SAAK,2BAA2B,CAAC,IAAI,KAAK,yBAAyB;AACnE,SAAK,6BAA6B;AAClC,UAAM,yBAAyB,CAAC,oBAAkB,cAAc,OAAO;AACvE,SAAK,aAAa,KAAK,sBAAsB;AAC7C,SAAK,yBAAyB,KAAK,sBAAsB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB;AACrB,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,qBAAqB,MAAM,KAAK,SAAS;AAC9C,UAAI,KAAK,eAAe,GAAG;AACzB,cAAM,QAAS,KAAK,cAAc,KAAK,eAAe,KAAM;AAC5D,eAAO,KAAK,oBAAoB,OAAO,KAAK,kBAAkB;AAAA,MAChE;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,kBAAQ;;;ACpoBf,IAAM,qBAAN,cAAiC,gBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7C,YAAY,WAAW,WAAW,YAAY,YAAY;AACxD,UAAM,WAAW,WAAW,YAAY,UAAU;AAMlD,SAAK,qBAAqB;AAM1B,SAAK,SAAS;AAMd,SAAK,mBAAmB;AAMxB,SAAK,WAAW;AAMhB,SAAK,WAAW;AAMhB,SAAK,UAAU;AAMf,SAAK,WAAW;AAMhB,SAAK,WAAW;AAMhB,SAAK,WAAW;AAMhB,SAAK,kBAAkB;AAMvB,SAAK,YAAY;AAMjB,SAAK,SAAS;AAMd,SAAK,SAAS;AAMd,SAAK,iBAAiB;AAOtB,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,eAAe,SAAS;AAChC,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,SAAK,cAAc,eAAe,OAAO;AACzC,UAAM,kBAAkB,cAAc,mBAAmB;AACzD,UAAM,SAAS,cAAc,UAAU;AACvC,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,KAAK,2BAA2B,iBAAiB,MAAM;AACrE,SAAK,aAAa,KAAK;AAAA,MACrB,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,MAEL,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,KAAK,KAAK,UAAU,KAAK,gBAAgB;AAAA,MAC9C,KAAK;AAAA,MACL,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,QACG,KAAK,OAAO,CAAC,IAAI,KAAK,aAAc,KAAK;AAAA,QACzC,KAAK,OAAO,CAAC,IAAI,KAAK,aAAc,KAAK;AAAA,MAC5C;AAAA,MACA,KAAK,KAAK,KAAK,SAAS,KAAK,gBAAgB;AAAA,MAC7C,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC;AACD,SAAK,yBAAyB,KAAK;AAAA,MACjC,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC;AACD,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,oBAAoB,SAAS;AAC1C,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,SAAK,cAAc,oBAAoB,OAAO;AAC9C,UAAM,kBAAkB,mBAAmB,mBAAmB;AAC9D,UAAM,SAAS,mBAAmB,UAAU;AAC5C,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,KAAK,2BAA2B,iBAAiB,MAAM;AACrE,SAAK,aAAa,KAAK;AAAA,MACrB,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,MAEL,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,KAAK,KAAK,UAAU,KAAK,gBAAgB;AAAA,MAC9C,KAAK;AAAA,MACL,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK,WAAW,KAAK;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,QACG,KAAK,OAAO,CAAC,IAAI,KAAK,aAAc,KAAK;AAAA,QACzC,KAAK,OAAO,CAAC,IAAI,KAAK,aAAc,KAAK;AAAA,MAC5C;AAAA,MACA,KAAK,KAAK,KAAK,SAAS,KAAK,gBAAgB;AAAA,MAC7C,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC;AACD,SAAK,yBAAyB,KAAK;AAAA,MACjC,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC;AACD,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,gCAAgC;AAErC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,YAAY,YAAY;AACpC,UAAM,SAAS,WAAW,UAAU;AACpC,UAAM,OAAO,WAAW,QAAQ;AAChC,UAAM,SAAS,WAAW,UAAU;AACpC,SAAK,mBAAmB,WAAW,cAAc,KAAK,UAAU;AAChE,SAAK,WAAW,OAAO,CAAC;AACxB,SAAK,WAAW,OAAO,CAAC;AACxB,SAAK,qBAAqB,WAAW,qBAAqB;AAC1D,SAAK,SAAS,WAAW,SAAS,KAAK,UAAU;AACjD,SAAK,UAAU,KAAK,CAAC;AACrB,SAAK,WAAW,WAAW,WAAW;AACtC,SAAK,WAAW,OAAO,CAAC;AACxB,SAAK,WAAW,OAAO,CAAC;AACxB,SAAK,kBAAkB,WAAW,kBAAkB;AACpD,SAAK,YAAY,WAAW,YAAY;AACxC,SAAK,SAAS,WAAW,cAAc;AACvC,SAAK,SAAS,KAAK,CAAC;AACpB,SAAK,iBAAiB,WAAW,iBAAiB;AAClD,SAAK,0BAA0B;AAAA,EACjC;AACF;AAEA,IAAO,uBAAQ;;;ACpQf,IAAM,0BAAN,cAAsC,gBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlD,YAAY,WAAW,WAAW,YAAY,YAAY;AACxD,UAAM,WAAW,WAAW,YAAY,UAAU;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB,iBAAiB,QAAQ,KAAK,QAAQ;AACzD,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,0BAA0B;AAAA,MAC9B,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,SAAK,aAAa,KAAK,uBAAuB;AAC9C,SAAK,yBAAyB,KAAK,uBAAuB;AAC1D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,oBAAoB,SAAS;AAC1C,UAAM,QAAQ,KAAK;AACnB,UAAM,cAAc,MAAM;AAC1B,UAAM,YAAY,MAAM;AACxB,QAAI,gBAAgB,UAAa,cAAc,QAAW;AACxD;AAAA,IACF;AACA,SAAK,kBAAkB,OAAO,KAAK,WAAW;AAC9C,SAAK,cAAc,oBAAoB,OAAO;AAC9C,SAAK,yBAAyB;AAAA,MAC5B;AAAA,QACE,oBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,UAAM,kBAAkB,mBAAmB,mBAAmB;AAC9D,UAAM,SAAS,mBAAmB,UAAU;AAC5C,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,IACF;AACA,SAAK,yBAAyB,KAAK,iBAAiB;AACpD,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,yBAAyB,SAAS;AACpD,UAAM,QAAQ,KAAK;AACnB,UAAM,cAAc,MAAM;AAC1B,UAAM,YAAY,MAAM;AACxB,QAAI,gBAAgB,UAAa,cAAc,QAAW;AACxD;AAAA,IACF;AACA,SAAK,kBAAkB,OAAO,KAAK,WAAW;AAC9C,SAAK,cAAc,yBAAyB,OAAO;AACnD,SAAK,yBAAyB;AAAA,MAC5B;AAAA,QACE,oBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,wBAAwB,QAAQ;AAC7C,UAAM,kBAAkB,wBAAwB,mBAAmB;AACnE,UAAM,SAAS,wBAAwB,UAAU;AACjD,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAS,KAAK;AAAA,QACZ;AAAA,QACA;AAAA;AAAA,QACuB,KAAK,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,SAAK,yBAAyB,KAAK,iBAAiB;AACpD,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,QAAQ,KAAK;AACnB,QACE,MAAM,cAAc,UACpB,MAAM,cAAc,KAAK,YAAY,QACrC;AACA,WAAK,aAAa,KAAK,iBAAiB;AAAA,IAC1C;AACA,SAAK,gCAAgC;AACrC,SAAK,QAAQ;AACb,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QACE,MAAM,cAAc,UACpB,MAAM,cAAc,KAAK,YAAY,QACrC;AACA,WAAK,aAAa,KAAK,iBAAiB;AACxC,YAAM,aAAa,KAAK,YAAY;AAAA,IACtC;AACA,UAAM,aAAa;AACnB,UAAM,YAAY,KAAK;AACvB,SAAK,aAAa,KAAK,oBAAoB;AAAA,EAC7C;AACF;AAEA,IAAO,4BAAQ;;;ACjJf,IAAM,uBAAN,cAAmC,gBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/C,YAAY,WAAW,WAAW,YAAY,YAAY;AACxD,UAAM,WAAW,WAAW,YAAY,UAAU;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,iBAAiB,QAAQ,MAAM,QAAQ;AAC3D,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,MAAM,cAAc;AACjC,UAAM,SAAS,MAAM,gBAAgB;AACrC,UAAM,UAAU,KAAK;AACrB,SAAK,aAAa,KAAK,oBAAoB;AAC3C,SAAK,yBAAyB,KAAK,oBAAoB;AACvD,aAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM,QAAQ,KAAK;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC;AAAA,MACH;AACA,YAAM,0BAA0B;AAAA,QAC9B,oBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AACA,WAAK,aAAa,KAAK,uBAAuB;AAC9C,WAAK,yBAAyB,KAAK,uBAAuB;AAC1D,UAAI,QAAQ;AAGV,aAAK,aAAa,KAAK,oBAAoB;AAC3C,aAAK,yBAAyB,KAAK,oBAAoB;AAAA,MACzD;AACA,eAAS;AAAA,IACX;AACA,QAAI,MAAM;AACR,WAAK,aAAa,KAAK,eAAe;AACtC,WAAK,yBAAyB,KAAK,eAAe;AAAA,IACpD;AACA,QAAI,QAAQ;AACV,WAAK,aAAa,KAAK,iBAAiB;AACxC,WAAK,yBAAyB,KAAK,iBAAiB;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,gBAAgB,SAAS;AAClC,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,MAAM;AACxB,UAAM,cAAc,MAAM;AAC1B,QAAI,cAAc,UAAa,gBAAgB,QAAW;AACxD;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,SAAK,cAAc,gBAAgB,OAAO;AAC1C,QAAI,MAAM,cAAc,QAAW;AACjC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,gBAAgB,QAAW;AACnC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,kBAAkB,eAAe,mBAAmB;AAC1D,UAAM,SAAS,eAAe,UAAU;AACxC,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,oBAAoB,CAAC,oBAAkB,QAAQ,OAAO;AAC5D,SAAK,aAAa,KAAK,sBAAsB,iBAAiB;AAC9D,SAAK,yBAAyB,KAAK,sBAAsB,iBAAiB;AAC1E,QAAI,MAAM,cAAc,QAAW;AACjC,WAAK,aAAa,KAAK,eAAe;AACtC,WAAK,yBAAyB,KAAK,eAAe;AAAA,IACpD;AACA,QAAI,MAAM,gBAAgB,QAAW;AACnC,WAAK,aAAa,KAAK,iBAAiB;AACxC,WAAK,yBAAyB,KAAK,iBAAiB;AAAA,IACtD;AACA,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,iBAAiB,SAAS;AACpC,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,MAAM;AACxB,UAAM,cAAc,MAAM;AAC1B,QAAI,cAAc,UAAa,gBAAgB,QAAW;AACxD;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,SAAK,cAAc,iBAAiB,OAAO;AAC3C,QAAI,MAAM,cAAc,QAAW;AACjC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,gBAAgB,QAAW;AACnC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,OAAO,gBAAgB,QAAQ;AACrC,UAAM,kBAAkB,gBAAgB,2BAA2B;AACnE,UAAM,SAAS,gBAAgB,UAAU;AACzC,SAAK;AAAA,MACH;AAAA,MACA;AAAA;AAAA,MAC8B;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,sBAAsB,SAAS;AAC9C,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,MAAM;AACxB,UAAM,cAAc,MAAM;AAC1B,QAAI,cAAc,UAAa,gBAAgB,QAAW;AACxD;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,SAAK,cAAc,sBAAsB,OAAO;AAChD,QAAI,MAAM,cAAc,QAAW;AACjC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,gBAAgB,QAAW;AACnC,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,QAAQ,qBAAqB,SAAS;AAC5C,UAAM,kBAAkB,qBAAqB,2BAA2B;AACxE,UAAM,SAAS,qBAAqB,UAAU;AAC9C,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,eAAS,KAAK;AAAA,QACZ;AAAA,QACA;AAAA,QACA,MAAM,CAAC;AAAA,QACP;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,gCAAgC;AACrC,SAAK,QAAQ;AAKb,UAAM,YAAY,KAAK;AACvB,QAAI,cAAc,GAAG;AACnB,YAAM,cAAc,KAAK;AACzB,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,oBAAY,CAAC,IAAI,KAAK,YAAY,CAAC,GAAG,SAAS;AAAA,MACjD;AAAA,IACF;AACA,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,MAAM;AACxB,QAAI,cAAc,QAAW;AAC3B,WAAK,gBAAgB,OAAO,KAAK,UAAU;AAAA,IAC7C;AACA,QAAI,MAAM,gBAAgB,QAAW;AACnC,WAAK,kBAAkB,OAAO,KAAK,WAAW;AAAA,IAChD;AAAA,EACF;AACF;AAEA,IAAO,yBAAQ;;;ACzPR,SAAS,UAAU,aAAa,iBAAiB,QAAQ,KAAK,QAAQ;AAC3E,QAAM,SAAS,CAAC;AAChB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,eAAe,gBAAgB,MAAM,QAAQ,CAAC;AAClD,SAAO,SAAS,eAAe,SAAS,SAAS,KAAK;AACpD,UAAM,CAAC,IAAI,EAAE,IAAI,aAAa,MAAM,EAAE;AACtC,UAAM,KAAK,gBAAgB,SAAS,MAAM;AAC1C,UAAM,KAAK,gBAAgB,SAAS,SAAS,CAAC;AAC9C,UAAM,gBAAgB,KAAK;AAAA,OACxB,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK;AAAA,IAC5C;AACA,cAAU;AACV,QAAI,UAAU,aAAa;AACzB,YAAM,KAAK,cAAc,SAAS,iBAAiB;AACnD,YAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AACxB,YAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AACxB,mBAAa,KAAK,GAAG,CAAC;AACtB,aAAO,KAAK,YAAY;AACxB,qBAAe,CAAC,GAAG,CAAC;AACpB,UAAI,UAAU,aAAa;AACzB,kBAAU;AAAA,MACZ;AACA,eAAS;AAAA,IACX,WAAW,SAAS,aAAa;AAC/B,mBAAa;AAAA,QACX,gBAAgB,SAAS,MAAM;AAAA,QAC/B,gBAAgB,SAAS,SAAS,CAAC;AAAA,MACrC;AACA,gBAAU;AAAA,IACZ,OAAO;AACL,YAAM,UAAU,gBAAgB;AAChC,YAAM,IAAI,KAAK,IAAI,IAAI,UAAU,aAAa;AAC9C,YAAM,IAAI,KAAK,IAAI,IAAI,UAAU,aAAa;AAC9C,mBAAa,KAAK,GAAG,CAAC;AACtB,aAAO,KAAK,YAAY;AACxB,qBAAe,CAAC,GAAG,CAAC;AACpB,eAAS;AACT,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,SAAS,GAAG;AACd,WAAO,KAAK,YAAY;AAAA,EAC1B;AACA,SAAO;AACT;;;AC3CO,SAAS,cAAc,UAAU,iBAAiB,QAAQ,KAAK,QAAQ;AAC5E,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,SAAS;AACb,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,MAAM,GAAG,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK;AAC9C,OAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACrC,UAAM,KAAK,gBAAgB,CAAC;AAC5B,UAAM,KAAK,gBAAgB,IAAI,CAAC;AAChC,QAAI,OAAO,QAAW;AACpB,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AACrC,UAAI,QAAQ,QAAW;AACrB,aAAK;AACL,eAAO,KAAK,MAAM,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAI;AACtD,YAAI,OAAO,UAAU;AACnB,cAAI,IAAI,QAAQ;AACd,qBAAS;AACT,yBAAa;AACb,uBAAW;AAAA,UACb;AACA,cAAI;AACJ,kBAAQ,IAAI;AAAA,QACd;AAAA,MACF;AACA,YAAM;AACN,YAAM;AACN,YAAM;AAAA,IACR;AACA,SAAK;AACL,SAAK;AAAA,EACP;AACA,OAAK;AACL,SAAO,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,QAAQ;AACxD;;;ACpBO,IAAM,aAAa;AAAA,EACxB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,eAAe;AAAA,EACf,UAAU;AACZ;AAEA,IAAM,oBAAN,cAAgC,gBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO5C,YAAY,WAAW,WAAW,YAAY,YAAY;AACxD,UAAM,WAAW,WAAW,YAAY,UAAU;AAMlD,SAAK,UAAU;AAMf,SAAK,QAAQ;AAMb,SAAK,eAAe;AAMpB,SAAK,eAAe;AAMpB,SAAK,sBAAsB;AAM3B,SAAK,gBAAgB;AAMrB,SAAK,iBAAiB;AAKtB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,gBAAgB,IAAI,EAAC,WAAW,iBAAgB;AAMhE,SAAK,mBAAmB;AAKxB,SAAK,eAAe,CAAC;AAMrB,SAAK;AAAA,IAA8D,CAAC;AAKpE,SAAK,aAAa,CAAC;AAMnB,SAAK,WAAW;AAMhB,SAAK,WAAW;AAMhB,SAAK,aAAa;AAOlB,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,eAAe,MAAM,OAAO;AAClC,iBAAa,aAAa,KAAK;AAC/B,iBAAa,aAAa,KAAK;AAC/B,iBAAa,eAAe,KAAK;AACjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,UAAU,SAAS;AAC1B,UAAM,YAAY,KAAK;AACvB,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,KAAK,UAAU,MAAM,CAAC,aAAc,CAAC,aAAa,CAAC,aAAc;AACnE;AAAA,IACF;AAEA,UAAM,cAAc,KAAK;AACzB,QAAI,QAAQ,YAAY;AAExB,UAAM,eAAe,SAAS,QAAQ;AACtC,QAAI,kBAAkB;AACtB,QAAI,SAAS,SAAS,UAAU;AAEhC,QACE,UAAU,cAAc,WACvB,gBAAgB,gBACf,gBAAgB,qBAChB,gBAAgB,aAChB,gBAAgB,iBAClB;AACA,UAAI,CAAC,WAAW,KAAK,qBAAqB,GAAG,SAAS,UAAU,CAAC,GAAG;AAClE;AAAA,MACF;AACA,UAAI;AACJ,wBAAkB,SAAS,mBAAmB;AAC9C,UAAI,gBAAgB,cAAc;AAChC,eAAO,CAAC,gBAAgB,MAAM;AAAA,MAChC,WAAW,gBAAgB,mBAAmB;AAC5C;AAAA,QACE,SACA,QAAQ;AAAA,MACZ,WAAW,gBAAgB,WAAW;AACpC;AAAA,QAA+D,SAC5D,QAAQ,EACR,MAAM,GAAG,CAAC;AAAA,MACf,WAAW,gBAAgB,gBAAgB;AACzC,cAAM;AAAA;AAAA,UAEF,SACA,SAAS;AAAA;AACb,eAAO,CAAC;AACR,iBAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,eAAK,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,QACvB;AAAA,MACF;AACA,WAAK,cAAc,UAAU,OAAO;AACpC,YAAM,SAAS,UAAU;AACzB,YAAM,YAAY,SAAS,SAAY,UAAU;AAEjD,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAI;AACJ,YAAI,QAAQ;AACV,mBAAS;AAAA,YACP,SAAS,KAAK;AAAA,YACd;AAAA,YACA;AAAA,YACA,KAAK,CAAC;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,mBAAS,CAAC,gBAAgB,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC;AAAA,QACtD;AACA,iBAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,gBAAM,QAAQ,OAAO,CAAC;AACtB,cAAI,aAAa;AACjB,cAAI,WAAW,MAAM;AACrB,cAAI,aAAa,QAAW;AAC1B,kBAAM,QAAQ;AAAA,cACZ,UAAU;AAAA,cACV;AAAA,cACA;AAAA,cACA,MAAM;AAAA,cACN;AAAA,YACF;AACA,yBAAa,MAAM,CAAC;AACpB,uBAAW,MAAM,CAAC;AAAA,UACpB;AACA,mBAAS,IAAI,YAAY,IAAI,UAAU,KAAK,QAAQ;AAClD,wBAAY,KAAK,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,UACzC;AACA,gBAAM,MAAM,YAAY;AACxB,uBAAa,KAAK,CAAC;AACnB,eAAK,WAAW,OAAO,GAAG;AAC1B,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,UAAI,iBAAiB,UAAU,WAAW,OAAO,CAAC;AAClD,cAAQ,cAAc;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH;AAAA,UAEI,SACA,mBAAmB;AACvB;AAAA,QACF,KAAK;AACH;AAAA,UAEI,SACA,gBAAgB;AACpB;AAAA,QACF,KAAK;AACH;AAAA,UAEI,SACA,UAAU;AACd;AAAA,QACF,KAAK;AACH;AAAA,UAEI,SACA,iBAAiB;AACrB,mBAAS;AACT;AAAA,QACF,KAAK;AACH;AAAA,UAEI,SACA,qBAAqB;AACzB,cAAI,CAAC,UAAU,UAAU;AACvB,2BAAe,KAAK,gBAAgB,CAAC,IAAI,KAAK,UAAU;AAAA,UAC1D;AACA,mBAAS;AACT;AAAA,QACF,KAAK;AACH,gBAAM;AAAA;AAAA,YAEF,SACA,sBAAsB;AAAA;AAC1B,4BAAkB,CAAC;AACnB,mBAAS,IAAI,GAAG,KAAK,eAAe,QAAQ,IAAI,IAAI,KAAK,GAAG;AAC1D,gBAAI,CAAC,UAAU,UAAU;AACvB,6BAAe,KAAK,eAAe,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,YAC7D;AACA,4BAAgB,KAAK,eAAe,CAAC,GAAG,eAAe,IAAI,CAAC,CAAC;AAAA,UAC/D;AACA,cAAI,gBAAgB,WAAW,GAAG;AAChC;AAAA,UACF;AACA,mBAAS;AACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,MAAM,KAAK,2BAA2B,iBAAiB,MAAM;AACnE,UAAI,QAAQ,OAAO;AACjB;AAAA,MACF;AACA,UACE,mBACC,MAAM,SAAS,MAAM,gBAAgB,SAAS,QAC/C;AACA,YAAI,MAAM,QAAQ;AAClB,yBAAiB,eAAe,OAAO,CAAC,GAAG,MAAM;AAC/C,gBAAM,OACJ,aAAa,MAAM,KAAK,CAAC,MAAM,gBAAgB,IAAI,MAAM,KACzD,aAAa,MAAM,KAAK,IAAI,CAAC,MAAM,gBAAgB,IAAI,SAAS,CAAC;AACnE,cAAI,CAAC,MAAM;AACT,cAAE;AAAA,UACJ;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,WAAK,gBAAgB;AAErB,UAAI,UAAU,kBAAkB,UAAU,kBAAkB;AAC1D,aAAK;AAAA,UACH,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AACA,YAAI,UAAU,gBAAgB;AAC5B,eAAK,gBAAgB,KAAK,OAAO,KAAK,UAAU;AAAA,QAClD;AACA,YAAI,UAAU,kBAAkB;AAC9B,eAAK,kBAAkB,KAAK,OAAO,KAAK,WAAW;AACnD,eAAK,yBAAyB,KAAK,KAAK,aAAa,KAAK,KAAK,CAAC;AAAA,QAClE;AAAA,MACF;AAEA,WAAK,cAAc,UAAU,OAAO;AAGpC,UAAI,UAAU,UAAU;AACxB,UACE,WAAW,mBACV,UAAU,MAAM,CAAC,IAAI,KAAK,UAAU,MAAM,CAAC,IAAI,IAChD;AACA,YAAI,KAAK,UAAU,QAAQ,CAAC;AAC5B,YAAIC,MAAK,UAAU,QAAQ,CAAC;AAC5B,YAAIC,MAAK,UAAU,QAAQ,CAAC;AAC5B,YAAIC,MAAK,UAAU,QAAQ,CAAC;AAC5B,YAAI,UAAU,MAAM,CAAC,IAAI,GAAG;AAC1B,UAAAF,MAAK,CAACA;AACN,UAAAE,MAAK,CAACA;AAAA,QACR;AACA,YAAI,UAAU,MAAM,CAAC,IAAI,GAAG;AAC1B,eAAK,CAAC;AACN,UAAAD,MAAK,CAACA;AAAA,QACR;AACA,kBAAU,CAAC,IAAID,KAAIC,KAAIC,GAAE;AAAA,MAC3B;AAKA,YAAM,aAAa,KAAK;AACxB,WAAK,aAAa,KAAK;AAAA,QACrB,oBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,CAAC,GAAG,CAAC;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,WAAW,iBACP,iBACA,QAAQ,IAAI,SAAU,GAAG;AACvB,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,QACL,CAAC,CAAC,UAAU;AAAA,QACZ,CAAC,CAAC,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AACD,YAAM,QAAQ,IAAI;AAElB,YAAM,mBAAmB,KAAK,MAAM;AACpC,UAAI,UAAU,gBAAgB;AAC5B,aAAK,MAAM,YAAY;AACvB,aAAK,yBAAyB,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC;AAAA,MAChE;AACA,WAAK,yBAAyB,KAAK;AAAA,QACjC,oBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,CAAC,OAAO,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,CAAC,CAAC,UAAU;AAAA,QACZ,CAAC,CAAC,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,WAAW,mBAAmB,KAAK;AAAA,QACxC,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AAED,UAAI,UAAU,gBAAgB;AAC5B,aAAK,MAAM,YAAY;AACvB,aAAK,yBAAyB,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC;AAAA,MAChE;AAEA,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AAEvB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,UAAI,EAAE,aAAa,KAAK,eAAe;AACrC,aAAK,aAAa,SAAS,IAAI;AAAA,UAC7B,aAAa,YAAY;AAAA,UACzB,SAAS,YAAY;AAAA,UACrB,gBAAgB,YAAY;AAAA,UAC5B,WAAW,YAAY;AAAA,UACvB,UAAU,YAAY;AAAA,UACtB,YAAY,YAAY;AAAA,UACxB,UAAU,YAAY;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,EAAE,WAAW,KAAK,aAAa;AACjC,WAAK,WAAW,OAAO,IAAI;AAAA,QACzB,MAAM,UAAU;AAAA,QAChB,WAAW,UAAU,aAAa;AAAA,QAClC,SAAS,UAAU;AAAA,QACnB,cAAc,UAAU,gBAAgB;AAAA,QACxC,OAAO,UAAU;AAAA,MACnB;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW;AACb,UAAI,EAAE,WAAW,KAAK,aAAa;AACjC,aAAK,WAAW,OAAO,IAAI;AAAA,UACzB,WAAW,UAAU;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO,KAAK;AACrB,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AAEvB,UAAM,YAAY,KAAK;AACvB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK;AACrB,SAAK,gBAAgB;AAErB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,WAAW,UAAU,YAAY;AAElD,UAAM,UAAU,KAAK,eAAe;AACpC,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,cACf,YAAY,YAAY,KAAK,IAAI,UAAU,MAAM,CAAC,CAAC,IAAK,IACzD;AAEJ,SAAK,aAAa,KAAK;AAAA,MACrB,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,yBAAyB,KAAK;AAAA,MACjC,oBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,UAAU,mBAAmB;AAAA,MAC7B,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,WAAW,YAAY;AAClC,QAAI,WAAW,WAAW;AAC1B,QAAI,CAAC,WAAW;AACd,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,YAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAI,CAAC,eAAe;AAClB,oBAAY;AACZ,aAAK,iBAAiB;AAAA,MACxB,OAAO;AACL,oBAAY,KAAK;AACjB,YAAI,CAAC,WAAW;AACd;AAAA,UAA6D,CAAC;AAC9D,eAAK,iBAAiB;AAAA,QACxB;AACA,kBAAU,YAAY;AAAA,UACpB,cAAc,SAAS,KAAK;AAAA,QAC9B;AAAA,MACF;AAEA,YAAM,kBAAkB,UAAU,UAAU;AAC5C,UAAI,CAAC,iBAAiB;AACpB,sBAAc;AACd,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,sBAAc,KAAK;AACnB,YAAI,CAAC,aAAa;AAChB;AAAA,UAAiE,CAAC;AAClE,eAAK,mBAAmB;AAAA,QAC1B;AACA,cAAM,WAAW,gBAAgB,YAAY;AAC7C,cAAM,iBAAiB,gBAAgB,kBAAkB;AACzD,cAAM,YAAY,gBAAgB,SAAS;AAC3C,cAAM,aAAa,gBAAgB,cAAc;AACjD,oBAAY,UAAU,gBAAgB,WAAW,KAAK;AACtD,oBAAY,WAAW,WAAW,SAAS,MAAM,IAAI;AACrD,oBAAY,iBACV,mBAAmB,SAAY,wBAAwB;AACzD,oBAAY,WAAW,gBAAgB,YAAY,KAAK;AACxD,oBAAY,YACV,cAAc,SAAY,mBAAmB;AAC/C,oBAAY,aACV,eAAe,SAAY,oBAAoB;AACjD,oBAAY,cAAc;AAAA,UACxB,gBAAgB,SAAS,KAAK;AAAA,QAChC;AAAA,MACF;AAEA,kBAAY,KAAK;AACjB,YAAM,OAAO,UAAU,QAAQ,KAAK;AACpC,mBAAa,IAAI;AACjB,YAAM,YAAY,UAAU,cAAc;AAC1C,gBAAU,WAAW,UAAU,YAAY;AAC3C,gBAAU,OAAO;AACjB,gBAAU,WAAW,UAAU,YAAY;AAC3C,gBAAU,YAAY,UAAU,aAAa;AAC7C,gBAAU,YAAY,UAAU,aAAa;AAC7C,gBAAU,SAAS,UAAU,UAAU;AACvC,gBAAU,UAAU,UAAU,WAAW;AACzC,gBAAU,eACR,UAAU,gBAAgB,KAAK;AACjC,gBAAU,iBAAiB,UAAU,kBAAkB;AACvD,gBAAU,mBAAmB,UAAU,oBAAoB;AAC3D,gBAAU,UAAU,UAAU,WAAW,KAAK;AAC9C,gBAAU,QAAQ,cAAc,SAAY,CAAC,GAAG,CAAC,IAAI;AAErD,YAAM,cAAc,UAAU,WAAW;AACzC,YAAM,cAAc,UAAU,WAAW;AACzC,YAAM,qBAAqB,UAAU,kBAAkB;AACvD,YAAM,eAAe,UAAU,YAAY;AAC3C,WAAK,QAAQ,UAAU,QAAQ,KAAK;AACpC,WAAK,eAAe,gBAAgB,SAAY,IAAI;AACpD,WAAK,eAAe,gBAAgB,SAAY,IAAI;AACpD,WAAK,sBACH,uBAAuB,SAAY,QAAQ;AAC7C,WAAK,gBAAgB,iBAAiB,SAAY,IAAI;AAEtD,WAAK,aAAa,eACb,OAAO,YAAY,eAAe,WAC/B,YAAY,cACZ,OAAO,YAAY,WAAW,KAClC,YAAY,UACZ,YAAY,iBACZ,MACA,YAAY,YACZ,YAAY,WACZ,YAAY,aACZ,MACA,YAAY,SAAS,KAAK,IAC1B,MACA;AACJ,WAAK,WACH,UAAU,OACV,UAAU,SACT,UAAU,aAAa,QACvB,UAAU,UAAU,QACpB,UAAU,WAAW,QACrB,UAAU,gBAAgB;AAC7B,WAAK,WAAW,YACZ,OAAO,UAAU,aAAa,WAC5B,UAAU,YACV,MAAM,OAAO,UAAU,SAAS,IAClC;AAAA,IACN;AACA,SAAK,0BAA0B;AAAA,EACjC;AACF;AAEA,IAAO,sBAAQ;;;ACxoBf,IAAM,qBAAqB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW;AAAA,EACX,QAAQ;AACV;AAEA,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,YAAY,WAAW,WAAW,YAAY,YAAY;AAKxD,SAAK,aAAa;AAMlB,SAAK,aAAa;AAMlB,SAAK,cAAc;AAMnB,SAAK,cAAc;AAMnB,SAAK,oBAAoB,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,sBAAsB,CAAC;AAC7B,eAAW,QAAQ,KAAK,mBAAmB;AACzC,0BAAoB,IAAI,IAAI,oBAAoB,IAAI,KAAK,CAAC;AAC1D,YAAM,WAAW,KAAK,kBAAkB,IAAI;AAC5C,iBAAW,cAAc,UAAU;AACjC,cAAM,qBAAqB,SAAS,UAAU,EAAE,OAAO;AACvD,4BAAoB,IAAI,EAAE,UAAU,IAAI;AAAA,MAC1C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,QAAQ,aAAa;AAC9B,UAAM,YAAY,WAAW,SAAY,OAAO,SAAS,IAAI;AAC7D,QAAI,UAAU,KAAK,kBAAkB,SAAS;AAC9C,QAAI,YAAY,QAAW;AACzB,gBAAU,CAAC;AACX,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC;AACA,QAAI,SAAS,QAAQ,WAAW;AAChC,QAAI,WAAW,QAAW;AACxB,YAAM,cAAc,mBAAmB,WAAW;AAClD,eAAS,IAAI;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,cAAQ,WAAW,IAAI;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,uBAAQ;;;AClFR,SAAS,eACd,iBACA,QACA,KACA,QACA,MACA,QACA,UACA,OACAC,2BACA,MACA,OACA,UACA;AACA,MAAI,KAAK,gBAAgB,MAAM;AAC/B,MAAI,KAAK,gBAAgB,SAAS,CAAC;AACnC,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,gBAAgB;AACpB,MAAI,WAAW;AAEf,WAAS,UAAU;AACjB,SAAK;AACL,SAAK;AACL,cAAU;AACV,SAAK,gBAAgB,MAAM;AAC3B,SAAK,gBAAgB,SAAS,CAAC;AAC/B,gBAAY;AACZ,oBAAgB,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,GAAG;AAAA,EACzE;AACA,KAAG;AACD,YAAQ;AAAA,EACV,SAAS,SAAS,MAAM,UAAU,WAAW,gBAAgB;AAE7D,MAAI,cACF,kBAAkB,IAAI,KAAK,SAAS,YAAY;AAClD,QAAM,SAAS,KAAK,IAAI,IAAI,WAAW;AACvC,QAAM,SAAS,KAAK,IAAI,IAAI,WAAW;AAEvC,QAAM,cAAc,SAAS;AAC7B,QAAM,cAAc;AACpB,QAAM,OAAO,SAAS,QAAQA,0BAAyB,MAAM,MAAM,KAAK;AACxE,SAAO,SAAS,MAAM,UAAU,WAAW,gBAAgB,MAAM;AAC/D,YAAQ;AAAA,EACV;AACA,gBAAc,kBAAkB,IAAI,KAAK,OAAO,YAAY;AAC5D,QAAM,OAAO,KAAK,IAAI,IAAI,WAAW;AACrC,QAAM,OAAO,KAAK,IAAI,IAAI,WAAW;AAGrC,MAAI;AACJ,MAAI,UAAU;AACZ,UAAM,OAAO,CAAC,QAAQ,QAAQ,MAAM,IAAI;AACxC,WAAO,MAAM,GAAG,GAAG,GAAG,UAAU,MAAM,IAAI;AAC1C,cAAU,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,EAC5B,OAAO;AACL,cAAU,SAAS;AAAA,EACrB;AAEA,QAAM,KAAK,KAAK;AAChB,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,cAAc,WAAW;AAE/C,WAAS;AACT,kBAAgB;AAChB,aAAW;AACX,OAAK,gBAAgB,MAAM;AAC3B,OAAK,gBAAgB,SAAS,CAAC;AAE/B,MAAI;AAEJ,MAAI,eAAe;AACjB,YAAQ;AAER,oBAAgB,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AAC3C,QAAI,SAAS;AACX,uBAAiB,gBAAgB,IAAI,CAAC,KAAK;AAAA,IAC7C;AACA,UAAM,KAAK,OAAO,UAAU;AAC5B,UAAM,KAAK,OAAO,UAAU;AAC5B,WAAO,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,UAAU,GAAG,eAAe,IAAI;AAC3D,WAAO;AAAA,EACT;AAGA,SAAO,KAAK,QAAQ,OAAO,GAAG;AAE9B,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,MAAM;AAC1C,YAAQ;AACR,QAAI,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AACvC,QAAI,SAAS;AACX,eAAS,QAAQ,IAAI,CAAC,KAAK;AAAA,IAC7B;AACA,QAAI,kBAAkB,QAAW;AAC/B,UAAI,QAAQ,QAAQ;AACpB,eAAS,QAAQ,KAAK,KAAK,KAAK,QAAQ,CAAC,KAAK,IAAI,KAAK;AACvD,UAAI,KAAK,IAAI,KAAK,IAAI,UAAU;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,oBAAgB;AAEhB,UAAM,SAAS;AACf,QAAI,aAAa;AACjB,WAAO,IAAI,IAAI,EAAE,GAAG;AAClB,YAAM,QAAQ,UAAU,KAAK,IAAI,IAAI;AACrC,YAAM,MAAM,QAAQA,0BAAyB,MAAM,KAAK,KAAK,GAAG,KAAK;AACrE,UACE,SAAS,SAAS,OAClB,WAAW,gBAAgB,SAAS,aAAa,MAAM,GACvD;AACA;AAAA,MACF;AACA,oBAAc;AAAA,IAChB;AACA,QAAI,MAAM,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,QAAQ,UACV,KAAK,UAAU,KAAK,QAAQ,KAAK,CAAC,IAClC,KAAK,UAAU,QAAQ,CAAC;AAC5B,kBACE,kBAAkB,IACd,KACC,SAAS,aAAa,IAAI,YAAY;AAC7C,UAAM,IAAI,KAAK,IAAI,IAAI,WAAW;AAClC,UAAM,IAAI,KAAK,IAAI,IAAI,WAAW;AAClC,WAAO,KAAK,CAAC,GAAG,GAAG,aAAa,GAAG,OAAO,KAAK,CAAC;AAChD,cAAU;AAAA,EACZ;AACA,SAAO;AACT;;;AC9FA,IAAM,YAAY,YAAY;AAG9B,IAAM,KAAK,CAAC;AAEZ,IAAM,KAAK,CAAC;AAEZ,IAAM,KAAK,CAAC;AAEZ,IAAM,KAAK,CAAC;AAMZ,SAAS,gBAAgB,wBAAwB;AAC/C,SAAO,uBAAuB,CAAC,EAAE;AACnC;AAEA,IAAM,WAAW,IAAI;AAAA;AAAA,EAEnB,MACE,OAAO,aAAa,IAAO,IAAI,MAAM,OAAO,aAAa,IAAO,IAChE,OAAO,aAAa,KAAO,IAAI,MAAM,OAAO,aAAa,KAAO,IAChE,OAAO,aAAa,KAAO,IAAI,MAAM,OAAO,aAAa,KAAO,IAChE,OAAO,aAAa,KAAO,IAAI,MAAM,OAAO,aAAa,KAAO,IAChE,OAAO,aAAa,MAAO,IAAI,MAAM,OAAO,aAAa,MAAO,IAClE;AAAA;AAEF;AAOA,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,UAAU,SAAS;AACrB,YAAQ,SAAS,KAAK,IAAI,IAAI,UAAU;AAAA,EAC1C,WAAW,UAAU,OAAO;AAC1B,YAAQ,SAAS,KAAK,IAAI,IAAI,SAAS;AAAA,EACzC;AACA,SAAO,WAAW,KAAK;AACzB;AAQA,SAAS,iBAAiB,KAAK,MAAM,GAAG;AACtC,MAAI,IAAI,GAAG;AACT,QAAI,KAAK,MAAM,EAAE;AAAA,EACnB;AACA,MAAI,KAAK,MAAM,EAAE;AACjB,SAAO;AACT;AAEA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOb,YAAY,YAAY,YAAY,UAAU,cAAc;AAK1D,SAAK,WAAW;AAMhB,SAAK,aAAa;AAOlB,SAAK,aAAa;AAMlB,SAAK;AAML,SAAK,eAAe,aAAa;AAMjC,SAAK,cAAc,aAAa;AAMhC,SAAK,mBAAmB,CAAC;AAMzB,SAAK,qBAAqB,OAAgB;AAM1C,SAAK,2BAA2B,aAAa;AAM7C,SAAK,oBAAoB;AAMzB,SAAK,gBAAgB;AAKrB,SAAK,aAAa,aAAa,cAAc,CAAC;AAK9C,SAAK,eAAe,aAAa,gBAAgB,CAAC;AAKlD,SAAK,aAAa,aAAa,cAAc,CAAC;AAM9C,SAAK,UAAU,CAAC;AAMhB,SAAK,UAAU,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,MAAM,SAAS,SAAS,WAAW;AAC7C,UAAM,MAAM,OAAO,UAAU,UAAU;AACvC,QAAI,KAAK,QAAQ,GAAG,GAAG;AACrB,aAAO,KAAK,QAAQ,GAAG;AAAA,IACzB;AACA,UAAM,cAAc,YAAY,KAAK,aAAa,SAAS,IAAI;AAC/D,UAAM,YAAY,UAAU,KAAK,WAAW,OAAO,IAAI;AACvD,UAAM,YAAY,KAAK,WAAW,OAAO;AACzC,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ;AAAA,MACZ,UAAU,MAAM,CAAC,IAAI;AAAA,MACrB,UAAU,MAAM,CAAC,IAAI;AAAA,IACvB;AACA,UAAM,cAAc,MAAM,QAAQ,IAAI;AACtC,UAAM,QAAQ,UAAU,UACpB,WAAW,UAAU,OAAO,IAC5B;AAAA,MACE,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AAAA,MAChC,UAAU,aAAa;AAAA,IACzB;AACJ,UAAM,cACJ,aAAa,YAAY,YAAY,YAAY,YAAY;AAE/D,UAAM,SAAS,cACX,OACA,KAAK,MAAM,IAAI,EAAE,OAAO,kBAAkB,CAAC,CAAC;AAEhD,UAAM,EAAC,OAAO,QAAQ,QAAQ,SAAS,WAAU,IAAI;AAAA,MACnD;AAAA,MACA;AAAA,IACF;AACA,UAAM,cAAc,QAAQ;AAC5B,UAAM,sBAAsB,CAAC;AAE7B,UAAM,KAAK,cAAc,KAAK,MAAM,CAAC;AACrC,UAAM,KAAK,SAAS,eAAe,MAAM,CAAC;AAE1C,UAAM,QAAQ;AAAA,MACZ,OAAO,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,MAC1C,QAAQ,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG;AAClC,0BAAoB,KAAK,SAAS,KAAK;AAAA,IACzC;AACA,QAAI,WAAW;AACb,0BAAoB,KAAK,eAAe,YAAY,WAAW;AAC/D,0BAAoB,KAAK,aAAa,WAAW;AACjD,0BAAoB,KAAK,WAAW,YAAY,OAAO;AACvD,0BAAoB,KAAK,YAAY,YAAY,QAAQ;AACzD,0BAAoB,KAAK,cAAc,YAAY,UAAU;AAC7D,0BAAoB,KAAK,eAAe,CAAC,YAAY,QAAQ,CAAC;AAC9D,0BAAoB,KAAK,kBAAkB,YAAY,cAAc;AAAA,IACvE;AACA,QAAI,SAAS;AACX,0BAAoB,KAAK,aAAa,UAAU,SAAS;AAAA,IAC3D;AACA,wBAAoB,KAAK,gBAAgB,QAAQ;AACjD,wBAAoB,KAAK,aAAa,QAAQ;AAC9C,UAAM,YAAY,MAAM;AACxB,QAAI,IAAI,QAAQ,cAAc,YAAY;AAC1C,UAAM,qBAAqB,CAAC;AAC5B,UAAM,mBAAmB,CAAC;AAC1B,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,mBAAmB;AACvB,QAAI,iBAAiB;AACrB,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK,GAAG;AAClD,YAAMC,QAAO,OAAO,CAAC;AACrB,UAAIA,UAAS,MAAM;AACjB,sBAAc;AACd,qBAAa;AACb,YAAI,QAAQ,cAAc,YAAY;AACtC,UAAE;AACF;AAAA,MACF;AACA,YAAM,OAAO,OAAO,IAAI,CAAC,KAAK,UAAU;AACxC,UAAI,SAAS,cAAc;AACzB,YAAI,WAAW;AACb,6BAAmB,KAAK,QAAQ,IAAI;AAAA,QACtC;AACA,YAAI,SAAS;AACX,2BAAiB,KAAK,QAAQ,IAAI;AAAA,QACpC;AACA,uBAAe;AAAA,MACjB;AACA,mBAAa,KAAK,IAAI,YAAY,QAAQ,gBAAgB,CAAC;AAC3D,YAAM,iBAAiB;AAAA,QACrBA;AAAA,QACA,IACE,YAAY,OAAO,gBAAgB,IACnC,SAAS,OAAO,gBAAgB,IAAI,WAAW,cAAc;AAAA,QAC/D,OAAO,cAAc,cAAc;AAAA,MACrC;AACA,WAAK,OAAO,gBAAgB;AAC5B,UAAI,WAAW;AACb,2BAAmB,KAAK,cAAc,cAAc;AAAA,MACtD;AACA,UAAI,SAAS;AACX,yBAAiB,KAAK,YAAY,cAAc;AAAA,MAClD;AACA,QAAE;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,MAAM,qBAAqB,kBAAkB;AAClE,UAAM,UAAU,KAAK,MAAM,qBAAqB,gBAAgB;AAChE,SAAK,QAAQ,GAAG,IAAI;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,sBACE,SACAC,KACAC,KACAC,KACAC,KACAC,kBACAC,oBACA;AACA,YAAQ,UAAU;AAClB,YAAQ,OAAO,MAAM,SAASL,GAAE;AAChC,YAAQ,OAAO,MAAM,SAASC,GAAE;AAChC,YAAQ,OAAO,MAAM,SAASC,GAAE;AAChC,YAAQ,OAAO,MAAM,SAASC,GAAE;AAChC,YAAQ,OAAO,MAAM,SAASH,GAAE;AAChC,QAAII,kBAAiB;AACnB,WAAK;AAAA,MAAqCA,iBAAgB,CAAC;AAC3D,WAAK,MAAM,OAAO;AAAA,IACpB;AACA,QAAIC,oBAAmB;AACrB,WAAK;AAAA,QACH;AAAA;AAAA,QACyBA;AAAA,MAC3B;AACA,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,iCACE,YACA,aACA,SACA,SACA,OACA,QACA,SACA,SACA,SACA,SACA,UACA,OACA,aACA,SACA,YACA,SACA;AACA,eAAW,MAAM,CAAC;AAClB,eAAW,MAAM,CAAC;AAClB,QAAI,IAAI,UAAU;AAClB,QAAI,IAAI,UAAU;AAElB,UAAM,IAAI,QAAQ,UAAU,aAAa,aAAa,UAAU;AAChE,UAAM,IAAI,SAAS,UAAU,cAAc,cAAc,UAAU;AACnE,UAAM,OAAO,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,QAAQ,CAAC;AAClD,UAAM,OAAO,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,QAAQ,CAAC;AAClD,UAAM,OAAO,IAAI,QAAQ,CAAC;AAC1B,UAAM,OAAO,IAAI,QAAQ,CAAC;AAE1B,QAAI,cAAc,aAAa,GAAG;AAChC,SAAG,CAAC,IAAI;AACR,SAAG,CAAC,IAAI;AACR,SAAG,CAAC,IAAI;AACR,SAAG,CAAC,IAAI;AACR,SAAG,CAAC,IAAI,OAAO;AACf,SAAG,CAAC,IAAI,GAAG,CAAC;AACZ,SAAG,CAAC,IAAI,OAAO;AACf,SAAG,CAAC,IAAI,GAAG,CAAC;AAAA,IACd;AAEA,QAAI;AACJ,QAAI,aAAa,GAAG;AAClB,kBAAY;AAAA,QACV,OAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC;AAAA,QACD,CAAC;AAAA,MACH;AAEA,YAAe,WAAW,EAAE;AAC5B,YAAe,WAAW,EAAE;AAC5B,YAAe,WAAW,EAAE;AAC5B,YAAe,WAAW,EAAE;AAC5B;AAAA,QACE,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACnC,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACnC,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACnC,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACnC;AAAA,MACF;AAAA,IACF,OAAO;AACL;AAAA,QACE,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QAC1B,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QAC1B,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QAC1B,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa;AACf,UAAI,KAAK,MAAM,CAAC;AAChB,UAAI,KAAK,MAAM,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,QACZ,MAAM,UAAU,CAAC;AAAA,QACjB,MAAM,UAAU,CAAC;AAAA,QACjB,MAAM,UAAU,CAAC;AAAA,QACjB,MAAM,UAAU,CAAC;AAAA,QACjB,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,oBACE,SACA,cACA,cACA,YACA,SACAD,kBACAC,oBACA;AACA,UAAM,aAAa,CAAC,EAAED,oBAAmBC;AAEzC,UAAM,MAAM,WAAW;AACvB,UAAM,SAAS,QAAQ;AACvB,UAAM,gBAAgBA,qBACjBA,mBAAkB,CAAC,IAAI,WAAW,MAAM,CAAC,IAAK,IAC/C;AACJ,UAAMC,cACJ,IAAI,OAAO,iBAAiB,OAAO,QAAQ,gBAC3C,IAAI,OAAO,iBAAiB,KAC5B,IAAI,OAAO,iBAAiB,OAAO,SAAS,gBAC5C,IAAI,OAAO,iBAAiB;AAE9B,QAAIA,aAAY;AACd,UAAI,YAAY;AACd,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACyBF;AAAA;AAAA,UACAC;AAAA,QAC3B;AAAA,MACF;AACA;AAAA,QACE;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS;AACb,QAAI,KAAK,YAAY;AACnB,YAAM,SAAS,MAAe,KAAK,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC7D,YAAM,aAAa,MAAM,KAAK;AAC9B,cAAQ,KAAK;AACb,cAAQ,UAAU,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,IAAI,UAAU;AAChE,cAAQ,OAAO,KAAK,aAAa;AAAA,IACnC;AACA,YAAQ,KAAK;AACb,QAAI,KAAK,YAAY;AACnB,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,SAAS,aAAa;AACpC,YAAQ,aAAa;AAAA,IACoC,YAAY,CAAC;AACtE,YAAQ;AAAA,IAAmC,YAAY,CAAC;AACxD,YAAQ;AAAA,IAAwC,YAAY,CAAC;AAC7D,YAAQ;AAAA,IAA0C,YAAY,CAAC;AAC/D,YAAQ;AAAA,IAAoC,YAAY,CAAC;AACzD,YAAQ;AAAA,IAAwC,YAAY,CAAC;AAC7D,YAAQ;AAAA;AAAA,MAA0C,YAAY,CAAC;AAAA,IAAE;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,6BAA6B,MAAM,SAAS,WAAW,SAAS;AAC9D,UAAM,YAAY,KAAK,WAAW,OAAO;AAEzC,UAAM,QAAQ,KAAK,YAAY,MAAM,SAAS,SAAS,SAAS;AAEhE,UAAM,cAAc,KAAK,aAAa,SAAS;AAC/C,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ;AAAA,MACZ,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AAAA,MAChC,UAAU,aAAa;AAAA,IACzB;AACA,UAAM,WAAW,WAAW,UAAU,gBAAgB,mBAAmB;AACzE,UAAM,cACJ,eAAe,YAAY,YAAY,YAAY,YAAY;AAGjE,UAAM,QAAQ,MAAM,QAAQ,aAAa,IAAI,UAAU,MAAM,CAAC;AAC9D,UAAM,UAAU,QAAQ,QAAQ,KAAK,MAAM,SAAS;AACpD,UAAM,UACH,WAAW,MAAM,SAAU,aAC5B,KAAK,MAAM,YAAY;AAEzB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,SACE,SACA,cACA,WACA,cACA,aACA,iBACA,WACA,eACA;AAEA,QAAI;AACJ,QAAI,KAAK,qBAAqB,OAAO,WAAW,KAAK,kBAAkB,GAAG;AACxE,yBAAmB,KAAK;AAAA,IAC1B,OAAO;AACL,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,oBAAoB,CAAC;AAAA,MAC5B;AACA,yBAAmB;AAAA,QACjB,KAAK;AAAA,QACL;AAAA,QACA,KAAK,YAAY;AAAA,QACjB;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AACA,mBAAsB,KAAK,oBAAoB,SAAS;AAAA,IAC1D;AACA,QAAI,IAAI;AACR,UAAM,KAAK,aAAa;AACxB,QAAI,IAAI;AACR,QAAI;AACJ,QAAI,SACF,SACA,OACA,OACA,QACA,QACA,OACA,MACA,SACA,WACA;AACF,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,sBAAsB;AAC1B,QAAI,wBAAwB;AAC5B,UAAM,kBAAkB,KAAK;AAC7B,UAAM,eAAe,KAAK;AAC1B,UAAM,4BACJ,KAAK,MAAM,KAAK,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,IAAI;AAE/D,UAAM;AAAA;AAAA,MAAwD;AAAA,QAC5D;AAAA,QACA,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA;AAIA,UAAM,YACJ,KAAK,gBAAgB,gBAAgB,KAAK,WAAW,IAAI;AAC3D,QAA0D;AAC1D,QAAI,GAAG,GAAG;AACV,WAAO,IAAI,IAAI;AACb,YAAM,cAAc,aAAa,CAAC;AAClC,YAAM;AAAA;AAAA,QACJ,YAAY,CAAC;AAAA;AAEf,cAAQ,MAAM;AAAA,QACZ,KAAK,oBAAkB;AACrB;AAAA,UACE,YAAY,CAAC;AAEf,4BAAkB,YAAY,CAAC;AAC/B,cAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B;AAAA,YAA2B,YAAY,CAAC;AAAA,UAC1C,WACE,cAAc,UACd,CAAC,WAAW,WAAW,gBAAgB,UAAU,CAAC,GAClD;AACA;AAAA,YAA2B,YAAY,CAAC,IAAK;AAAA,UAC/C,OAAO;AACL,cAAE;AAAA,UACJ;AACA;AAAA,QACF,KAAK,oBAAkB;AACrB,cAAI,cAAc,WAAW;AAC3B,iBAAK,MAAM,OAAO;AAClB,0BAAc;AAAA,UAChB;AACA,cAAI,gBAAgB,WAAW;AAC7B,oBAAQ,OAAO;AACf,4BAAgB;AAAA,UAClB;AACA,cAAI,CAAC,eAAe,CAAC,eAAe;AAClC,oBAAQ,UAAU;AAClB,oBAAQ;AACR,oBAAQ;AAAA,UACV;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB;AAAA,UAA2B,YAAY,CAAC;AACxC,gBAAM,KAAK,iBAAiB,CAAC;AAC7B,gBAAM,KAAK,iBAAiB,IAAI,CAAC;AACjC,gBAAM,KAAK,iBAAiB,IAAI,CAAC;AACjC,gBAAM,KAAK,iBAAiB,IAAI,CAAC;AACjC,gBAAM,KAAK,KAAK;AAChB,gBAAM,KAAK,KAAK;AAChB,gBAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACrC,kBAAQ,OAAO,KAAK,GAAG,EAAE;AACzB,kBAAQ,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,kBAAQ,UAAU;AAClB,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB;AAAA,UAA2B,YAAY,CAAC;AACxC,eAAK,YAAY,CAAC;AAClB,gBAAM;AAAA;AAAA,YAEF,YAAY,CAAC;AAAA;AAEjB,gBAAM,WAAW,YAAY,CAAC;AAC9B,gBAAM,KAAK,YAAY,UAAU,IAAI,YAAY,CAAC,IAAI;AACtD,gBAAM,WAAW;AACjB,gBAAM,UAAU;AAChB,cAAI,EAAE,KAAK,kBAAkB;AAC3B,4BAAgB,CAAC,IAAI,CAAC;AAAA,UACxB;AACA,gBAAM,SAAS,gBAAgB,CAAC;AAChC,cAAI,IAAI;AACN,eAAG,kBAAkB,GAAG,IAAI,GAAG,MAAM;AAAA,UACvC,OAAO;AACL,mBAAO,CAAC,IAAI,iBAAiB,CAAC;AAC9B,mBAAO,CAAC,IAAI,iBAAiB,IAAI,CAAC;AAClC,mBAAO,SAAS;AAAA,UAClB;AACA,mBAAS,QAAQ,KAAK;AACtB,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB;AAAA,UAA2B,YAAY,CAAC;AACxC;AAAA,UAA4B,YAAY,CAAC;AACzC;AAAA,UAEI,YAAY,CAAC;AAIjB;AAAA,UAAiC,YAAY,CAAC;AAC9C;AAAA,UAAiC,YAAY,CAAC;AAC9C,cAAI;AAAA;AAAA,YAAgC,YAAY,CAAC;AAAA;AACjD,gBAAM;AAAA;AAAA,YAAiC,YAAY,CAAC;AAAA;AACpD,gBAAM;AAAA;AAAA,YAAiC,YAAY,CAAC;AAAA;AACpD,gBAAM;AAAA;AAAA,YAAiC,YAAY,CAAC;AAAA;AACpD,gBAAM;AAAA;AAAA,YAAyC,YAAY,EAAE;AAAA;AAC7D,cAAI;AAAA;AAAA,YAAkC,YAAY,EAAE;AAAA;AACpD,gBAAM;AAAA;AAAA,YACJ,YAAY,EAAE;AAAA;AAEhB,cAAI;AAAA;AAAA,YAA+B,YAAY,EAAE;AAAA;AACjD,gBAAM;AAAA;AAAA,YAEF,YAAY,EAAE;AAAA;AAElB,gBAAM;AAAA;AAAA,YAEF,YAAY,EAAE;AAAA;AAGlB,cAAI,CAAC,SAAS,YAAY,UAAU,IAAI;AAEtC;AAAA,YAA8B,YAAY,EAAE;AAC5C;AAAA,YAAiC,YAAY,EAAE;AAC/C;AAAA,YAAmC,YAAY,EAAE;AACjD;AAAA,YAAiC,YAAY,EAAE;AAC/C,kBAAM,kBAAkB,KAAK;AAAA,cAC3B;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,oBAAQ,gBAAgB;AACxB,wBAAY,CAAC,IAAI;AACjB,kBAAM;AAAA;AAAA,cAAqC,YAAY,EAAE;AAAA;AACzD,uBAAW,gBAAgB,UAAU,eAAe,KAAK;AACzD,wBAAY,CAAC,IAAI;AACjB,kBAAM;AAAA;AAAA,cAAqC,YAAY,EAAE;AAAA;AACzD,uBAAW,gBAAgB,UAAU,eAAe,KAAK;AACzD,wBAAY,CAAC,IAAI;AACjB,qBAAS,MAAM;AACf,wBAAY,CAAC,IAAI;AACjB,oBAAQ,MAAM;AACd,wBAAY,EAAE,IAAI;AAAA,UACpB;AAEA,cAAI;AACJ,cAAI,YAAY,SAAS,IAAI;AAC3B;AAAA,YAAwC,YAAY,EAAE;AAAA,UACxD;AAEA,cAAI,SAAS,gBAAgB;AAC7B,cAAI,YAAY,SAAS,IAAI;AAC3B;AAAA,YAAwC,YAAY,EAAE;AACtD;AAAA,YAAyC,YAAY,EAAE;AACvD;AAAA,YAA2C,YAAY,EAAE;AAAA,UAC3D,OAAO;AACL,sBAAU;AACV,6BAAiB;AACjB,+BAAmB;AAAA,UACrB;AAEA,cAAI,kBAAkB,2BAA2B;AAE/C,wBAAY;AAAA,UACd,WAAW,CAAC,kBAAkB,CAAC,2BAA2B;AAExD,wBAAY;AAAA,UACd;AACA,cAAI,aAAa;AACjB,iBAAO,IAAI,IAAI,KAAK,GAAG;AACrB,gBACE,kBACA,eAAe,YAAY,IAAI,QAAQ,KAAK,YAC5C;AACA;AAAA,YACF;AACA,kBAAM,aAAa,KAAK;AAAA,cACtB,MAAM;AAAA,cACN,MAAM;AAAA,cACN,iBAAiB,CAAC;AAAA,cAClB,iBAAiB,IAAI,CAAC;AAAA,cACtB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,kBAAkB;AAAA,cAClB;AAAA,YACF;AAEA,kBAAM,OAAO;AAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAAA,gBAC6B;AAAA,kBACzB;AAAA,cACJ;AAAA;AAAA,gBAC6B;AAAA,kBACzB;AAAA,YACN;AACA,gBAAI,eAAe;AACjB,kBAAI,kBAAkB,QAAQ;AAE5B;AAAA,cACF,WAAW,kBAAkB,YAAY;AAEvC,8BAAc,OAAO,WAAW,YAAY;AAC5C;AAAA,cACF,OAAO;AACL,oBAAI;AACJ,oBAAI;AACJ,oBAAI,wBAAwB;AAC1B,wBAAM,QAAQ,KAAK;AACnB,sBAAI,CAAC,uBAAuB,KAAK,GAAG;AAElC,2CAAuB,KAAK,IAAI;AAEhC;AAAA,kBACF;AACA,8BAAY,uBAAuB,KAAK;AACxC,yBAAO,uBAAuB,KAAK;AACnC,sCAAoB,gBAAgB,SAAS;AAC7C,sBAAI,cAAc,SAAS,iBAAiB,GAAG;AAC7C;AAAA,kBACF;AAAA,gBACF;AACA,oBAAI,cAAc,SAAS,WAAW,YAAY,GAAG;AACnD;AAAA,gBACF;AACA,oBAAI,WAAW;AAEb,gCAAc,OAAO,iBAAiB;AAEtC,uBAAK,oBAAoB,MAAM,MAAM,SAAS;AAAA,gBAChD;AACA,8BAAc,OAAO,WAAW,YAAY;AAAA,cAC9C;AAAA,YACF;AACA,iBAAK,oBAAoB,MAAM,MAAM,IAAI;AAAA,UAC3C;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,gBAAM;AAAA;AAAA,YAA+B,YAAY,CAAC;AAAA;AAClD,gBAAM;AAAA;AAAA,YAA6B,YAAY,CAAC;AAAA;AAChD,gBAAM;AAAA;AAAA,YAAkC,YAAY,CAAC;AAAA;AACrD,gBAAM;AAAA;AAAA,YAAkC,YAAY,CAAC;AAAA;AACrD;AAAA,UAAiC,YAAY,CAAC;AAC9C,gBAAM;AAAA;AAAA,YAAkC,YAAY,CAAC;AAAA;AACrD,gBAAM;AAAA;AAAA,YAA2C,YAAY,CAAC;AAAA;AAC9D,gBAAM;AAAA;AAAA,YAAiC,YAAY,CAAC;AAAA;AACpD;AAAA,UAAmC,YAAY,CAAC;AAChD,gBAAM;AAAA;AAAA,YAAqC,YAAY,EAAE;AAAA;AACzD;AAAA,UAA8B,YAAY,EAAE;AAC5C;AAAA,UAAiC,YAAY,EAAE;AAC/C,gBAAM,kBAAkB;AAAA;AAAA,YACC,YAAY,EAAE;AAAA;AAAA,YACd,YAAY,EAAE;AAAA,UACvC;AAEA,gBAAM,YAAY,KAAK,WAAW,OAAO;AACzC,gBAAM,OAAO,UAAU;AACvB,gBAAM,YAAY;AAAA,YAChB,UAAU,MAAM,CAAC,IAAI;AAAA,YACrB,UAAU,MAAM,CAAC,IAAI;AAAA,UACvB;AAEA,cAAI;AACJ,cAAI,QAAQ,KAAK,SAAS;AACxB,2BAAe,KAAK,QAAQ,IAAI;AAAA,UAClC,OAAO;AACL,2BAAe,CAAC;AAChB,iBAAK,QAAQ,IAAI,IAAI;AAAA,UACvB;AAEA,gBAAM,aAAa,iBAAiB,kBAAkB,OAAO,KAAK,CAAC;AACnE,gBAAM,aACJ,KAAK,IAAI,UAAU,CAAC,CAAC,IACrB,yBAAyB,MAAM,MAAM,YAAY;AACnD,cAAI,YAAY,cAAc,YAAY;AACxC,kBAAM,YAAY,KAAK,WAAW,OAAO,EAAE;AAC3C,kBAAM,UACH,aAAa,cAAc,oBAAoB,MAAM,SAAS;AACjE,kBAAM,QAAQ;AAAA,cACZ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,IAAI,UAAU,CAAC,CAAC;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA,4BAA4B,IAAI,KAAK;AAAA,YACvC;AACA,sBAAW,KAAI,OAAO;AAEpB,oBAAM,yBAAyB,CAAC;AAChC,kBAAI,GAAG,IAAI,OAAO,OAAO;AACzB,kBAAI,WAAW;AACb,qBAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC1C,yBAAO,MAAM,CAAC;AACd;AAAA,kBAA+B,KAAK,CAAC;AACrC,0BAAQ,KAAK,YAAY,OAAO,SAAS,IAAI,SAAS;AACtD;AAAA,kBACyB,KAAK,CAAC,KAC5B,UAAU,CAAC,IAAI,IAAI,CAAC,cAAc;AACrC,4BACE,WAAW,MAAM,UACf,MAAM,YAAY,IAAI,cAAc,UAAU,CAAC,IAC/C,UAAU,CAAC,IACb;AACF,wBAAM,aAAa,KAAK;AAAA,oBACtB,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,KAAK,CAAC;AAAA,oBACN,KAAK,CAAC;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,KAAK,CAAC;AAAA,oBACN;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AACA,sBACE,iBACA,cAAc,SAAS,WAAW,YAAY,GAC9C;AACA,0BAAM;AAAA,kBACR;AACA,yCAAuB,KAAK;AAAA,oBAC1B;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF;AACA,kBAAI,SAAS;AACX,qBAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC1C,yBAAO,MAAM,CAAC;AACd;AAAA,kBAA+B,KAAK,CAAC;AACrC,0BAAQ,KAAK,YAAY,OAAO,SAAS,SAAS,EAAE;AACpD;AAAA,kBAAiC,KAAK,CAAC;AACvC,4BAAU,WAAW,MAAM,SAAS;AACpC,wBAAM,aAAa,KAAK;AAAA,oBACtB,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,KAAK,CAAC;AAAA,oBACN,KAAK,CAAC;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,KAAK,CAAC;AAAA,oBACN;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AACA,sBACE,iBACA,cAAc,SAAS,WAAW,YAAY,GAC9C;AACA,0BAAM;AAAA,kBACR;AACA,yCAAuB,KAAK;AAAA,oBAC1B;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF;AACA,kBAAI,eAAe;AACjB,8BAAc,KAAK,uBAAuB,IAAI,eAAe,CAAC;AAAA,cAChE;AACA,uBAASE,KAAI,GAAGC,MAAK,uBAAuB,QAAQD,KAAIC,KAAI,EAAED,IAAG;AAC/D,qBAAK,oBAAoB,MAAM,MAAM,uBAAuBA,EAAC,CAAC;AAAA,cAChE;AAAA,YACF;AAAA,UACF;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,cAAI,oBAAoB,QAAW;AACjC;AAAA,YACE,YAAY,CAAC;AAEf,kBAAM,SAAS,gBAAgB,SAAS,eAAe;AACvD,gBAAI,QAAQ;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,cAAI,WAAW;AACb;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,OAAO;AAAA,UACpB;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB;AAAA,UAA2B,YAAY,CAAC;AACxC;AAAA,UAA4B,YAAY,CAAC;AACzC,cAAI,iBAAiB,CAAC;AACtB,cAAI,iBAAiB,IAAI,CAAC;AAC1B,mBAAU,IAAI,MAAO;AACrB,mBAAU,IAAI,MAAO;AACrB,cAAI,WAAW,SAAS,WAAW,OAAO;AACxC,oBAAQ,OAAO,GAAG,CAAC;AACnB,oBAAQ;AACR,oBAAQ;AAAA,UACV;AACA,eAAK,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG;AAC3B,gBAAI,iBAAiB,CAAC;AACtB,gBAAI,iBAAiB,IAAI,CAAC;AAC1B,qBAAU,IAAI,MAAO;AACrB,qBAAU,IAAI,MAAO;AACrB,gBAAI,KAAK,KAAK,KAAK,WAAW,SAAS,WAAW,OAAO;AACvD,sBAAQ,OAAO,GAAG,CAAC;AACnB,sBAAQ;AACR,sBAAQ;AAAA,YACV;AAAA,UACF;AACA,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,gCAAsB;AACtB,eAAK,aAAa,YAAY,CAAC;AAE/B,cAAI,aAAa;AACf,iBAAK,MAAM,OAAO;AAClB,0BAAc;AACd,gBAAI,eAAe;AACjB,sBAAQ,OAAO;AACf,8BAAgB;AAAA,YAClB;AAAA,UACF;AAEA,kBAAQ;AAAA,UAEJ,YAAY,CAAC;AAEjB,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,kCAAwB;AACxB,cAAI,eAAe;AACjB,oBAAQ,OAAO;AACf,4BAAgB;AAAA,UAClB;AACA,eAAK;AAAA,YAAgB;AAAA;AAAA,YAAkC;AAAA,UAAY;AACnE,YAAE;AACF;AAAA,QACF,KAAK,oBAAkB;AACrB,cAAI,WAAW;AACb;AAAA,UACF,OAAO;AACL,oBAAQ,OAAO;AAAA,UACjB;AACA,YAAE;AACF;AAAA,QACF;AACE,YAAE;AACF;AAAA,MACJ;AAAA,IACF;AACA,QAAI,aAAa;AACf,WAAK,MAAM,OAAO;AAAA,IACpB;AACA,QAAI,eAAe;AACjB,cAAQ,OAAO;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QACE,SACA,cACA,WACA,cACA,aACA,eACA;AACA,SAAK,gBAAgB;AACrB,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,oBACE,SACA,WACA,cACA,iBACA,WACA;AACA,SAAK,gBAAgB;AACrB,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,mBAAQ;;;ACvtCf,IAAM,QAAQ,CAAC,WAAW,UAAU,cAAc,SAAS,QAAQ,SAAS;AAE5E,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAalB,YACE,WACA,YACA,YACA,UACA,iBACA,cACA;AAKA,SAAK,aAAa;AAMlB,SAAK,YAAY;AAMjB,SAAK,cAAc;AAMnB,SAAK,cAAc;AAMnB,SAAK,gBAAgB;AAMrB,SAAK,qBAAqB,CAAC;AAM3B,SAAK,uBAAuB;AAM5B,SAAK,yBAAyB,OAAgB;AAE9C,SAAK,iBAAiB,eAAe;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAS,WAAW;AACvB,UAAM,iBAAiB,KAAK,cAAc,SAAS;AACnD,YAAQ,UAAU;AAClB,YAAQ,OAAO,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AACnD,YAAQ,OAAO,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AACnD,YAAQ,OAAO,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AACnD,YAAQ,OAAO,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;AACnD,YAAQ,KAAK;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,iBAAiB;AAChC,eAAW,UAAU,iBAAiB;AACpC,UAAI,YAAY,KAAK,mBAAmB,MAAM;AAC9C,UAAI,cAAc,QAAW;AAC3B,oBAAY,CAAC;AACb,aAAK,mBAAmB,MAAM,IAAI;AAAA,MACpC;AACA,YAAM,sBAAsB,gBAAgB,MAAM;AAClD,iBAAW,eAAe,qBAAqB;AAC7C,cAAM,eAAe,oBAAoB,WAAW;AACpD,kBAAU,WAAW,IAAI,IAAI;AAAA,UAC3B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,WAAW;AACtB,eAAW,UAAU,KAAK,oBAAoB;AAC5C,YAAM,aAAa,KAAK,mBAAmB,MAAM;AACjD,eAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,YAAI,UAAU,CAAC,KAAK,YAAY;AAC9B,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,2BACE,YACA,YACA,UACA,cACA,UACA,qBACA;AACA,mBAAe,KAAK,MAAM,YAAY;AACtC,UAAM,cAAc,eAAe,IAAI;AACvC,UAAM,YAAY;AAAA,MAChB,KAAK;AAAA,MACL,eAAe;AAAA,MACf,eAAe;AAAA,MACf,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,CAAC;AAAA,MACD,CAAC,WAAW,CAAC;AAAA,MACb,CAAC,WAAW,CAAC;AAAA,IACf;AAEA,UAAM,aAAa,CAAC,KAAK;AACzB,QAAI,YAAY;AACd,WAAK,uBAAuB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA,EAAC,oBAAoB,KAAI;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AAErB,QACE,QAAQ,OAAO,UAAU,eACzB,QAAQ,OAAO,WAAW,aAC1B;AACA,cAAQ,OAAO,QAAQ;AACvB,cAAQ,OAAO,SAAS;AAAA,IAC1B,WAAW,CAAC,YAAY;AACtB,cAAQ,UAAU,GAAG,GAAG,aAAa,WAAW;AAAA,IAClD;AAKA,QAAI;AACJ,QAAI,KAAK,kBAAkB,QAAW;AACpC,kBAAY,YAAY;AACxB,uBAAiB,WAAW,UAAU;AACtC;AAAA,QACE;AAAA,QACA,cAAc,KAAK,gBAAgB;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAU,mBAAmB,YAAY;AAE/C,QAAI;AAOJ,aAAS,gBAAgB,SAAS,UAAU;AAC1C,YAAM,YAAY,QAAQ;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE;AACF,eAASE,KAAI,GAAG,KAAK,QAAQ,QAAQA,KAAI,IAAIA,MAAK;AAChD,YAAI,UAAU,QAAQA,EAAC,CAAC,IAAI,GAAG;AAC7B,cACE,CAAC,uBACA,gBAAgB,WAAW,gBAAgB,UAC5C,oBAAoB,SAAS,OAAO,GACpC;AACA,kBAAM,OAAO,QAAQA,EAAC,IAAI,KAAK;AAC/B,kBAAM,IAAI,eAAgB,MAAM;AAChC,kBAAM,IAAI,gBAAiB,MAAM,cAAe;AAChD,kBAAMC,UAAS,SAAS,SAAS,UAAU,IAAI,IAAI,IAAI,CAAC;AACxD,gBAAIA,SAAQ;AACV,qBAAOA;AAAA,YACT;AAAA,UACF;AACA,kBAAQ,UAAU,GAAG,GAAG,aAAa,WAAW;AAChD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,UAAM,KAAK,OAAO,KAAK,KAAK,kBAAkB,EAAE,IAAI,MAAM;AAC1D,OAAG,KAAK,SAAS;AAEjB,QAAI,GAAG,GAAG,WAAW,UAAU;AAC/B,SAAK,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACnC,YAAM,YAAY,GAAG,CAAC,EAAE,SAAS;AACjC,kBAAY,KAAK,mBAAmB,SAAS;AAC7C,WAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACtC,sBAAc,MAAM,CAAC;AACrB,mBAAW,UAAU,WAAW;AAChC,YAAI,aAAa,QAAW;AAC1B,mBAAS,SAAS;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,QAAQ;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,WAAW;AACvB,UAAM,YAAY,KAAK;AACvB,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,UAAM,OAAO,UAAU,CAAC;AACxB,UAAM,OAAO,UAAU,CAAC;AACxB,UAAM,OAAO,UAAU,CAAC;AACxB,UAAM,OAAO,UAAU,CAAC;AACxB,UAAM,iBAAiB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACtE,gBAAY,gBAAgB,GAAG,GAAG,GAAG,WAAW,cAAc;AAC9D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,QAAQ,KAAK,kBAAkB;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QACE,SACA,cACA,WACA,cACA,aACA,cACA,eACA;AAEA,UAAM,KAAK,OAAO,KAAK,KAAK,kBAAkB,EAAE,IAAI,MAAM;AAC1D,OAAG,KAAK,SAAS;AAIjB,QAAI,KAAK,YAAY;AACnB,cAAQ,KAAK;AACb,WAAK,KAAK,SAAS,SAAS;AAAA,IAC9B;AAEA,mBAAe,eAAe,eAAe;AAC7C,QAAI,GAAG,IAAI,GAAG,IAAI,SAAS;AAC3B,QAAI,eAAe;AACjB,SAAG,QAAQ;AAAA,IACb;AACA,SAAK,IAAI,GAAG,KAAK,GAAG,QAAQ,IAAI,IAAI,EAAE,GAAG;AACvC,YAAM,YAAY,GAAG,CAAC,EAAE,SAAS;AACjC,gBAAU,KAAK,mBAAmB,SAAS;AAC3C,WAAK,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,cAAc,aAAa,CAAC;AAClC,iBAAS,QAAQ,WAAW;AAC5B,YAAI,WAAW,QAAW;AACxB,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,YAAY;AACnB,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AACF;AAQA,IAAM,6BAA6B,CAAC;AAS7B,SAAS,mBAAmB,QAAQ;AACzC,MAAI,2BAA2B,MAAM,MAAM,QAAW;AACpD,WAAO,2BAA2B,MAAM;AAAA,EAC1C;AAEA,QAAM,OAAO,SAAS,IAAI;AAC1B,QAAM,gBAAgB,SAAS;AAC/B,QAAM,YAAY,IAAI,MAAM,gBAAgB,CAAC;AAC7C,WAAS,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG;AAChC,aAAS,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG;AAChC,YAAM,aAAa,IAAI,IAAI,IAAI;AAC/B,UAAI,aAAa,eAAe;AAC9B;AAAA,MACF;AACA,UAAI,WAAW,UAAU,UAAU;AACnC,UAAI,CAAC,UAAU;AACb,mBAAW,CAAC;AACZ,kBAAU,UAAU,IAAI;AAAA,MAC1B;AACA,eAAS,OAAO,SAAS,KAAK,QAAQ,SAAS,MAAM,IAAI,CAAC;AAC1D,UAAI,IAAI,GAAG;AACT,iBAAS,OAAO,SAAS,KAAK,QAAQ,SAAS,MAAM,IAAI,CAAC;AAAA,MAC5D;AACA,UAAI,IAAI,GAAG;AACT,iBAAS,OAAO,SAAS,KAAK,QAAQ,SAAS,MAAM,IAAI,CAAC;AAC1D,YAAI,IAAI,GAAG;AACT,mBAAS,OAAO,SAAS,KAAK,QAAQ,SAAS,MAAM,IAAI,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa,CAAC;AACpB,WAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,QAAI,UAAU,CAAC,GAAG;AAChB,iBAAW,KAAK,GAAG,UAAU,CAAC,CAAC;AAAA,IACjC;AAAA,EACF;AAEA,6BAA2B,MAAM,IAAI;AACrC,SAAO;AACT;AAEA,IAAO,wBAAQ;;;AC9Xf,IAAM,0BAAN,cAAsC,sBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlD,YACE,SACA,YACA,QACA,WACA,cACA,kBACA,eACA;AACA,UAAM;AAMN,SAAK,WAAW;AAMhB,SAAK,cAAc;AAMnB,SAAK,UAAU;AAMf,SAAK,aAAa;AAMlB,SAAK,qBAAqB,YACtB,QAAQ,KAAK,MAAM,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,IAClD;AAMJ,SAAK,gBAAgB;AAMrB,SAAK,oBAAoB;AAMzB,SAAK,iBAAiB;AAMtB,SAAK,oBAAoB;AAMzB,SAAK,sBAAsB;AAM3B,SAAK,oBAAoB;AAMzB,SAAK,aAAa;AAMlB,SAAK,eAAe;AAMpB,SAAK,SAAS;AAMd,SAAK,gBAAgB;AAMrB,SAAK,gBAAgB;AAMrB,SAAK,eAAe;AAMpB,SAAK,gBAAgB;AAMrB,SAAK,gBAAgB;AAMrB,SAAK,gBAAgB;AAMrB,SAAK,uBAAuB;AAM5B,SAAK,iBAAiB;AAMtB,SAAK,cAAc,CAAC,GAAG,CAAC;AAMxB,SAAK,cAAc;AAMnB,SAAK,QAAQ;AAMb,SAAK,eAAe;AAMpB,SAAK,eAAe;AAMpB,SAAK,sBAAsB;AAM3B,SAAK,gBAAgB;AAMrB,SAAK,aAAa,CAAC,GAAG,CAAC;AAMvB,SAAK,iBAAiB;AAMtB,SAAK,mBAAmB;AAMxB,SAAK,aAAa;AAMlB,SAAK,oBAAoB,CAAC;AAM1B,SAAK,qBAAqB,OAAgB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,iBAAiB,QAAQ,KAAK,QAAQ;AAChD,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,iBAAiB,KAAK;AAC5B,UAAM,QAAQ,QAAQ;AACtB,QAAI,KAAK,iBAAiB,GAAG;AAC3B,cAAQ,cAAc,QAAQ,KAAK;AAAA,IACrC;AACA,QAAI,WAAW,KAAK;AACpB,QAAI,KAAK,uBAAuB,GAAG;AACjC,kBAAY,KAAK;AAAA,IACnB;AACA,QAAI,KAAK,sBAAsB;AAC7B,kBAAY,KAAK;AAAA,IACnB;AACA,aAAS,IAAI,GAAG,KAAK,iBAAiB,QAAQ,IAAI,IAAI,KAAK,GAAG;AAC5D,YAAM,IAAI,iBAAiB,CAAC,IAAI,KAAK;AACrC,YAAM,IAAI,iBAAiB,IAAI,CAAC,IAAI,KAAK;AACzC,UACE,aAAa,KACb,KAAK,YAAY,CAAC,KAAK,KACvB,KAAK,YAAY,CAAC,KAAK,GACvB;AACA,cAAM,UAAU,IAAI,KAAK;AACzB,cAAM,UAAU,IAAI,KAAK;AACzB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAC;AAAA,UACD,CAAC;AAAA,QACH;AACA,gBAAQ,aAAa,MAAM,SAAS,cAAc;AAClD,gBAAQ,UAAU,SAAS,OAAO;AAClC,gBAAQ,MAAM,KAAK,YAAY,CAAC,GAAG,KAAK,YAAY,CAAC,CAAC;AACtD,gBAAQ;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,CAAC,KAAK;AAAA,UACN,CAAC,KAAK;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AACA,gBAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MACvC,OAAO;AACL,gBAAQ;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,iBAAiB,GAAG;AAC3B,cAAQ,cAAc;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,iBAAiB,QAAQ,KAAK,QAAQ;AAC9C,QAAI,CAAC,KAAK,cAAc,KAAK,UAAU,IAAI;AACzC;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,qBAAqB,KAAK,cAAc;AAAA,IAC/C;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,uBAAuB,KAAK,gBAAgB;AAAA,IACnD;AACA,SAAK,qBAAqB,KAAK,UAAU;AACzC,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,KAAK;AACpB,QAAI,KAAK,uBAAuB,GAAG;AACjC,kBAAY,KAAK;AAAA,IACnB;AACA,QAAI,KAAK,qBAAqB;AAC5B,kBAAY,KAAK;AAAA,IACnB;AACA,WAAO,SAAS,KAAK,UAAU,QAAQ;AACrC,YAAM,IAAI,iBAAiB,MAAM,IAAI,KAAK;AAC1C,YAAM,IAAI,iBAAiB,SAAS,CAAC,IAAI,KAAK;AAC9C,UACE,aAAa,KACb,KAAK,WAAW,CAAC,KAAK,KACtB,KAAK,WAAW,CAAC,KAAK,GACtB;AACA,gBAAQ,UAAU,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY;AAC9D,gBAAQ,OAAO,QAAQ;AACvB,gBAAQ,UAAU,KAAK,cAAc,KAAK,YAAY;AACtD,gBAAQ,MAAM,KAAK,WAAW,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC;AACpD,YAAI,KAAK,kBAAkB;AACzB,kBAAQ,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,QACrC;AACA,YAAI,KAAK,gBAAgB;AACvB,kBAAQ,SAAS,KAAK,OAAO,GAAG,CAAC;AAAA,QACnC;AACA,gBAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MACvC,OAAO;AACL,YAAI,KAAK,kBAAkB;AACzB,kBAAQ,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,QACrC;AACA,YAAI,KAAK,gBAAgB;AACvB,kBAAQ,SAAS,KAAK,OAAO,GAAG,CAAC;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cAAc,iBAAiB,QAAQ,KAAK,QAAQ,OAAO;AACzD,UAAM,UAAU,KAAK;AACrB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,YAAQ,OAAO,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,CAAC;AACvD,QAAI,SAAS,iBAAiB;AAC9B,QAAI,OAAO;AACT,gBAAU;AAAA,IACZ;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,cAAQ,OAAO,iBAAiB,CAAC,GAAG,iBAAiB,IAAI,CAAC,CAAC;AAAA,IAC7D;AACA,QAAI,OAAO;AACT,cAAQ,UAAU;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,iBAAiB,QAAQ,MAAM,QAAQ;AAChD,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAS,KAAK;AAAA,QACZ;AAAA,QACA;AAAA,QACA,KAAK,CAAC;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,UAAU;AACnB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,QAAI,CAAC,WAAW,KAAK,SAAS,SAAS,UAAU,CAAC,GAAG;AACnD;AAAA,IACF;AACA,QAAI,KAAK,cAAc,KAAK,cAAc;AACxC,UAAI,KAAK,YAAY;AACnB,aAAK,qBAAqB,KAAK,UAAU;AAAA,MAC3C;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,uBAAuB,KAAK,YAAY;AAAA,MAC/C;AACA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,YAAM,KAAK,iBAAiB,CAAC,IAAI,iBAAiB,CAAC;AACnD,YAAM,KAAK,iBAAiB,CAAC,IAAI,iBAAiB,CAAC;AACnD,YAAM,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC1C,YAAM,UAAU,KAAK;AACrB,cAAQ,UAAU;AAClB,cAAQ;AAAA,QACN,iBAAiB,CAAC;AAAA,QAClB,iBAAiB,CAAC;AAAA,QAClB;AAAA,QACA;AAAA,QACA,IAAI,KAAK;AAAA,MACX;AACA,UAAI,KAAK,YAAY;AACnB,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,KAAK,cAAc;AACrB,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,WAAK,UAAU,SAAS,UAAU,GAAG,GAAG,GAAG,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,OAAO;AACd,SAAK,mBAAmB,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC;AAC1D,SAAK,cAAc,MAAM,SAAS,CAAC;AACnC,SAAK,aAAa,MAAM,QAAQ,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,WAAW;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,UAAU;AACrB,UAAM,OAAO,SAAS,QAAQ;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,aAAK;AAAA;AAAA,UACmD;AAAA,QACxD;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UACwD;AAAA,QAC7D;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UACqD;AAAA,QAC1D;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UACwD;AAAA,QAC7D;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UAED;AAAA,QAEJ;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UAC0D;AAAA,QAC/D;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UAED;AAAA,QAEJ;AACA;AAAA,MACF,KAAK;AACH,aAAK;AAAA;AAAA,UACoD;AAAA,QACzD;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,YAAY,SAAS,OAAO;AAC1B,UAAM,WAAW,MAAM,oBAAoB,EAAE,OAAO;AACpD,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,SAAK,SAAS,KAAK;AACnB,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,SAAS,mBAAmB;AAC/C,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,WAAK,aAAa,WAAW,CAAC,CAAC;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,UAAU;AAClB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,UAAM,kBAAkB,SAAS,mBAAmB;AACpD,UAAM,SAAS,SAAS,UAAU;AAClC,QAAI,KAAK,QAAQ;AACf,WAAK,YAAY,iBAAiB,GAAG,gBAAgB,QAAQ,MAAM;AAAA,IACrE;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,WAAK,UAAU,iBAAiB,GAAG,gBAAgB,QAAQ,MAAM;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,UAAU;AACvB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,UAAM,kBAAkB,SAAS,mBAAmB;AACpD,UAAM,SAAS,SAAS,UAAU;AAClC,QAAI,KAAK,QAAQ;AACf,WAAK,YAAY,iBAAiB,GAAG,gBAAgB,QAAQ,MAAM;AAAA,IACrE;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,WAAK,UAAU,iBAAiB,GAAG,gBAAgB,QAAQ,MAAM;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,UAAU;AACvB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,QAAI,CAAC,WAAW,KAAK,SAAS,SAAS,UAAU,CAAC,GAAG;AACnD;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,uBAAuB,KAAK,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,kBAAkB,SAAS,mBAAmB;AACpD,cAAQ,UAAU;AAClB,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB;AAAA,MACF;AACA,cAAQ,OAAO;AAAA,IACjB;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,YAAM,eAAe,SAAS,gBAAgB;AAC9C,WAAK,UAAU,cAAc,GAAG,GAAG,CAAC;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,UAAU;AAC5B,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MAEI,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEN;AACA,UAAM,iBAAiB,SAAS,UAAU;AAC1C,QAAI,CAAC,WAAW,KAAK,SAAS,cAAc,GAAG;AAC7C;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,uBAAuB,KAAK,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,kBAAkB,SAAS,mBAAmB;AACpD,UAAI,SAAS;AACb,YAAM;AAAA;AAAA,QAAqC,SAAS,QAAQ;AAAA;AAC5D,YAAM,SAAS,SAAS,UAAU;AAClC,cAAQ,UAAU;AAClB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,iBAAS,KAAK;AAAA,UACZ;AAAA,UACA;AAAA,UACA,KAAK,CAAC;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,cAAQ,OAAO;AAAA,IACjB;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,YAAM,gBAAgB,SAAS,iBAAiB;AAChD,WAAK,UAAU,eAAe,GAAG,cAAc,QAAQ,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,QAAI,CAAC,WAAW,KAAK,SAAS,SAAS,UAAU,CAAC,GAAG;AACnD;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,UAAI,KAAK,YAAY;AACnB,aAAK,qBAAqB,KAAK,UAAU;AAAA,MAC3C;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,uBAAuB,KAAK,YAAY;AAAA,MAC/C;AACA,YAAM,UAAU,KAAK;AACrB,cAAQ,UAAU;AAClB,WAAK;AAAA,QACH,SAAS,2BAA2B;AAAA,QACpC;AAAA;AAAA,QAC8B,SAAS,QAAQ;AAAA,QAC/C,SAAS,UAAU;AAAA,MACrB;AACA,UAAI,KAAK,YAAY;AACnB,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,KAAK,cAAc;AACrB,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,YAAM,oBAAoB,SAAS,qBAAqB;AACxD,WAAK,UAAU,mBAAmB,GAAG,GAAG,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,UAAU;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,MACE,SAAS;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IAEJ;AACA,QAAI,CAAC,WAAW,KAAK,SAAS,SAAS,UAAU,CAAC,GAAG;AACnD;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,UAAI,KAAK,YAAY;AACnB,aAAK,qBAAqB,KAAK,UAAU;AAAA,MAC3C;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,uBAAuB,KAAK,YAAY;AAAA,MAC/C;AACA,YAAM,UAAU,KAAK;AACrB,YAAM,kBAAkB,SAAS,2BAA2B;AAC5D,UAAI,SAAS;AACb,YAAM,QAAQ,SAAS,SAAS;AAChC,YAAM,SAAS,SAAS,UAAU;AAClC,cAAQ,UAAU;AAClB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,cAAM,OAAO,MAAM,CAAC;AACpB,iBAAS,KAAK,WAAW,iBAAiB,QAAQ,MAAM,MAAM;AAAA,MAChE;AACA,UAAI,KAAK,YAAY;AACnB,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,KAAK,cAAc;AACrB,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,QAAI,KAAK,UAAU,IAAI;AACrB,YAAM,qBAAqB,SAAS,sBAAsB;AAC1D,WAAK,UAAU,oBAAoB,GAAG,mBAAmB,QAAQ,CAAC;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAW;AAC9B,UAAM,UAAU,KAAK;AACrB,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB,cAAQ,YAAY,UAAU;AAC9B,WAAK,oBAAoB;AAAA,QACvB,WAAW,UAAU;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,iBAAiB,aAAa,UAAU,WAAW;AACrD,yBAAiB,YAAY,UAAU;AACvC,gBAAQ,YAAY,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,aAAa;AAClC,UAAM,UAAU,KAAK;AACrB,UAAM,qBAAqB,KAAK;AAChC,QAAI,CAAC,oBAAoB;AACvB,cAAQ,UAAU,YAAY;AAC9B,cAAQ,YAAY,YAAY,QAAQ;AACxC,cAAQ,iBAAiB,YAAY;AACrC,cAAQ,WAAW,YAAY;AAC/B,cAAQ,YAAY,YAAY;AAChC,cAAQ,aAAa,YAAY;AACjC,cAAQ,cAAc,YAAY;AAClC,WAAK,sBAAsB;AAAA,QACzB,SAAS,YAAY;AAAA,QACrB,UAAU,YAAY;AAAA,QACtB,gBAAgB,YAAY;AAAA,QAC5B,UAAU,YAAY;AAAA,QACtB,WAAW,YAAY;AAAA,QACvB,YAAY,YAAY;AAAA,QACxB,aAAa,YAAY;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,UAAI,mBAAmB,WAAW,YAAY,SAAS;AACrD,2BAAmB,UAAU,YAAY;AACzC,gBAAQ,UAAU,YAAY;AAAA,MAChC;AACA,UAAI,CAAC,OAAO,mBAAmB,UAAU,YAAY,QAAQ,GAAG;AAC9D,gBAAQ;AAAA,UACL,mBAAmB,WAAW,YAAY;AAAA,QAC7C;AAAA,MACF;AACA,UAAI,mBAAmB,kBAAkB,YAAY,gBAAgB;AACnE,2BAAmB,iBAAiB,YAAY;AAChD,gBAAQ,iBAAiB,YAAY;AAAA,MACvC;AACA,UAAI,mBAAmB,YAAY,YAAY,UAAU;AACvD,2BAAmB,WAAW,YAAY;AAC1C,gBAAQ,WAAW,YAAY;AAAA,MACjC;AACA,UAAI,mBAAmB,aAAa,YAAY,WAAW;AACzD,2BAAmB,YAAY,YAAY;AAC3C,gBAAQ,YAAY,YAAY;AAAA,MAClC;AACA,UAAI,mBAAmB,cAAc,YAAY,YAAY;AAC3D,2BAAmB,aAAa,YAAY;AAC5C,gBAAQ,aAAa,YAAY;AAAA,MACnC;AACA,UAAI,mBAAmB,eAAe,YAAY,aAAa;AAC7D,2BAAmB,cAAc,YAAY;AAC7C,gBAAQ,cAAc,YAAY;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,WAAW;AAC9B,UAAM,UAAU,KAAK;AACrB,UAAM,mBAAmB,KAAK;AAC9B,UAAM,YAAY,UAAU,YACxB,UAAU,YACV;AACJ,QAAI,CAAC,kBAAkB;AACrB,cAAQ,OAAO,UAAU;AACzB,cAAQ,YAAY;AACpB,cAAQ,eAAe,UAAU;AACjC,WAAK,oBAAoB;AAAA,QACvB,MAAM,UAAU;AAAA,QAChB;AAAA,QACA,cAAc,UAAU;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,UAAI,iBAAiB,QAAQ,UAAU,MAAM;AAC3C,yBAAiB,OAAO,UAAU;AAClC,gBAAQ,OAAO,UAAU;AAAA,MAC3B;AACA,UAAI,iBAAiB,aAAa,WAAW;AAC3C,yBAAiB,YAAY;AAC7B,gBAAQ,YAAY;AAAA,MACtB;AACA,UAAI,iBAAiB,gBAAgB,UAAU,cAAc;AAC3D,yBAAiB,eAAe,UAAU;AAC1C,gBAAQ,eAAe,UAAU;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,WAAW,aAAa;AACzC,QAAI,CAAC,WAAW;AACd,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,YAAM,iBAAiB,UAAU,SAAS;AAC1C,WAAK,aAAa;AAAA,QAChB,WAAW;AAAA,UACT,iBAAiB,iBAAiB;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,aAAa;AAChB,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,YAAM,mBAAmB,YAAY,SAAS;AAC9C,YAAM,qBAAqB,YAAY,WAAW;AAClD,YAAM,sBAAsB,YAAY,YAAY;AACpD,YAAM,4BAA4B,YAAY,kBAAkB;AAChE,YAAM,sBAAsB,YAAY,YAAY;AACpD,YAAM,mBAAmB,YAAY,SAAS;AAC9C,YAAM,wBAAwB,YAAY,cAAc;AACxD,YAAM,WAAW,sBACb,sBACA;AACJ,WAAK,eAAe;AAAA,QAClB,SACE,uBAAuB,SACnB,qBACA;AAAA,QACN,UACE,KAAK,gBAAgB,IACjB,WACA,SAAS,IAAI,CAAC,MAAM,IAAI,KAAK,WAAW;AAAA,QAC9C,iBACG,4BACG,4BACA,yBAAyB,KAAK;AAAA,QACpC,UACE,wBAAwB,SACpB,sBACA;AAAA,QACN,YACG,qBAAqB,SAClB,mBACA,oBAAoB,KAAK;AAAA,QAC/B,YACE,0BAA0B,SACtB,wBACA;AAAA,QACN,aAAa;AAAA,UACX,mBAAmB,mBAAmB;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,QAAI;AACJ,QAAI,CAAC,cAAc,EAAE,YAAY,WAAW,QAAQ,IAAI;AACtD,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,kBAAkB,WAAW,cAAc,KAAK,WAAW;AACjE,UAAM,cAAc,WAAW,UAAU;AACzC,UAAM,cAAc,WAAW,UAAU;AACzC,SAAK,SAAS,WAAW,SAAS,KAAK,WAAW;AAClD,SAAK,gBAAgB,YAAY,CAAC,IAAI;AACtC,SAAK,gBAAgB,YAAY,CAAC,IAAI;AACtC,SAAK,eAAe,UAAU,CAAC,IAAI;AACnC,SAAK,gBAAgB,WAAW,WAAW;AAC3C,SAAK,gBAAgB,YAAY,CAAC;AAClC,SAAK,gBAAgB,YAAY,CAAC;AAClC,SAAK,uBAAuB,WAAW,kBAAkB;AACzD,SAAK,iBAAiB,WAAW,YAAY;AAC7C,UAAM,aAAa,WAAW,cAAc;AAC5C,SAAK,cAAc;AAAA,MAChB,WAAW,CAAC,IAAI,KAAK,cAAe;AAAA,MACpC,WAAW,CAAC,IAAI,KAAK,cAAe;AAAA,IACvC;AACA,SAAK,cAAc,UAAU,CAAC,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,WAAW;AACtB,QAAI,CAAC,WAAW;AACd,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,YAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAI,CAAC,eAAe;AAClB,aAAK,iBAAiB;AAAA,MACxB,OAAO;AACL,cAAM,qBAAqB,cAAc,SAAS;AAClD,aAAK,iBAAiB;AAAA,UACpB,WAAW;AAAA,YACT,qBAAqB,qBAAqB;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB,UAAU,UAAU;AAC5C,UAAI,CAAC,iBAAiB;AACpB,aAAK,mBAAmB;AAAA,MAC1B,OAAO;AACL,cAAM,uBAAuB,gBAAgB,SAAS;AACtD,cAAM,yBAAyB,gBAAgB,WAAW;AAC1D,cAAM,0BAA0B,gBAAgB,YAAY;AAC5D,cAAM,gCACJ,gBAAgB,kBAAkB;AACpC,cAAM,0BAA0B,gBAAgB,YAAY;AAC5D,cAAM,uBAAuB,gBAAgB,SAAS;AACtD,cAAM,4BAA4B,gBAAgB,cAAc;AAChE,aAAK,mBAAmB;AAAA,UACtB,SACE,2BAA2B,SACvB,yBACA;AAAA,UACN,UAAU,0BACN,0BACA;AAAA,UACJ,gBAAgB,gCACZ,gCACA;AAAA,UACJ,UACE,4BAA4B,SACxB,0BACA;AAAA,UACN,WACE,yBAAyB,SACrB,uBACA;AAAA,UACN,YACE,8BAA8B,SAC1B,4BACA;AAAA,UACN,aAAa;AAAA,YACX,uBAAuB,uBAAuB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,UAAU,QAAQ;AACnC,YAAM,cAAc,UAAU,WAAW;AACzC,YAAM,cAAc,UAAU,WAAW;AACzC,YAAM,qBAAqB,UAAU,kBAAkB;AACvD,YAAM,eAAe,UAAU,YAAY;AAC3C,YAAM,YAAY,UAAU,cAAc;AAC1C,YAAM,WAAW,UAAU,QAAQ;AACnC,YAAM,gBAAgB,UAAU,aAAa;AAC7C,YAAM,mBAAmB,UAAU,gBAAgB;AACnD,WAAK,aAAa;AAAA,QAChB,MAAM,aAAa,SAAY,WAAW;AAAA,QAC1C,WACE,kBAAkB,SAAY,gBAAgB;AAAA,QAChD,cACE,qBAAqB,SACjB,mBACA;AAAA,MACR;AACA,WAAK,QACH,aAAa,SACT,MAAM,QAAQ,QAAQ,IACpB,SAAS,OAAO,CAAC,KAAK,GAAG,MAAO,OAAO,IAAI,IAAI,MAAM,GAAI,EAAE,IAC3D,WACF;AACN,WAAK,eACH,gBAAgB,SAAY,KAAK,cAAc,cAAc;AAC/D,WAAK,eACH,gBAAgB,SAAY,KAAK,cAAc,cAAc;AAC/D,WAAK,sBACH,uBAAuB,SAAY,qBAAqB;AAC1D,WAAK,gBAAgB,iBAAiB,SAAY,eAAe;AACjE,WAAK,aAAa;AAAA,QAChB,KAAK,cAAc,UAAU,CAAC;AAAA,QAC9B,KAAK,cAAc,UAAU,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,oBAAQ;;;AC9oCR,IAAM,wBAAwB;AAgB9B,SAAS,4BACd,MACA,YACA,UACA,eACA,QACA,YACA,UACA;AACA,QAAM,QAAQ,KAAK,CAAC,IAAI;AACxB,QAAM,SAAS,KAAK,CAAC,IAAI;AACzB,QAAM,UAAU,sBAAsB,OAAO,MAAM;AACnD,UAAQ,wBAAwB;AAChC,QAAM,SAAS,QAAQ;AACvB,QAAM,WAAW,IAAI;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS;AAE9B,QAAM,cAAc,KAAK,OAAO,MAAM,MAAM,MAAM,KAAK,YAAY;AACnE,QAAM,mBAAmB,CAAC;AAC1B,WAAS,IAAI,GAAG,KAAK,cAAc,EAAE,GAAG;AACtC,UAAM,UAAU,SAAS,IAAI,CAAC;AAC9B,UAAM,uBAAuB,QAAQ,iBAAiB,KAAK;AAC3D,QAAI,CAAC,sBAAsB;AACzB;AAAA,IACF;AACA,QAAI,SAAS,qBAAqB,SAAS,UAAU;AACrD,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,eAAS,CAAC,MAAM;AAAA,IAClB;AACA,UAAM,QAAQ,IAAI;AAClB,UAAM,QAAQ,MAAM,SAAS,EAAE,EAAE,SAAS,GAAG,QAAQ;AACrD,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,YAAM,gBAAgB,OAAO,CAAC;AAC9B,YAAM,WAAW,cAAc,oBAAoB,EAAE,OAAO;AAC5D,UAAI,CAAC,YAAY,CAAC,WAAW,QAAQ,SAAS,UAAU,CAAC,GAAG;AAC1D;AAAA,MACF;AACA,YAAM,QAAQ,cAAc,MAAM;AAClC,YAAM,OAAO,MAAM,QAAQ;AAC3B,UAAI,MAAM;AACR,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,YAAM,SAAS,MAAM,UAAU;AAC/B,UAAI,QAAQ;AACV,eAAO,SAAS,KAAK;AACrB,eAAO,YAAY,IAAI;AAAA,MACzB;AACA,YAAM,QAAQ,MAAS;AACvB,YAAM,QAAQ,cAAc,SAAS;AACrC,UAAI,OAAO;AACT,cAAM,UAAU,MAAM,aAAa;AACnC,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,cAAM,aAAa;AAAA,UACjB,QAAQ,CAAC;AAAA,UACT,QAAQ,CAAC;AAAA,UACT;AAAA,UACA,EAAC,OAAO,MAAK;AAAA,QACf;AACA,cAAM,MAAM,WAAW;AACvB,mBAAW,YAAY;AACvB,mBAAW,SAAS,GAAG,GAAG,IAAI,OAAO,IAAI,MAAM;AAC/C,cAAM;AAAA,UACJ,IAAI,aAAK;AAAA,YACP;AAAA,YACA;AAAA,YACA,QAAQ,MAAM,UAAU;AAAA,YACxB,cAAc;AAAA,YACd,cAAc;AAAA,YACd,QAAQ,MAAM,UAAU;AAAA,YACxB,SAAS;AAAA,YACT,MAAM,MAAM,QAAQ;AAAA,YACpB,OAAO,MAAM,SAAS;AAAA,YACtB,UAAU,MAAM,YAAY;AAAA,YAC5B,gBAAgB,MAAM,kBAAkB;AAAA,UAC1C,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,SAAS,MAAM,UAAU,KAAK;AACpC,UAAI,iBAAiB,iBAAiB,MAAM;AAC5C,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,CAAC;AAClB,yBAAiB,MAAM,IAAI;AAC3B,uBAAe,SAAS,IAAI,CAAC;AAC7B,uBAAe,QAAQ,IAAI,CAAC;AAC5B,uBAAe,YAAY,IAAI,CAAC;AAChC,uBAAe,OAAO,IAAI,CAAC;AAAA,MAC7B;AACA,YAAM,OAAO,SAAS,QAAQ;AAC9B,UAAI,SAAS,sBAAsB;AACjC,cAAM;AAAA;AAAA,UAEF,SACA,4BAA4B;AAAA;AAChC,iBAASC,KAAI,GAAG,KAAK,WAAW,QAAQA,KAAI,IAAI,EAAEA,IAAG;AACnD,gBAAMC,YAAW,WAAWD,EAAC;AAC7B,yBAAeC,UAAS,QAAQ,EAAE,QAAQ,SAAS,EAAE,CAAC,EAAE;AAAA,YACtDA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,uBAAe,KAAK,QAAQ,SAAS,EAAE,CAAC,EAAE,KAAK,UAAU,KAAK;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa,OAAO,KAAK,gBAAgB,EAAE,IAAI,MAAM,EAAE,KAAK,SAAS;AAC3E,WAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAM,iBAAiB,iBAAiB,WAAW,CAAC,CAAC;AACrD,eAAW,QAAQ,gBAAgB;AACjC,YAAM,eAAe,eAAe,IAAI;AACxC,eAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK,GAAG;AACxD,iBAAS,SAAS,aAAa,IAAI,CAAC,CAAC;AACrC,iBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,mBAAS,aAAa,WAAW,CAAC,CAAC;AACnC,mBAAS,aAAa,aAAa,CAAC,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,QAAQ,aAAa,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/D;AAYO,SAAS,UAAU,OAAO,UAAU,WAAW;AACpD,QAAM,iBAAiB,CAAC;AACxB,MAAI,WAAW;AACb,UAAM,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,IAAI,qBAAqB;AACjE,UAAM,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,IAAI,qBAAqB;AAIjE,UAAM,SACH,MAAM,GAAG,GAAG,UAAU,QAAQ,CAAC,IAC9B,MAAM,GAAG,GAAG,UAAU,SAAS,CAAC,IAAI,UAAU,SAChD;AACF,UAAM,IAAI,UAAU,KAAK,KAAK;AAC9B,UAAM,IAAI,UAAU,KAAK,QAAQ,CAAC;AAClC,UAAM,IAAI,UAAU,KAAK,QAAQ,CAAC;AAClC,UAAM,IAAI,IAAI,OAAO,IAAI,MAAM;AAC/B,UAAM,cAAc,KAAK,OAAO,MAAM,MAAM,MAAM,KAAK,SAAS,MAAM;AACtE,QAAI,KAAK,IAAI,gBAAgB,GAAG;AAC9B,qBAAe,KAAK,SAAS,IAAI,cAAc,CAAC,CAAC;AAAA,IACnD;AAAA,EACF;AAEA,SAAO;AACT;;;AC/KA,IAAM,qBAAqB;AAQ3B,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,UAAU;AACZ;AAOO,SAAS,aAAa,UAAU,UAAU;AAC/C,SAAO,SAAS,OAAO,QAAQ,GAAG,EAAE,IAAI,SAAS,OAAO,QAAQ,GAAG,EAAE;AACvE;AAOO,SAAS,oBAAoB,YAAY,YAAY;AAC1D,QAAM,YAAY,aAAa,YAAY,UAAU;AACrD,SAAO,YAAY;AACrB;AAOO,SAAS,aAAa,YAAY,YAAY;AACnD,SAAQ,qBAAqB,aAAc;AAC7C;AASA,SAAS,qBACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,cAAc,MAAM,UAAU;AACpC,MAAI,aAAa,aAAa;AAC5B,UAAM,eAAe,aAAa,WAAW,MAAM,UAAU,GAAG,QAAQ;AACxE,iBAAa,mBAAmB,WAAW,WAAW;AACtD,iBAAa,WAAW,UAAU,OAAO;AAAA,EAC3C;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,UAAM,cAAc,yBAAyB,cAAc;AAAA,MACzD,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,eAAW,aAAa,SAAS;AACjC,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AAYO,SAAS,cACd,aACA,SACA,OACA,kBACA,UACA,WACA,uBACA;AACA,MAAI,UAAU;AACd,QAAM,aAAa,MAAM,SAAS;AAClC,MAAI,YAAY;AACd,UAAM,aAAa,WAAW,cAAc;AAC5C,QAAI,cAAc,mBAAW,UAAU,cAAc,mBAAW,OAAO;AACrE,iBAAW,oBAAoB,QAAQ;AAAA,IACzC,OAAO;AACL,UAAI,cAAc,mBAAW,MAAM;AACjC,mBAAW,KAAK;AAAA,MAClB;AACA,iBAAW,kBAAkB,QAAQ;AACrC,gBAAU;AAAA,IACZ;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,sBACP,aACA,SACA,OACA,kBACA,WACA,uBACA;AACA,QAAM,WAAW,MAAM,oBAAoB,EAAE,OAAO;AACpD,MAAI,CAAC,UAAU;AACb;AAAA,EACF;AACA,QAAM,qBAAqB,SAAS;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,MAAM,YAAY;AACnC,MAAI,UAAU;AACZ,mBAAe,aAAa,oBAAoB,OAAO,OAAO;AAAA,EAChE,OAAO;AACL,UAAM,mBAAmB,mBAAmB,mBAAmB,QAAQ,CAAC;AACxE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,eAAe,aAAa,UAAU,OAAO,SAAS;AAC7D,MAAI,SAAS,QAAQ,KAAK,sBAAsB;AAC9C,UAAM;AAAA;AAAA,MAEF,SACA,cAAc;AAAA;AAClB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,qBAAe,aAAa,WAAW,CAAC,GAAG,OAAO,OAAO;AAAA,IAC3D;AACA;AAAA,EACF;AACA,QAAM,SAAS,YAAY,WAAW,MAAM,UAAU,GAAG,SAAS;AAClE,SAAO;AAAA;AAAA,IACuD;AAAA,IAC5D;AAAA,IACA,MAAM,YAAY;AAAA,IAClB,MAAM,wBAAwB;AAAA,EAChC;AACF;AASA,SAAS,iCACP,aACA,UACA,OACA,SACA,uBACA;AACA,QAAM,aAAa,SAAS,mBAAmB;AAC/C,MAAI,GAAG;AACP,OAAK,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,UAAM,mBAAmB,mBAAmB,WAAW,CAAC,EAAE,QAAQ,CAAC;AACnE;AAAA,MACE;AAAA,MACA,WAAW,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AASA,SAAS,yBACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,cAAc,MAAM,UAAU;AACpC,MAAI,aAAa;AACf,UAAM,mBAAmB,aAAa;AAAA,MACpC,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,qBAAiB,mBAAmB,MAAM,WAAW;AACrD,qBAAiB,eAAe,UAAU,OAAO;AAAA,EACnD;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,UAAM,cAAc,yBAAyB,cAAc;AAAA,MACzD,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,eAAW,aAAa,SAAS;AACjC,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AASA,SAAS,8BACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,cAAc,MAAM,UAAU;AACpC,MAAI,aAAa;AACf,UAAM,mBAAmB,aAAa;AAAA,MACpC,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,qBAAiB,mBAAmB,MAAM,WAAW;AACrD,qBAAiB,oBAAoB,UAAU,OAAO;AAAA,EACxD;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,UAAM,cAAc,yBAAyB,cAAc;AAAA,MACzD,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,eAAW,aAAa,SAAS;AACjC,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AASA,SAAS,2BACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,cAAc,MAAM,UAAU;AACpC,MAAI,eAAe,WAAW;AAC5B,UAAM,gBAAgB,aAAa,WAAW,MAAM,UAAU,GAAG,SAAS;AAC1E,kBAAc,mBAAmB,WAAW,WAAW;AACvD,kBAAc,iBAAiB,UAAU,OAAO;AAAA,EAClD;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,UAAM,cAAc,yBAAyB,cAAc;AAAA,MACzD,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,eAAW,aAAa,SAAS;AACjC,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AASA,SAAS,oBACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,YAAY,MAAM,QAAQ;AAEhC,MAAI;AACJ,MAAI,YAAY;AACd,QAAI,WAAW,cAAc,KAAK,mBAAW,QAAQ;AACnD;AAAA,IACF;AACA,QAAI,oBAAoB;AACxB,QAAI,uBAAuB;AACzB,YAAM,gBAAgB,WAAW,iBAAiB;AAClD,UAAI,kBAAkB,QAAQ;AAC5B,4BAAoB;AACpB,YAAI,kBAAkB,YAAY;AAEhC,gBAAMC,eAAc,aAAa;AAAA,YAC/B,MAAM,UAAU;AAAA,YAChB;AAAA,UACF;AACA,UAAAA,aAAY,cAAc,YAAY,sBAAsB;AAC5D,UAAAA,aAAY,UAAU,UAAU,OAAO;AAAA,QACzC,WAAW,aAAa,UAAU,QAAQ,GAAG;AAC3C,mCAAyB,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,UAAM,cAAc,kBAAkB;AAAA,MACpC,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,gBAAY,cAAc,YAAY,sBAAsB;AAC5D,gBAAY,UAAU,UAAU,OAAO;AAAA,EACzC;AACA,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,QAAI,mBAAmB;AACvB,QAAI,uBAAuB;AACzB,yBAAmB;AAAA,IACrB;AACA,UAAM,aAAa,iBAAiB,WAAW,MAAM,UAAU,GAAG,MAAM;AACxE,eAAW,aAAa,WAAW,sBAAsB;AACzD,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AASA,SAAS,yBACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,YAAY,MAAM,QAAQ;AAEhC,MAAI;AACJ,MAAI,YAAY;AACd,QAAI,WAAW,cAAc,KAAK,mBAAW,QAAQ;AACnD;AAAA,IACF;AACA,QAAI,oBAAoB;AACxB,QAAI,uBAAuB;AACzB,YAAM,gBAAgB,WAAW,iBAAiB;AAClD,UAAI,kBAAkB,QAAQ;AAC5B,4BAAoB;AACpB,YAAI,kBAAkB,YAAY;AAEhC,gBAAMA,eAAc,aAAa;AAAA,YAC/B,MAAM,UAAU;AAAA,YAChB;AAAA,UACF;AACA,UAAAA,aAAY,cAAc,YAAY,sBAAsB;AAC5D,UAAAA,aAAY,eAAe,UAAU,OAAO;AAAA,QAC9C,WAAW,aAAa,UAAU,QAAQ,GAAG;AAC3C,mCAAyB,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,UAAM,cAAc,kBAAkB;AAAA,MACpC,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,gBAAY,cAAc,YAAY,sBAAsB;AAC5D,gBAAY,eAAe,UAAU,OAAO;AAAA,EAC9C;AACA,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,QAAI,mBAAmB;AACvB,QAAI,uBAAuB;AACzB,yBAAmB;AAAA,IACrB;AACA,UAAM,aAAa,iBAAiB,WAAW,MAAM,UAAU,GAAG,MAAM;AACxE,eAAW,aAAa,WAAW,sBAAsB;AACzD,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;AASA,SAAS,sBACP,cACA,UACA,OACA,SACA,uBACA;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,cAAc,MAAM,UAAU;AACpC,MAAI,aAAa,aAAa;AAC5B,UAAM,gBAAgB,aAAa,WAAW,MAAM,UAAU,GAAG,SAAS;AAC1E,kBAAc,mBAAmB,WAAW,WAAW;AACvD,kBAAc,YAAY,UAAU,OAAO;AAAA,EAC7C;AACA,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,aAAa,UAAU,QAAQ,GAAG;AACpC,UAAM,cAAc,yBAAyB,cAAc;AAAA,MACzD,MAAM,UAAU;AAAA,MAChB;AAAA,IACF;AACA,eAAW,aAAa,SAAS;AACjC,eAAW,SAAS,UAAU,OAAO;AAAA,EACvC;AACF;", "names": ["createDefaultStyle", "fillInstruction", "p1", "p2", "p3", "measureAndCacheTextWidth", "text", "p1", "p2", "p3", "p4", "fillInstruction", "strokeInstruction", "intersects", "i", "ii", "i", "result", "i", "geometry", "imageReplay"]}