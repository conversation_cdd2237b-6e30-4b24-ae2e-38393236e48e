<template>
  <el-dialog
    v-model="visible"
    title="分析详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="task" class="detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(task.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="图层名称">{{ task.layer_name }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ task.theme }}</el-descriptions-item>
          <el-descriptions-item label="分析数">{{ task.analysisCount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag type="success">已完成</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 左右布局区域 -->
      <div class="detail-layout">
        <!-- 左侧：分析任务 -->
        <div class="left-section">
          <div class="analysis-tasks">
            <div class="task-header">
              <h4>分析任务</h4>
              <div class="header-actions">
                <el-button type="success" size="small" :icon="Refresh" @click="refreshTaskInfo">
                  刷新
                </el-button>
                <el-button type="primary" size="small" :icon="Plus" @click="addAnalysisTask">
                  添加任务
                </el-button>
              </div>
            </div>

            <div class="task-list">
              <el-empty v-if="analysisTasks.length === 0" description="暂无分析任务">
                <template #image>
                  <el-icon size="60" color="#c0c4cc">
                    <List />
                  </el-icon>
                </template>
                <template #description>
                  <p>暂无分析任务</p>
                  <p>点击上方按钮添加新任务</p>
                </template>
              </el-empty>

              <div v-else class="task-items">
                <div
                  v-for="(analysisTask, index) in analysisTasks"
                  :key="analysisTask.task_id"
                  class="task-item"
                >
                  <div class="task-info">
                    <div class="task-name">{{ analysisTask.name }}</div>
                    <div class="task-meta">
                      <span class="task-type">{{ analysisTask.type }}</span>
                      <span class="task-time">{{ formatDateTime(analysisTask.createTime) }}</span>
                    </div>
                    <div class="task-status">
                      <el-tag :type="getTaskStatusType(analysisTask.status)">
                        {{ analysisTask.status }}
                      </el-tag>
                    </div>

                    <!-- 统计信息 -->
                    <div v-if="analysisTask.spatial_statistics" class="task-statistics">
                      <div class="stats-row">
                        <span class="stats-label">流出图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.outflow_count }}</span>
                        <span class="stats-label">流入图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.inflow_count }}</span>
                        <span class="stats-label">总计图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.total_count }}</span>
                      </div>
                      <div class="stats-row">
                        <span class="stats-label">流出面积:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.outflow_area.toFixed(2) }}㎡</span>
                        <span class="stats-label">流入面积:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.inflow_area.toFixed(2) }}㎡</span>
                      </div>
                    </div>
                  </div>
                  <div class="task-actions">
                    <el-button size="small" type="info" @click="viewTaskLog(analysisTask)" v-if="analysisTask.log_file">
                      查看日志
                    </el-button>
                    <el-button size="small" type="primary" @click="viewTaskDetail(analysisTask)">
                      查看
                    </el-button>
                    <el-button size="small" type="danger" @click="removeTask(index)" v-if="analysisTask.status === '待开始'">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：地图展示 -->
        <div class="right-section">
          <div class="map-display">
            <h4>地图展示</h4>
            <div class="map-container">
              <div v-if="mapLoading" class="loading-overlay">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <p>正在加载图层...</p>
              </div>
              <div :id="`analysis-map-${Date.now()}`" ref="mapContainer" class="map-content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" disabled>导出报告</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 任务配置对话框 -->
  <el-dialog
    v-model="taskConfigDialogVisible"
    title="添加分析任务"
    width="600px"
    :close-on-click-modal="false"
    @close="cancelTaskConfig"
  >
    <el-form :model="taskConfigForm" label-width="120px" label-position="left">
      <el-form-item label="任务ID">
        <el-input
          :value="props.task?.id || ''"
          readonly
          disabled
          placeholder="任务ID"
        />
      </el-form-item>

      <el-form-item label="任务类型" required>
        <el-select
          v-model="taskConfigForm.taskType"
          placeholder="请选择任务类型"
          style="width: 100%"
          @change="handleTaskTypeChange"
        >
          <el-option
            v-for="option in taskTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模型类型" required>
        <el-select
          v-model="taskConfigForm.modelType"
          placeholder="请选择模型类型"
          style="width: 100%"
          :disabled="!taskConfigForm.taskType"
          @change="handleModelTypeChange"
        >
          <el-option
            v-for="option in modelTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模型名称" required>
        <el-select
          v-model="taskConfigForm.modelName"
          placeholder="请选择模型名称"
          style="width: 100%"
          :disabled="!taskConfigForm.modelType"
        >
          <el-option
            v-for="option in modelNameOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="待对比对象" required>
        <el-select
          v-model="taskConfigForm.shpFile"
          placeholder="请选择待对比的对象"
          style="width: 100%"
          :disabled="!taskConfigForm.taskType"
        >
          <el-option
            v-for="option in shpFileOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="过滤面积" required>
        <el-input-number
          v-model="taskConfigForm.areaThreshold"
          :min="1"
          :max="10000"
          :step="10"
          style="width: 100%"
          placeholder="请输入过滤面积阈值"
        />
        <div class="form-item-tip">单位：平方米，用于过滤小于该面积的区域</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelTaskConfig">取消</el-button>
        <el-button type="primary" @click="confirmTaskConfig">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 日志查看对话框 -->
  <el-dialog
    v-model="logDialogVisible"
    :title="'任务日志: ' + currentLogTaskId"
    width="80%"
    destroy-on-close
  >
    <div class="log-dialog">
      <div class="content-box">
        <div class="content-header">
          <span>任务日志:</span>
          <el-button type="primary" size="small" :loading="logLoading" @click="fetchTaskLog(currentLogTaskId)">
            刷新
          </el-button>
        </div>
        <div class="content-body">
          <div v-if="logLoading" class="loading-content">
            <el-skeleton :rows="10" animated />
          </div>
          <div v-else-if="!logContent" class="empty-content">
            暂无任务日志信息，请点击刷新按钮获取
          </div>
          <pre v-else class="log-content">{{ logContent }}</pre>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, List, Loading, Refresh } from '@element-plus/icons-vue';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, ScaleLine, ZoomSlider, FullScreen } from 'ol/control';
import { getLayerBbox } from '/@/utils/geoserver';

// 接口定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

interface AnalysisTask {
  task_id: string;
  name: string;
  type: string;
  status: string;
  createTime: string;
  description?: string;
  analysis_category: string;
  timestamp: number;
  spatial_statistics?: {
    outflow_count: number;
    inflow_count: number;
    total_count: number;
    outflow_area: number;
    inflow_area: number;
    area_threshold: number;
  };
  log_file?: string;
}

// Props
interface Props {
  modelValue: boolean;
  task: ProcessedTask | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(props.modelValue);
const mapLoading = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);
const analysisTasks = ref<AnalysisTask[]>([]);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal && props.task) {
    console.log('弹窗打开，准备初始化地图，任务信息:', props.task);
    // 获取任务信息
    fetchTaskInfo();
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      console.log('开始初始化地图和加载图层');
      initMap();
      loadWMTSLayer();
    }, 300);
  }
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);

  if (!newVal && map.value) {
    // 清理地图
    cleanupMap();
  }
});

// 地图相关方法
const initMap = () => {
  console.log('initMap 被调用');
  console.log('map.value:', map.value);
  console.log('mapContainer.value:', mapContainer.value);

  if (map.value) {
    console.log('地图已存在，跳过初始化');
    return;
  }

  if (!mapContainer.value) {
    console.error('地图容器不存在');
    return;
  }

  try {
    mapLoading.value = true;
    console.log('开始初始化地图...');

    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    console.log('初始中心点:', initialCenter);

    // 创建地图控件
    const scaleLine = new ScaleLine({
      units: 'metric',
      bar: true,
      steps: 4,
      minWidth: 140
    });

    const zoomSlider = new ZoomSlider();
    const fullScreen = new FullScreen();

    console.log('创建地图实例...');
    map.value = new Map({
      target: mapContainer.value,
      layers: [], // 不添加底图，只使用GeoServer图层
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: defaultControls({
        zoom: true,
        rotate: false,
        attribution: false
      }).extend([
        scaleLine,
        zoomSlider,
        fullScreen
      ])
    });

    mapLoading.value = false;
    console.log('地图初始化成功，地图实例:', map.value);
  } catch (error) {
    console.error('地图初始化失败:', error);
    ElMessage.error('地图初始化失败');
    mapLoading.value = false;
  }
};

const loadWMTSLayer = () => {
  if (!map.value || !props.task?.layer_name) return;

  try {
    mapLoading.value = true;

    // 解析工作空间和图层名
    const parts = props.task.layer_name.split(':');
    if (parts.length !== 2) {
      throw new Error(`图层ID格式不正确: ${props.task.layer_name}`);
    }

    const workspace = parts[0];
    const layerName = parts[1];

    console.log(`创建WMTS图层: ${props.task.layer_name}, 工作区: ${workspace}, 图层名: ${layerName}`);

    // 获取Geoserver基础URL
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
    const geoserverUrl = `http://${host}:${port}/geoserver`;

    console.log(`WMTS服务端点: ${geoserverUrl}/gwc/service/wmts`);

    // 设置默认值
    const tileMatrixSet = 'EPSG:4326';
    const format = 'image/png';
    const style = '';

    // 获取投影
    const projection = getProjection(tileMatrixSet);
    if (!projection) {
      throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
    }

    // 创建WMTS源
    const source = new WMTS({
      url: `${geoserverUrl}/gwc/service/wmts`,
      layer: props.task.layer_name,
      matrixSet: tileMatrixSet,
      format: format,
      projection: projection,
      style: style,
      requestEncoding: 'KVP',
      tileGrid: createWmtsTileGrid(projection, tileMatrixSet),
      wrapX: true,
      transition: 0,
      crossOrigin: 'anonymous'
    });

    // 添加事件处理
    source.on('tileloaderror', (event) => {
      console.warn(`WMTS图层 ${props.task.layer_name} 加载失败:`, event);
    });

    source.on('tileloadend', () => {
      console.log(`WMTS图层 ${props.task.layer_name} 部分加载成功`);
    });

    // 创建并添加图层
    const tileLayer = new TileLayer({
      source: source,
      visible: true,
      opacity: 1,
      zIndex: 1
    });

    // 添加到地图
    map.value.addLayer(tileLayer);

    // 调用API获取图层范围并缩放
    fetchLayerExtentAndZoom(workspace, layerName);

  } catch (error) {
    console.error('加载图层失败:', error);
    ElMessage.error('加载图层失败');
    mapLoading.value = false;
  }
};

// 创建WMTS瓦片网格 - 完全参考MapPreviewDialog.vue的实现
const createWmtsTileGrid = (projection: any, gridSetId: string): WMTSTileGrid => {
  const projectionExtent = projection.getExtent();

  // 根据不同的坐标系创建合适的参数
  let origin, resolutions, matrixIds;

  if (gridSetId === 'EPSG:4326') {
    // EPSG:4326 特殊处理
    origin = [-180, 90]; // 正确的原点

    // 标准的EPSG:4326分辨率
    resolutions = [
      0.703125,              // 层级 0
      0.3515625,             // 层级 1
      0.17578125,            // 层级 2
      0.087890625,           // 层级 3
      0.0439453125,          // 层级 4
      0.02197265625,         // 层级 5
      0.010986328125,        // 层级 6
      0.0054931640625,       // 层级 7
      0.00274658203125,      // 层级 8
      0.001373291015625,     // 层级 9
      0.0006866455078125,    // 层级 10
      0.0003433227539062,    // 层级 11
      0.0001716613769531,    // 层级 12
      0.0000858306884766,    // 层级 13
      0.0000429153442383,    // 层级 14
      0.0000214576721191,    // 层级 15
      0.0000107288360596,    // 层级 16
      0.0000053644180298,    // 层级 17
      0.0000026822090149,    // 层级 18
      0.0000013411045074,    // 层级 19
      0.0000006705522537,    // 层级 20
      0.0000003352761269,    // 层级 21
      0.00000016763806345,   // 层级 22
      0.00000008381903173,   // 层级 23
      0.00000004190951586,   // 层级 24
      0.00000002095475793    // 层级 25
    ];

    // 标准的EPSG:4326 GeoServer矩阵ID
    matrixIds = [];
    for (let i = 0; i < resolutions.length; i++) {
      matrixIds.push(`${gridSetId}:${i}`);
    }
  } else {
    // 默认情况下，使用自动计算的值
    origin = getTopLeft(projectionExtent);
    const size = Math.max(
      projectionExtent[2] - projectionExtent[0],
      projectionExtent[3] - projectionExtent[1]
    );
    const maxResolution = size / 256;

    resolutions = [];
    matrixIds = [];
    for (let i = 0; i < 20; i++) {
      resolutions.push(maxResolution / Math.pow(2, i));
      // 使用标准GeoServer格式矩阵ID
      matrixIds.push(`${gridSetId}:${i}`);
    }
  }

  return new WMTSTileGrid({
    origin: origin,
    resolutions: resolutions,
    matrixIds: matrixIds
  });
};

// 获取图层范围并缩放到合适的位置
const fetchLayerExtentAndZoom = async (workspace: string, layerName: string) => {
  try {
    console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);
    const bboxData = await getLayerBbox(workspace, layerName);

    // 检查返回数据格式
    if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
      const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;

      // 检查获取的边界框是否有效
      if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
        console.warn(`API返回全球范围作为图层 ${workspace}:${layerName} 的边界框，尝试使用备用方法`);

        // 如果是全球范围，尝试使用native属性
        if (bboxData.bbox.native &&
            !(bboxData.bbox.native.minx === -180 &&
              bboxData.bbox.native.miny === -90 &&
              bboxData.bbox.native.maxx === 180 &&
              bboxData.bbox.native.maxy === 90)) {
          // 使用native属性
          const nativeBox = bboxData.bbox.native;
          console.log(`使用native边界框：`, nativeBox);

          // 转换为OpenLayers可用的范围
          const bottomLeft = fromLonLat([nativeBox.minx, nativeBox.miny]);
          const topRight = fromLonLat([nativeBox.maxx, nativeBox.maxy]);

          // 构建extent数组
          const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];

          console.log(`成功从API native属性获取并转换图层边界框:`, extent);
          fitToExtent(extent);
          return;
        } else {
          // 如果都是全球范围，使用默认范围
          fitToDefaultExtent();
        }
      } else {
        // 如果不是全球范围，则进行正常转换
        console.log(`API返回的有效边界框: minx=${minx}, miny=${miny}, maxx=${maxx}, maxy=${maxy}`);

        // 转换为OpenLayers可用的范围
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);

        // 构建extent数组
        const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];

        // 检查转换后的坐标是否有效
        if (extent.some(val => !isFinite(val))) {
          console.warn(`转换后的坐标包含无效值:`, extent);
          fitToDefaultExtent();
        } else {
          console.log(`成功从API获取并转换图层边界框:`, extent);
          fitToExtent(extent);
        }
      }
    } else {
      console.warn('API返回的边界框数据格式不正确:', bboxData);
      fitToDefaultExtent();
    }
  } catch (error) {
    console.error(`获取图层边界框失败:`, error);
    fitToDefaultExtent();
  } finally {
    mapLoading.value = false;
  }
};

// 缩放到指定范围
const fitToExtent = (extent: number[]): void => {
  if (!map.value) return;

  try {
    map.value.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      maxZoom: 16 // 降低最大缩放级别，避免超出瓦片范围
    });
    console.log(`已缩放至范围: ${extent}`);
  } catch (error) {
    console.error(`缩放至范围失败: ${error}`);
    fitToDefaultExtent();
  }
};

// 缩放到默认范围（南宁区域的大致范围）
const fitToDefaultExtent = (): void => {
  if (!map.value) return;

  try {
    // 南宁市区范围 - 与MapPreviewDialog.vue保持一致
    const extent = fromLonLat([108.2, 22.7]).concat(fromLonLat([108.5, 23.0]));
    map.value.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      maxZoom: 15
    });
    console.log(`已缩放至默认范围`);
  } catch (error) {
    console.error(`缩放至默认范围失败: ${error}`);
  }
};

const cleanupMap = () => {
  if (map.value) {
    map.value.setTarget(undefined);
    map.value = null;
  }
};

// 权重信息相关接口定义
interface WeightModel {
  name: string;
  path: string;
}

interface WeightTaskType {
  default: string;
  models: {
    [modelType: string]: WeightModel[];
  };
  display_name: string;
  default_area: number;
  shp_files: string[];
}

interface WeightInfo {
  [taskType: string]: WeightTaskType;
}

interface WeightApiResponse {
  status: string;
  message: string;
  data: WeightInfo;
}

// 任务配置相关数据
const weightInfo = ref<WeightInfo>({});
const taskConfigDialogVisible = ref(false);

// 日志查看相关数据
const logDialogVisible = ref(false);
const currentLogTaskId = ref('');
const logContent = ref('');
const logLoading = ref(false);
const taskConfigForm = ref({
  taskType: '',
  modelType: '',
  modelName: '',
  shpFile: '',
  areaThreshold: 400
});

// 获取权重信息
const fetchWeightInfo = async () => {
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/weight-info/`;

    console.log('获取权重信息，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: WeightApiResponse = await response.json();

    if (data.status === 'success') {
      weightInfo.value = data.data;
      console.log('成功获取权重信息:', data.data);
    } else {
      throw new Error(data.message || 'API返回状态不正确');
    }
  } catch (error) {
    console.error('获取权重信息失败:', error);
    ElMessage.error('获取权重信息失败，请检查网络连接');
  }
};

// 获取任务信息
const fetchTaskInfo = async () => {
  if (!props.task?.id) return;

  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/taskinfo/?id=${props.task.id}`;

    console.log('获取任务信息，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status === 'success' && data.data) {
      // 转换API数据为AnalysisTask格式
      const tasks: AnalysisTask[] = data.data.map((item: any) => {
        // 根据analysis_category和timestamp生成任务名称
        const categoryMap: Record<string, string> = {
          'arableLand': '耕地分析',
          'constructionLand': '建设用地分析'
        };
        const categoryName = categoryMap[item.analysis_category] || '分析';
        const date = new Date(item.timestamp * 1000);
        const timeStr = date.getFullYear().toString() +
                       (date.getMonth() + 1).toString().padStart(2, '0') +
                       date.getDate().toString().padStart(2, '0') +
                       date.getHours().toString().padStart(2, '0') +
                       date.getMinutes().toString().padStart(2, '0');

        return {
          task_id: item.task_id,
          name: `${categoryName}_${timeStr}`,
          type: categoryName,
          status: item.status,
          createTime: item.datetime,
          analysis_category: item.analysis_category,
          timestamp: item.timestamp,
          spatial_statistics: item.results?.spatial_statistics,
          log_file: item.log_file
        };
      });

      analysisTasks.value = tasks;
      console.log('成功获取任务信息:', tasks);
    } else {
      throw new Error(data.message || 'API返回状态不正确');
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    ElMessage.error('获取任务信息失败，请检查网络连接');
  }
};

// 获取任务日志
const fetchTaskLog = async (taskId: string) => {
  try {
    logLoading.value = true;
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/logs/?task_id=${taskId}`;

    console.log('获取任务日志，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status === 'success') {
      logContent.value = data.content || '暂无日志信息';
    } else {
      throw new Error(data.message || '获取日志失败');
    }
  } catch (error) {
    console.error('获取任务日志失败:', error);
    logContent.value = '获取任务日志失败，请重试';
    ElMessage.error('获取日志信息失败');
  } finally {
    logLoading.value = false;
  }
};

// 查看任务日志
const viewTaskLog = (task: AnalysisTask) => {
  currentLogTaskId.value = task.task_id;
  logDialogVisible.value = true;
  fetchTaskLog(task.task_id);
};

// 刷新任务信息
const refreshTaskInfo = () => {
  fetchTaskInfo();
};

// 分析任务相关方法
const addAnalysisTask = async () => {
  // 先获取权重信息
  await fetchWeightInfo();

  if (Object.keys(weightInfo.value).length === 0) {
    ElMessage.error('无法获取任务配置信息');
    return;
  }

  // 重置表单
  taskConfigForm.value = {
    taskType: '',
    modelType: '',
    modelName: '',
    shpFile: '',
    areaThreshold: 400
  };

  // 打开任务配置对话框
  taskConfigDialogVisible.value = true;
};

// 计算属性：获取任务类型选项
const taskTypeOptions = computed(() => {
  return Object.keys(weightInfo.value).map(key => ({
    value: key,
    label: weightInfo.value[key].display_name
  }));
});

// 计算属性：获取当前任务类型的模型类型选项
const modelTypeOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !weightInfo.value[taskConfigForm.value.taskType]) {
    return [];
  }

  const models = weightInfo.value[taskConfigForm.value.taskType].models;
  return Object.keys(models).map(key => ({
    value: key,
    label: key
  }));
});

// 计算属性：获取当前模型类型的模型名称选项
const modelNameOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !taskConfigForm.value.modelType ||
      !weightInfo.value[taskConfigForm.value.taskType]) {
    return [];
  }

  const models = weightInfo.value[taskConfigForm.value.taskType].models[taskConfigForm.value.modelType];
  return models ? models.map(model => ({
    value: model.name,
    label: model.name,
    path: model.path
  })) : [];
});

// 计算属性：获取当前任务类型的SHP文件选项
const shpFileOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !weightInfo.value[taskConfigForm.value.taskType]) {
    return [];
  }

  const shpFiles = weightInfo.value[taskConfigForm.value.taskType].shp_files;
  return shpFiles.map(filePath => {
    const fileName = filePath.split('/').pop() || filePath;
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, ''); // 去掉后缀
    return {
      value: filePath,
      label: nameWithoutExt
    };
  });
});

// 监听任务类型变化，自动设置默认模型
const handleTaskTypeChange = () => {
  if (taskConfigForm.value.taskType && weightInfo.value[taskConfigForm.value.taskType]) {
    const taskTypeInfo = weightInfo.value[taskConfigForm.value.taskType];
    const defaultModel = taskTypeInfo.default;

    console.log('任务类型变化:', taskConfigForm.value.taskType);
    console.log('任务类型信息:', taskTypeInfo);
    console.log('默认面积:', taskTypeInfo.default_area);

    // 解析默认模型 "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    const [modelType, modelName] = defaultModel.split('/');

    taskConfigForm.value.modelType = modelType;
    taskConfigForm.value.modelName = modelName;

    // 默认选择第一个SHP文件
    if (taskTypeInfo.shp_files.length > 0) {
      taskConfigForm.value.shpFile = taskTypeInfo.shp_files[0];
    }

    // 设置默认过滤面积
    taskConfigForm.value.areaThreshold = taskTypeInfo.default_area || 400;
    console.log('设置过滤面积为:', taskConfigForm.value.areaThreshold);
  } else {
    // 清空其他选项
    taskConfigForm.value.modelType = '';
    taskConfigForm.value.modelName = '';
    taskConfigForm.value.shpFile = '';
    taskConfigForm.value.areaThreshold = 400;
  }
};

// 监听模型类型变化，清空模型名称
const handleModelTypeChange = () => {
  taskConfigForm.value.modelName = '';
};

// 生成任务名称
const generateTaskName = (taskType: string): string => {
  const displayName = weightInfo.value[taskType]?.display_name || '分析';
  const now = new Date();
  const timeStr = now.getFullYear().toString() +
                  (now.getMonth() + 1).toString().padStart(2, '0') +
                  now.getDate().toString().padStart(2, '0') +
                  now.getHours().toString().padStart(2, '0') +
                  now.getMinutes().toString().padStart(2, '0');
  return `${displayName}_${timeStr}`;
};

// 生成影像路径
const generateImagePath = (taskId: string): string => {
  // 从props.task中获取window_output_path，然后拼接影像文件名
  // 影像路径格式：window_output_path/taskId_out.tif
  if (!props.task?.id) return '';

  // 这里需要从ODM任务数据中获取window_output_path
  // 暂时使用固定格式，后续可以从API获取完整数据
  const basePath = `D:/Drone_Project/nginxData/ODM/Output/${taskId}`;
  return `${basePath}/${taskId}_out.tif`;
};

// 确认任务配置
const confirmTaskConfig = () => {
  // 验证表单
  if (!taskConfigForm.value.taskType) {
    ElMessage.error('请选择任务类型');
    return;
  }

  if (!taskConfigForm.value.modelType) {
    ElMessage.error('请选择模型类型');
    return;
  }

  if (!taskConfigForm.value.modelName) {
    ElMessage.error('请选择模型名称');
    return;
  }

  if (!taskConfigForm.value.shpFile) {
    ElMessage.error('请选择待对比的对象');
    return;
  }

  // 获取选中模型的路径
  const selectedModel = modelNameOptions.value.find(model => model.value === taskConfigForm.value.modelName);
  const modelPath = selectedModel?.path || '';

  // 生成任务名称
  const taskName = generateTaskName(taskConfigForm.value.taskType);

  // 生成影像路径
  const imagePath = generateImagePath(props.task?.id || '');

  // 构建任务配置对象
  const taskConfig = {
    image: imagePath,
    model: modelPath,
    old_data_path: taskConfigForm.value.shpFile,
    area_threshold: taskConfigForm.value.areaThreshold,
    model_type: taskConfigForm.value.modelType,
    id: props.task?.id || ''
  };

  // 打印任务配置信息
  console.log('任务配置信息:', taskConfig);

  // 创建新的分析任务
  const newTask: AnalysisTask = {
    task_id: `temp-${Date.now()}`, // 临时ID，实际提交后会有真实ID
    name: taskName,
    type: weightInfo.value[taskConfigForm.value.taskType].display_name,
    status: '待开始',
    createTime: new Date().toLocaleString('zh-CN'),
    description: `${weightInfo.value[taskConfigForm.value.taskType].display_name}分析任务`,
    analysis_category: taskConfigForm.value.taskType,
    timestamp: Math.floor(Date.now() / 1000)
  };

  analysisTasks.value.push(newTask);
  taskConfigDialogVisible.value = false;
  ElMessage.success('任务添加成功');
};

// 取消任务配置
const cancelTaskConfig = () => {
  taskConfigDialogVisible.value = false;
};

const removeTask = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分析任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    analysisTasks.value.splice(index, 1);
    ElMessage.success('任务删除成功');
  } catch {
    // 用户取消操作
  }
};

const viewTaskDetail = (task: AnalysisTask) => {
  ElMessage.info(`查看任务详情: ${task.name} (功能开发中)`);
};

const getTaskStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '待开始':
      return 'info';
    case '失败':
      return 'danger';
    default:
      return 'info';
  }
};

// 其他方法
const handleClose = () => {
  visible.value = false;
};

const handleExport = () => {
  // 导出功能，后续实现
  console.log('导出报告功能开发中...');
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};



// 生命周期
onBeforeUnmount(() => {
  cleanupMap();
});
</script>

<style lang="scss" scoped>
.detail-content {
  .basic-info {
    margin-bottom: 24px;
  }
  
  .detail-layout {
    display: flex;
    gap: 24px;
    min-height: 400px;
    
    .left-section {
      flex: 1;

      .analysis-tasks {
        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            flex: 1;
          }

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .task-list {
          max-height: 400px;
          overflow-y: auto;

          .task-items {
            .task-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px;
              margin-bottom: 8px;
              background-color: #f8f9fa;
              border-radius: 8px;
              border: 1px solid #e4e7ed;
              transition: all 0.3s;

              &:hover {
                background-color: #ecf5ff;
                border-color: #b3d8ff;
              }

              .task-info {
                flex: 1;

                .task-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: #303133;
                  margin-bottom: 4px;
                }

                .task-meta {
                  display: flex;
                  gap: 12px;
                  margin-bottom: 4px;

                  .task-type {
                    font-size: 12px;
                    color: #909399;
                  }

                  .task-time {
                    font-size: 12px;
                    color: #909399;
                  }
                }

                .task-status {
                  margin-bottom: 8px;

                  .el-tag {
                    font-size: 12px;
                  }
                }

                .task-statistics {
                  background-color: #f8f9fa;
                  padding: 8px;
                  border-radius: 4px;
                  font-size: 12px;

                  .stats-row {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 4px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .stats-label {
                      color: #606266;
                      min-width: 60px;
                    }

                    .stats-value {
                      color: #409EFF;
                      font-weight: 600;
                      margin-right: 12px;
                    }
                  }
                }
              }

              .task-actions {
                display: flex;
                gap: 8px;
              }
            }
          }
        }
      }
    }
    
    .right-section {
      flex: 1;

      .map-display {
        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 8px;
        }

        .map-container {
          position: relative;
          height: 450px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          overflow: hidden;

          .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;

            .loading-icon {
              font-size: 32px;
              color: #409EFF;
              animation: rotate 2s linear infinite;
              margin-bottom: 12px;
            }

            p {
              color: #606266;
              font-size: 14px;
              margin: 0;
            }
          }

          .map-content {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 动画效果
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// OpenLayers地图样式覆盖
:deep(.ol-control) {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

:deep(.ol-zoom) {
  top: 8px;
  left: 8px;
}

:deep(.ol-attribution) {
  bottom: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.ol-scale-line) {
  bottom: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

// 任务配置对话框样式
.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
