<template>
  <el-dialog
    v-model="visible"
    title="分析详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="task" class="detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(task.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="图层名称">{{ task.layer_name }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ task.theme }}</el-descriptions-item>
          <el-descriptions-item label="分析数">{{ analysisTasks.length }}</el-descriptions-item>
          <el-descriptions-item label="空间范围">{{ formatBoundingBox(task.bbox) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 左右布局区域 -->
      <div class="detail-layout">
        <!-- 左侧：分析任务 -->
        <div class="left-section">
          <div class="analysis-tasks">
            <div class="task-header">
              <h4>分析任务</h4>
              <div class="header-actions">
                <el-button type="success" size="small" :icon="Refresh" @click="refreshTaskInfo">
                  刷新
                </el-button>
                <el-button type="primary" size="small" :icon="Plus" @click="addAnalysisTask">
                  添加任务
                </el-button>
              </div>
            </div>

            <div class="task-list">
              <el-empty v-if="analysisTasks.length === 0" description="暂无分析任务">
                <template #image>
                  <el-icon size="60" color="#c0c4cc">
                    <List />
                  </el-icon>
                </template>
                <template #description>
                  <p>暂无分析任务</p>
                  <p>点击上方按钮添加新任务</p>
                </template>
              </el-empty>

              <div v-else class="task-items">
                <div
                  v-for="(analysisTask, index) in analysisTasks"
                  :key="analysisTask.task_id"
                  class="task-item"
                >
                  <div class="task-info">
                    

                    <div class="task-header-row">
                      <div class="task-name-with-status">
                        <span class="task-name">{{ analysisTask.name }}</span>
                        <el-tag
                          :type="getTaskStatusType(analysisTask.status)"
                          :class="getTaskStatusClass(analysisTask.status)"
                          size="small"
                        >
                          {{ analysisTask.status }}
                        </el-tag>
                        <el-tooltip content="查看日志" placement="top" v-if="analysisTask.log_file">
                          <el-button
                            size="small"
                            circle
                            type="info"
                            :icon="Document"
                            @click="viewTaskLog(analysisTask)"
                          />
                        </el-tooltip>
                      </div>
                    </div>

                  

                    <!-- 统计信息 -->
                    <div v-if="analysisTask.spatial_statistics" class="task-statistics">
                      <div class="stats-row">
                        <span class="stats-label">分析类型:</span>
                        <span class="stats-value">{{ getAnalysisCategoryName(analysisTask.analysis_category) }}</span>
                        <span class="stats-label">对比数据:</span>
                        <span class="stats-value">{{ extractFileName(analysisTask.old_data_path) }}</span>
                    

                      </div>
                      <div class="stats-row">
                         
                        <span class="stats-label">流出图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.outflow_count || 0 }}</span>
                        <span class="stats-label">流入图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.inflow_count || 0 }}</span>
                        <span class="stats-label">总计图斑数:</span>
                        <span class="stats-value">{{ analysisTask.spatial_statistics.total_count || 0 }}</span>
                      </div>
                      <div class="stats-row">
                        <span class="stats-label">流出面积:</span>
                        <span class="stats-value">{{ formatArea(analysisTask.spatial_statistics.outflow_area) }}㎡</span>
                        <span class="stats-label">流入面积:</span>
                        <span class="stats-value">{{ formatArea(analysisTask.spatial_statistics.inflow_area) }}㎡</span>
                      </div>
                    </div>

                    <!-- 模型信息 -->
                    <div class="task-model-row">
                      <div class="model-info">
                        <span class="model-label">模型类型:</span>
                        <span class="model-value">{{ analysisTask.model_type || '-' }}</span>
                      </div>
                      <div class="model-info">
                        <span class="model-label">模型名:</span>
                        <el-tooltip :content="analysisTask.model_name || '-'" placement="top" :disabled="!analysisTask.model_name || analysisTask.model_name.length <= 20">
                          <span class="model-value model-name-truncated">{{ truncateModelName(analysisTask.model_name) }}</span>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <div class="task-actions">
                    <el-tooltip
                      :content="currentViewingTask === analysisTask.task_id ? '取消查看分析结果' : (analysisTask.status === '完成' ? '查看分析结果' : '任务未完成，无法查看结果')"
                      placement="top"
                    >
                      <el-button
                        size="small"
                        :type="currentViewingTask === analysisTask.task_id ? 'warning' : 'primary'"
                        @click="currentViewingTask === analysisTask.task_id ? cancelViewAnalysisTask() : viewAnalysisTask(analysisTask)"
                        :disabled="analysisTask.status !== '完成'"
                      >
                        {{ currentViewingTask === analysisTask.task_id ? '取消' : '查看' }}
                      </el-button>
                    </el-tooltip>
                    <el-button size="small" type="danger" @click="removeTask(index)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：地图展示 -->
        <div class="right-section">
          <div class="map-display">
            <h4>地图展示</h4>
            <div class="map-container">
              <div v-if="mapLoading" class="loading-overlay">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <p>正在加载图层...</p>
              </div>
              <div :id="`analysis-map-${Date.now()}`" ref="mapContainer" class="map-content"></div>

              <!-- 鼠标位置显示 -->
              <div v-if="mousePositionVisible" class="mouse-position">
                {{ mousePosition }}
              </div>

              <!-- 图例 -->
              <div v-if="currentViewingTask" class="map-legend">
                <div class="legend-title">图例</div>
                <div class="legend-items">
                  <div class="legend-item">
                    <div class="legend-color legend-inflow"></div>
                    <span class="legend-text">流入</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color legend-outflow"></div>
                    <span class="legend-text">流出</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color legend-ai-result"></div>
                    <span class="legend-text">AI提取结果</span>
                  </div>
                </div>
              </div>

              <!-- 图层控制面板 -->
              <div class="layer-control-panel" :class="{ 'collapsed': !layerPanelVisible }">
                <div class="panel-header" @click="toggleLayerPanel">
                  <el-icon><List /></el-icon>
                  <span v-show="layerPanelVisible">图层控制</span>
                  <el-icon v-show="layerPanelVisible" class="collapse-icon" :class="{ 'collapsed': !layerPanelVisible }">
                    <ArrowDown />
                  </el-icon>
                </div>

                <div v-show="layerPanelVisible" class="panel-content">
                  <div class="layer-list">
                    <div class="layer-items">
                      <div
                        v-for="(layer, index) in layersList"
                        :key="layer.id"
                        class="layer-item"
                        :class="{ 'layer-hidden': !layer.visible }"
                      >
                        <!-- 左侧：上移下移按钮 -->
                        <div class="move-controls">
                          <el-button
                            size="small"
                            :disabled="index === 0"
                            @click="moveLayerUp(index)"
                            class="move-btn move-up"
                          >
                            <el-icon><ArrowUp /></el-icon>
                          </el-button>

                          <el-button
                            size="small"
                            :disabled="index === layersList.length - 1"
                            @click="moveLayerDown(index)"
                            class="move-btn move-down"
                          >
                            <el-icon><ArrowDown /></el-icon>
                          </el-button>
                        </div>

                        <!-- 中间：图层名称 -->
                        <div class="layer-info">
                          <div class="layer-name">{{ layer.name }}</div>
                        </div>

                        <!-- 右侧：显示隐藏按钮 -->
                        <div class="visibility-control">
                          <el-button
                            size="small"
                            :type="layer.visible ? 'primary' : 'info'"
                            @click="toggleLayerVisibility(layer.id)"
                            class="visibility-btn"
                          >
                            <el-icon>
                              <View v-if="layer.visible" />
                              <Hide v-else />
                            </el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" disabled>导出报告</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 任务配置对话框组件 -->
  <TaskConfigDialog
    v-model="taskConfigDialogVisible"
    :task="task"
    :weight-info="weightInfo"
    @confirm="handleTaskConfigConfirm"
  />

  <!-- 日志查看对话框组件 -->
  <LogViewDialog
    v-model="logDialogVisible"
    :task-id="currentLogTaskId"
  />
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, List, Loading, Refresh, Document, ArrowDown, View, Hide, ArrowUp } from '@element-plus/icons-vue';

// 导入子组件
import TaskConfigDialog from './TaskConfigDialog.vue';
import LogViewDialog from './LogViewDialog.vue';

// 导入地图工具类
import { MapUtils } from '../utils/mapUtils';
import Map from 'ol/Map';

// 接口定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

interface AnalysisTask {
  task_id: string;
  name: string;
  type: string;
  status: string;
  createTime: string;
  description?: string;
  analysis_category: string;
  timestamp: number;
  model_type?: string;
  model_name?: string;
  old_data_path?: string;
  spatial_statistics?: {
    outflow_count: number;
    inflow_count: number;
    total_count: number;
    outflow_area: number;
    inflow_area: number;
    area_threshold: number;
  };
  log_file?: string;
}

// Props
interface Props {
  modelValue: boolean;
  task: ProcessedTask | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(props.modelValue);
const mapLoading = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);
const mousePosition = ref<string>('');
const mousePositionVisible = ref<boolean>(false);
const analysisTasks = ref<AnalysisTask[]>([]);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal && props.task) {
    console.log('弹窗打开，准备初始化地图，任务信息:', props.task);
    // 获取任务信息
    fetchTaskInfo();
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      console.log('开始初始化地图和加载图层');
      mapUtils.initMap();

      // 设置图层变化监听器
      mapUtils.setLayersChangeListener(() => {
        console.log('检测到图层变化，自动刷新图层列表');
        refreshLayersList();
      });

      if (props.task?.layer_name) {
        mapUtils.loadWMTSLayer(props.task.layer_name);
      }
    }, 300);
  }
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);

  if (!newVal && map.value) {
    // 清理地图
    mapUtils.cleanupMap();
  }
});









// 权重信息相关接口定义
interface WeightModel {
  name: string;
  path: string;
}

interface WeightTaskType {
  default: string;
  models: {
    [modelType: string]: WeightModel[];
  };
  display_name: string;
  default_area: number;
  shp_files: string[];
}

interface WeightInfo {
  [taskType: string]: WeightTaskType;
}

interface WeightApiResponse {
  status: string;
  message: string;
  data: WeightInfo;
}

// 任务配置相关数据
const weightInfo = ref<WeightInfo>({});
const taskConfigDialogVisible = ref(false);

// 日志查看相关数据
const logDialogVisible = ref(false);
const currentLogTaskId = ref('');

// 图层控制面板相关
const layerPanelVisible = ref(true);
const layersList = ref<any[]>([]);

// 创建地图工具类实例
const mapUtils = new MapUtils(
  map,
  mapContainer,
  mapLoading,
  mousePosition,
  mousePositionVisible
);

// 获取权重信息
const fetchWeightInfo = async () => {
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/weight-info/`;

 
    
    console.log('获取权重信息，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: WeightApiResponse = await response.json();

    if (data.status === 'success') {
      weightInfo.value = data.data;
      console.log('成功获取权重信息:', data.data);
    } else {
      throw new Error(data.message || 'API返回状态不正确');
    }
  } catch (error) {
    console.error('获取权重信息失败:', error);
    ElMessage.error('获取权重信息失败，请检查网络连接');
  }
};

// 获取任务信息
const fetchTaskInfo = async () => {
  if (!props.task?.id) return;

  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/taskinfo/?id=${props.task.id}`;

    
    console.log('获取任务信息，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status === 'success' && data.data) {
      // 转换API数据为AnalysisTask格式
      const tasks: AnalysisTask[] = data.data.map((item: any) => {
        // 根据analysis_category和timestamp生成任务名称
        const categoryMap: Record<string, string> = {
          'arableLand': '耕地分析',
          'constructionLand': '建设用地分析'
        };
        const categoryName = categoryMap[item.analysis_category] || '分析';
        const date = new Date(item.timestamp * 1000);
        const timeStr = date.getFullYear().toString() +
                       (date.getMonth() + 1).toString().padStart(2, '0') +
                       date.getDate().toString().padStart(2, '0') +
                       date.getHours().toString().padStart(2, '0') +
                       date.getMinutes().toString().padStart(2, '0');

        return {
          task_id: item.task_id,
          name: `${categoryName}_${timeStr}`,
          type: categoryName,
          status: item.status,
          createTime: item.datetime,
          analysis_category: item.analysis_category,
          timestamp: item.timestamp,
          model_type: item.parameters?.model_type,
          model_name: item.parameters?.model_name,
          old_data_path: item.input_files?.old_data_path,
          spatial_statistics: item.results?.spatial_statistics,
          log_file: item.log_file
        };
      });

      // 将任务列表按时间戳倒序排列（最新的在前面）
      analysisTasks.value = tasks.reverse();
      console.log('成功获取任务信息:', tasks);
    } else {
      throw new Error(data.message || 'API返回状态不正确');
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    ElMessage.error('获取任务信息失败，请检查网络连接');
  }
};

// 获取任务日志
// 查看任务日志
const viewTaskLog = (task: AnalysisTask) => {
  currentLogTaskId.value = task.task_id;
  logDialogVisible.value = true;
};

// 刷新任务信息
const refreshTaskInfo = () => {
  fetchTaskInfo();
};

// 分析任务相关方法
const addAnalysisTask = async () => {
  // 先获取权重信息
  await fetchWeightInfo();

  if (Object.keys(weightInfo.value).length === 0) {
    ElMessage.error('无法获取任务配置信息');
    return;
  }



  // 打开任务配置对话框
  taskConfigDialogVisible.value = true;
};

// 调用分析任务API
const callAnalysisAPI = async (configData: any) => {
  try {
    // 构建API URL
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const baseUrl = `http://${geoserverIp}:${geoserverPort}`;

    // 调试：打印配置数据
    console.log('完整的configData:', configData);
    console.log('config对象:', configData.config);

    // 从config对象中获取所有参数
    const config = configData.config;
    if (!config) {
      throw new Error('配置对象不存在');
    }

    // 检查必需参数
    if (!config.model) {
      throw new Error('模型路径不能为空，请检查模型选择');
    }

    // 构建请求参数 - 全部从config对象获取
    const requestParams = {
      id: config.id || props.task?.id || '',
      image: config.image,
      model: config.model,
      old_data_path: config.old_data_path,
      area_threshold: config.area_threshold.toString()
    };

    console.log('请求参数对象:', requestParams);

    // 检查每个参数是否有值
    Object.entries(requestParams).forEach(([key, value]) => {
      console.log(`参数 ${key}:`, value, `(类型: ${typeof value})`);
      if (!value && key !== 'old_data_path') {  // old_data_path 可以为空
        console.warn(`警告: 参数 ${key} 为空`);
      }
    });

    const params = new URLSearchParams(requestParams);
    const apiUrl = `${baseUrl}/api/analysis/combined-ai-spatial-analysis/?${params.toString()}`;

    console.log('最终API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('HTTP响应状态:', response.status, response.statusText);

    if (!response.ok) {
      // 尝试获取错误响应的详细内容
      let errorDetail = '';
      try {
        const errorText = await response.text();
        console.log('错误响应内容:', errorText);
        errorDetail = errorText ? ` - ${errorText}` : '';
      } catch (e) {
        console.log('无法读取错误响应内容');
      }
      throw new Error(`API请求失败: ${response.status} ${response.statusText}${errorDetail}`);
    }

    const result = await response.json();
    console.log('API响应:', result);

    return result;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};

// 处理任务配置确认
const handleTaskConfigConfirm = async (configData: any) => {
  try {
    console.log('提交任务配置:', configData);

    // 显示加载状态
    ElMessage.info('正在提交分析任务...');

    // 调用API
    const apiResult = await callAnalysisAPI(configData);

    // 检查API响应
    if (apiResult.status === 'success' || apiResult.status === 'started') {
      ElMessage.success('分析任务已成功提交并开始执行');

      // 刷新分析任务列表
      await fetchTaskInfo();
    } else {
      throw new Error(apiResult.message || 'API返回状态异常');
    }

  } catch (error) {
    console.error('提交分析任务失败:', error);
    ElMessage.error(`提交分析任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 加载WMS图层
const loadWMSLayer = (layerConfig: any) => {
  if (!mapUtils) {
    console.error('地图工具类未初始化');
    return;
  }

  console.log('加载WMS图层:', layerConfig);
  mapUtils.loadWMSLayer(layerConfig);
};

// 解析分析任务数据为WMS配置对象
const parseTaskToWMSConfigs = (task: any) => {
  const configs = [];
  const workspace = task.analysis_category; // 从analysis_category获取工作空间

  // 1. 解析 geoserver_publish 的 ai_result
  if (task.geoserver_publish?.ai_result?.success && task.geoserver_publish.ai_result.layer_name) {
    configs.push({
      defaultStyle: "fenxi",
      id: `${workspace}:${task.geoserver_publish.ai_result.layer_name}`,
      name: "AI提取结果",
      type: "raster",
      protocol: "WMS",
      workspace: workspace,
      layerName: task.geoserver_publish.ai_result.layer_name,
      opacity: 1,
      zIndex: 2 // 中间层
    });
  }

  // 2. 解析 geoserver_publish 的 final_result
  if (task.geoserver_publish?.final_result?.success && task.geoserver_publish.final_result.layer_name) {
    configs.push({
      defaultStyle: "flowtype",
      id: `${workspace}:${task.geoserver_publish.final_result.layer_name}`,
      name: "流入流出结果",
      type: "raster",
      protocol: "WMS",
      workspace: workspace,
      layerName: task.geoserver_publish.final_result.layer_name,
      opacity: 1,
      zIndex: 3 // 最上层
    });
  }

  // 3. 解析 input_files 的 old_data_path
  if (task.input_files?.old_data_path) {
    // 从路径中提取文件名（去掉扩展名）
    const pathParts = task.input_files.old_data_path.split(/[/\\]/);
    const fileName = pathParts[pathParts.length - 1];
    const layerName = fileName.replace(/\.[^/.]+$/, ''); // 去掉扩展名

    configs.push({
      defaultStyle: "ditu",
      id: `${workspace}:${layerName}`,
      name: layerName,
      type: "raster",
      protocol: "WMS",
      workspace: workspace,
      layerName: layerName,
      opacity: 1,
      zIndex: 1 // 底层
    });
  }

  return configs;
};

// 当前查看的任务ID和对应的图层ID列表
const currentViewingTask = ref<string | null>(null);
const currentViewingLayers = ref<string[]>([]);

// 获取任务详细信息
const fetchTaskDetailInfo = async (taskId: string) => {
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const baseUrl = `http://${geoserverIp}:${geoserverPort}`;

    const apiUrl = `${baseUrl}/api/analysis/taskinfo/?id=${props.task?.id}`;
    console.log('获取任务详细信息:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('任务详细信息响应:', result);

    if (result.status === 'success' && result.data && Array.isArray(result.data)) {
      // 查找指定的任务
      const taskDetail = result.data.find((item: any) => item.task_id === taskId);
      return taskDetail;
    } else {
      throw new Error(result.message || '获取任务详细信息失败');
    }
  } catch (error) {
    console.error('获取任务详细信息失败:', error);
    throw error;
  }
};

// 查看分析任务结果
const viewAnalysisTask = async (task: any) => {
  if (currentViewingTask.value === task.task_id) {
    // 如果当前正在查看这个任务，则取消查看
    cancelViewAnalysisTask();
    return;
  }

  // 如果正在查看其他任务，先取消
  if (currentViewingTask.value) {
    cancelViewAnalysisTask();
  }

  try {
    console.log('查看分析任务:', task);
    ElMessage.info('正在获取任务详细信息...');

    // 获取任务详细信息
    const taskDetail = await fetchTaskDetailInfo(task.task_id);

    if (!taskDetail) {
      ElMessage.warning('未找到该任务的详细信息');
      return;
    }

    console.log('任务详细信息:', taskDetail);

    // 解析任务详细数据为WMS配置
    const wmsConfigs = parseTaskToWMSConfigs(taskDetail);
    console.log('解析的WMS配置:', wmsConfigs);

    if (wmsConfigs.length === 0) {
      ElMessage.warning('该任务没有可查看的图层数据');
      return;
    }

    // 按zIndex排序加载图层（先加载底层，再加载上层）
    wmsConfigs.sort((a, b) => a.zIndex - b.zIndex);

    // 加载所有图层
    const layerIds: string[] = [];
    wmsConfigs.forEach(config => {
      loadWMSLayer(config);
      layerIds.push(config.id);
    });

    // 记录当前查看的任务和图层
    currentViewingTask.value = task.task_id;
    currentViewingLayers.value = layerIds;

    ElMessage.success(`已加载 ${wmsConfigs.length} 个分析结果图层`);

  } catch (error) {
    console.error('查看分析任务失败:', error);
    ElMessage.error(`查看分析任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 取消查看分析任务结果
const cancelViewAnalysisTask = () => {
  if (!currentViewingTask.value || currentViewingLayers.value.length === 0) {
    return;
  }

  console.log('取消查看分析任务，移除图层:', currentViewingLayers.value);

  // 移除所有相关图层
  if (mapUtils) {
    mapUtils.removeLayers(currentViewingLayers.value);
  }

  // 清空记录
  currentViewingTask.value = null;
  currentViewingLayers.value = [];

  ElMessage.info('已移除分析结果图层');
};



// 图层控制面板相关方法
const toggleLayerPanel = () => {
  layerPanelVisible.value = !layerPanelVisible.value;
};

const refreshLayersList = () => {
  if (mapUtils) {
    layersList.value = mapUtils.getLayersInfo();
    console.log('图层列表已刷新:', layersList.value);
  }
};

const toggleLayerVisibility = (layerId: string) => {
  if (mapUtils) {
    mapUtils.toggleLayerVisibility(layerId);
    // 不需要手动刷新，监听器会自动刷新
  }
};

// 上移图层
const moveLayerUp = (index: number) => {
  if (index > 0 && mapUtils) {
    // 交换数组中的位置
    const newList = [...layersList.value];
    [newList[index], newList[index - 1]] = [newList[index - 1], newList[index]];

    // 更新图层顺序
    const orderedLayerIds = newList.map(layer => layer.id);
    mapUtils.updateLayersOrder(orderedLayerIds);
  }
};

// 下移图层
const moveLayerDown = (index: number) => {
  if (index < layersList.value.length - 1 && mapUtils) {
    // 交换数组中的位置
    const newList = [...layersList.value];
    [newList[index], newList[index + 1]] = [newList[index + 1], newList[index]];

    // 更新图层顺序
    const orderedLayerIds = newList.map(layer => layer.id);
    mapUtils.updateLayersOrder(orderedLayerIds);
  }
};











const removeTask = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分析任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    analysisTasks.value.splice(index, 1);
    ElMessage.success('任务删除成功');
  } catch {
    // 用户取消操作
  }
};

const viewTaskDetail = (task: AnalysisTask) => {
  ElMessage.info(`查看任务详情: ${task.name} (功能开发中)`);
};

const getTaskStatusType = (status: string) => {
  switch (status) {
    case '完成':
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '待开始':
      return 'info';
    case '失败':
      return 'danger';
    default:
      return 'info';
  }
};

const getTaskStatusClass = (status: string) => {
  switch (status) {
    case '完成':
    case '已完成':
      return 'status-success';
    case '进行中':
      return 'status-processing';
    case '待开始':
      return 'status-info';
    case '失败':
      return 'status-error';
    default:
      return 'status-info';
  }
};

// 其他方法
const handleClose = () => {
  visible.value = false;
};

const handleExport = () => {
  // 导出功能，后续实现
  console.log('导出报告功能开发中...');
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 安全地格式化面积数值
const formatArea = (area: number | null | undefined): string => {
  if (area === null || area === undefined || isNaN(area)) {
    return '0.00';
  }
  return area.toFixed(2);
};

// 获取分析类型的显示名称
const getAnalysisCategoryName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'arableLand': '耕地',
    'constructionLand': '建设用地'
  };
  return categoryMap[category] || category;
};

// 截断模型名称用于显示
const truncateModelName = (modelName: string | undefined, maxLength: number = 20): string => {
  if (!modelName) return '-';
  if (modelName.length <= maxLength) return modelName;
  return modelName.substring(0, maxLength) + '...';
};

// 格式化边界框信息
const formatBoundingBox = (bbox: any): string => {
  if (!bbox) return '-';

  try {
    const { minx, miny, maxx, maxy } = bbox;
    if (minx !== undefined && miny !== undefined && maxx !== undefined && maxy !== undefined) {
      return `${minx.toFixed(6)}, ${miny.toFixed(6)}, ${maxx.toFixed(6)}, ${maxy.toFixed(6)}`;
    }
    return '-';
  } catch (error) {
    console.error('格式化边界框失败:', error);
    return '-';
  }
};

// 从文件路径中提取文件名
const extractFileName = (filePath: string | undefined): string => {
  if (!filePath) return '-';

  try {
    // 处理Windows和Unix路径分隔符
    const fileName = filePath.split(/[/\\]/).pop();
    return fileName || '-';
  } catch (error) {
    console.error('提取文件名失败:', error);
    return '-';
  }
};



// 生命周期
onBeforeUnmount(() => {
  mapUtils.cleanupMap();
});

// 暴露方法给父组件使用
defineExpose({
  loadWMSLayer
});
</script>

<style lang="scss" scoped>
.detail-content {
  .basic-info {
    margin-bottom: 24px;
  }
  
  .detail-layout {
    display: flex;
    gap: 24px;
    min-height: 400px;

    .left-section {
      flex: 0 0 35%;

      .analysis-tasks {
        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            flex: 1;
          }

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .task-list {
          max-height: 370px;
          overflow-y: auto;

          .task-items {
            .task-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px;
              margin-bottom: 8px;
              background-color: #f8f9fa;
              border-radius: 8px;
              border: 1px solid #e4e7ed;
              transition: all 0.3s;

              &:hover {
                background-color: #ecf5ff;
                border-color: #b3d8ff;
              }

              .task-info {
                flex: 1;

                .task-category-row {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 8px;
                  padding: 4px 8px;
                  background-color: #f0f2f5;
                  border-radius: 4px;

                  .category-info {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .category-label {
                      font-size: 12px;
                      color: #606266;
                    }

                    .category-value {
                      font-size: 12px;
                      font-weight: 600;
                      color: #409eff;
                    }
                  }
                }

                .task-header-row {
                  margin-bottom: 12px;

                  .task-name-with-status {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .task-name {
                      font-size: 14px;
                      font-weight: 600;
                      color: #303133;
                    }

                    .el-tag {
                      font-size: 12px;

                      &.status-success {
                        background-color: #f0f9ff;
                        border-color: #67c23a;
                        color: #67c23a;
                      }

                      &.status-error {
                        background-color: #fef0f0;
                        border-color: #f56c6c;
                        color: #f56c6c;
                      }

                      &.status-processing {
                        background-color: #f4f4f5;
                        border-color: #409eff;
                        color: #409eff;
                      }

                      &.status-info {
                        background-color: #f4f4f5;
                        border-color: #909399;
                        color: #909399;
                      }
                    }

                    .el-button {
                      width: 24px;
                      height: 24px;
                      padding: 0;

                      .el-icon {
                        font-size: 12px;
                      }
                    }
                  }
                }

                .task-statistics {
                  background-color: #f8f9fa;
                  padding: 8px;
                  border-radius: 4px;
                  font-size: 12px;
                  margin-bottom: 12px;

                  .stats-row {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 4px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .stats-label {
                      color: #606266;
                      min-width: 60px;
                    }

                    .stats-value {
                      color: #409EFF;
                      font-weight: 600;
                      margin-right: 12px;
                    }
                  }
                }

                .task-model-row {
                  display: flex;
                  gap: 16px;
                  padding: 6px 8px;
                  background-color: #fafafa;
                  border-radius: 4px;
                  border-left: 3px solid #e6a23c;

                  .model-info {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .model-label {
                      font-size: 12px;
                      color: #606266;
                      white-space: nowrap;
                    }

                    .model-value {
                      font-size: 12px;
                      color: #303133;
                      font-weight: 500;

                      &.model-name-truncated {
                        max-width: 150px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        cursor: help;
                      }
                    }
                  }
                }
              }

              .task-actions {
                display: flex;
                gap: 8px;

                .el-button {
                  &:disabled {
                    background-color: #f5f7fa !important;
                    border-color: #e4e7ed !important;
                    color: #c0c4cc !important;
                    cursor: not-allowed !important;

                    &:hover {
                      background-color: #f5f7fa !important;
                      border-color: #e4e7ed !important;
                      color: #c0c4cc !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .right-section {
      flex: 1;

      .map-display {
        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 8px;
        }

        .map-container {
          position: relative;
          height: 370px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          overflow: hidden;

          .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;

            .loading-icon {
              font-size: 32px;
              color: #409EFF;
              animation: rotate 2s linear infinite;
              margin-bottom: 12px;
            }

            p {
              color: #606266;
              font-size: 14px;
              margin: 0;
            }
          }

          .map-content {
            width: 100%;
            height: 100%;
          }

          .mouse-position {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000;
            pointer-events: none;
            white-space: nowrap;
          }

          // 图例样式
          .map-legend {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(255, 255, 255, 0.95);
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 120px;

            .legend-title {
              font-size: 14px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 8px;
              text-align: center;
              border-bottom: 1px solid #e4e7ed;
              padding-bottom: 6px;
            }

            .legend-items {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;

                .legend-color {
                  width: 16px;
                  height: 16px;
                  border-radius: 3px;
                  border: 1px solid rgba(0, 0, 0, 0.1);
                  flex-shrink: 0;
                }

                .legend-inflow {
                  background-color: #67c23a; // 绿色 - 流入
                }

                .legend-outflow {
                  background-color: #f56c6c; // 红色 - 流出
                }

                .legend-ai-result {
                  background-color: #e6a23c; // 黄色 - AI提取结果
                }

                .legend-text {
                  font-size: 12px;
                  color: #606266;
                  line-height: 1;
                }
              }
            }
          }

          /* 图层控制面板样式 */
          .layer-control-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
            max-width: 180px;
            transition: all 0.3s ease;

            &.collapsed {
              min-width: 36px;
              max-width: 36px;
            }

            .panel-header {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 8px 12px;
              background: #f5f7fa;
              border-radius: 8px;
              cursor: pointer;
              user-select: none;
              transition: all 0.3s ease;

              .el-icon {
                color: #606266;
                font-size: 16px;
              }

              span {
                flex: 1;
                font-size: 13px;
                font-weight: 500;
                color: #303133;
                margin-left: 6px;
              }

              .collapse-icon {
                margin-left: 6px;
                transition: transform 0.3s;

                &.collapsed {
                  transform: rotate(-90deg);
                }
              }

              &:hover {
                background: #ecf5ff;
              }
            }

            &.collapsed .panel-header {
              border-radius: 8px;
              padding: 6px;
              justify-content: center;

              .el-icon {
                font-size: 14px;
              }
            }

            .panel-content {
              border-top: 1px solid #ebeef5;

              .layer-list {
                max-height: 250px;
                overflow-y: auto;

                .layer-items {
                  .layer-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 6px 8px;
                    border-bottom: 1px solid #f0f0f0;
                    transition: background-color 0.2s;
                    gap: 6px;

                    &:hover {
                      background: #f8f9fa;
                    }

                    &.layer-hidden {
                      opacity: 0.6;
                      background: #fafafa;
                    }

                    // 左侧：上移下移按钮
                    .move-controls {
                      display: flex;
                      flex-direction: column;
                      justify-content: flex-start;
                      align-items: center;
                      gap: 1px;
                      flex-shrink: 0;
                      width: 20px;
                      padding-top: 2px;

                      // 重置Element Plus的按钮间距
                      .el-button + .el-button {
                        margin-left: 0 !important;
                      }

                      .move-btn {
                        padding: 0;
                        min-height: 16px;
                        min-width: 16px;
                        max-width: 16px;
                        width: 16px;
                        height: 16px;
                        border-radius: 2px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-sizing: border-box;
                        margin-left: 0 !important;  // 覆盖Element Plus的默认边距

                        .el-icon {
                          font-size: 8px;
                        }
                      }
                    }

                    // 中间：图层名称
                    .layer-info {
                      flex: 1;
                      min-width: 0;
                      padding-top: 2px;
                      display: flex;
                      align-items: flex-start;

                      .layer-name {
                        font-size: 11px;
                        font-weight: 500;
                        color: #303133;
                        line-height: 1.3;
                        word-wrap: break-word;
                        word-break: break-all;
                      }
                    }

                    // 右侧：显示隐藏按钮
                    .visibility-control {
                      flex-shrink: 0;
                      padding-top: 2px;

                      .visibility-btn {
                        padding: 0;
                        min-height: 18px;
                        min-width: 18px;
                        width: 18px;
                        height: 18px;
                        border-radius: 3px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-sizing: border-box;

                        .el-icon {
                          font-size: 9px;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 动画效果
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// OpenLayers地图样式覆盖
:deep(.ol-control) {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

:deep(.ol-zoom) {
  top: 8px;
  left: 8px;
}

:deep(.ol-attribution) {
  bottom: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.ol-scale-line) {
  bottom: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}


</style>
