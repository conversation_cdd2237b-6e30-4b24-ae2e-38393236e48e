<template>
  <el-dialog
    v-model="visible"
    :title="`分析详情 - ${task?.id || ''}`"
    width="90%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="analysis-detail">
      <div class="left-panel">
        <!-- 基本信息组件 -->
        <BasicInfo :task="task" :analysis-count="analysisTasks.length" />

        <!-- 分析任务列表组件 -->
        <AnalysisTaskList
          :tasks="analysisTasks"
          @refresh="refreshTaskInfo"
          @add-task="addAnalysisTask"
          @view-log="viewTaskLog"
          @view-detail="viewTaskDetail"
          @remove-task="removeTask"
        />
      </div>

      <div class="right-panel">
        <!-- 地图展示组件 -->
        <MapDisplay :task="task" :visible="visible" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" disabled>导出报告</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 任务配置对话框组件 -->
  <TaskConfigDialog
    v-model="taskConfigDialogVisible"
    :task="task"
    :weight-info="weightInfo"
    @confirm="handleTaskConfigConfirm"
  />

  <!-- 日志查看对话框组件 -->
  <LogViewDialog
    v-model="logDialogVisible"
    :task-id="currentLogTaskId"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 导入子组件
import BasicInfo from './BasicInfo.vue';
import AnalysisTaskList from './AnalysisTaskList.vue';
import MapDisplay from './MapDisplay.vue';
import TaskConfigDialog from './TaskConfigDialog.vue';
import LogViewDialog from './LogViewDialog.vue';

// 接口定义
interface SpatialStatistics {
  outflow_count: number;
  inflow_count: number;
  total_count: number;
  outflow_area: number;
  inflow_area: number;
  area_threshold: number;
}

interface AnalysisTask {
  task_id: string;
  name: string;
  type: string;
  status: string;
  createTime: string;
  description?: string;
  analysis_category: string;
  timestamp: number;
  model_type?: string;
  model_name?: string;
  old_data_path?: string;
  spatial_statistics?: SpatialStatistics;
  log_file?: string;
}

interface Task {
  id: string;
  endTime: string;
  layer_name: string;
  theme: string;
  bbox: any;
}

interface Props {
  modelValue: boolean;
  task: Task | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(props.modelValue);
const analysisTasks = ref<AnalysisTask[]>([]);
const taskConfigDialogVisible = ref(false);
const logDialogVisible = ref(false);
const currentLogTaskId = ref('');
const weightInfo = ref({});

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal && props.task) {
    console.log('弹窗打开，获取任务信息:', props.task);
    fetchTaskInfo();
    fetchWeightInfo();
  }
});

// 监听visible变化
watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 获取权重信息
const fetchWeightInfo = async () => {
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/weights/`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'success' && data.data) {
      weightInfo.value = data.data;
      console.log('权重信息获取成功:', data.data);
    } else {
      throw new Error(data.message || '获取权重信息失败');
    }
  } catch (error) {
    console.warn('权重信息API不可用，使用默认配置:', error);
    // 使用默认的权重信息配置
    weightInfo.value = getDefaultWeightInfo();
  }
};

// 获取默认权重信息配置
const getDefaultWeightInfo = () => {
  return {
    arableLand: {
      default: "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
      models: {
        deeplabv3_plus: [
          {
            name: "deeplabv3_plus_best_20250807-111949.pth",
            path: "D:/Drone_Project/weights/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
          }
        ]
      },
      display_name: "耕地",
      default_area: 400,
      shp_files: [
        "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
      ]
    },
    constructionLand: {
      default: "deeplabv3_plus/deeplabv3_plus_best_construction.pth",
      models: {
        deeplabv3_plus: [
          {
            name: "deeplabv3_plus_best_construction.pth",
            path: "D:/Drone_Project/weights/constructionLand/deeplabv3_plus/deeplabv3_plus_best_construction.pth"
          }
        ]
      },
      display_name: "建设用地",
      default_area: 400,
      shp_files: [
        "D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设用地2024_84.shp"
      ]
    }
  };
};

// 获取任务信息
const fetchTaskInfo = async () => {
  if (!props.task?.id) return;

  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/tasks/?task_id=${props.task.id}`;

    console.log('获取任务信息，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('任务API返回数据:', data);

    if (data.status === 'success' && data.data && Array.isArray(data.data.tasks)) {
      // 转换数据格式
      const tasks = data.data.tasks.map((item: any) => {
        const categoryName = getAnalysisCategoryName(item.analysis_category);
        const createTime = new Date(item.datetime);
        const timeStr = createTime.getFullYear().toString() +
                       (createTime.getMonth() + 1).toString().padStart(2, '0') +
                       createTime.getDate().toString().padStart(2, '0') +
                       createTime.getHours().toString().padStart(2, '0') +
                       createTime.getMinutes().toString().padStart(2, '0');

        return {
          task_id: item.task_id,
          name: `${categoryName}_${timeStr}`,
          type: categoryName,
          status: item.status,
          createTime: item.datetime,
          analysis_category: item.analysis_category,
          timestamp: item.timestamp,
          model_type: item.parameters?.model_type,
          model_name: item.parameters?.model_name,
          old_data_path: item.input_files?.old_data_path,
          spatial_statistics: item.results?.spatial_statistics,
          log_file: item.log_file
        };
      });

      analysisTasks.value = tasks;
      console.log('任务信息处理完成，任务数量:', tasks.length);
    } else {
      throw new Error(data.message || '获取任务信息失败');
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    ElMessage.error('获取任务信息失败');
  }
};

// 获取分析类型的显示名称
const getAnalysisCategoryName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'arableLand': '耕地',
    'constructionLand': '建设用地'
  };
  return categoryMap[category] || category;
};

// 事件处理方法
const refreshTaskInfo = () => {
  fetchTaskInfo();
};

const addAnalysisTask = () => {
  taskConfigDialogVisible.value = true;
};

const viewTaskLog = (task: AnalysisTask) => {
  currentLogTaskId.value = task.task_id;
  logDialogVisible.value = true;
};

const viewTaskDetail = (task: AnalysisTask) => {
  ElMessage.info(`查看任务详情: ${task.name} (功能开发中)`);
};

const removeTask = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分析任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const task = analysisTasks.value[index];
    console.log('删除任务:', task);

    // 调用删除API
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/tasks/${task.task_id}/`;

    const response = await fetch(apiUrl, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'success') {
      analysisTasks.value.splice(index, 1);
      ElMessage.success('任务删除成功');
    } else {
      throw new Error(data.message || '删除任务失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error);
      ElMessage.error('删除任务失败');
    }
  }
};

const handleTaskConfigConfirm = async (configData: any) => {
  try {
    console.log('提交任务配置:', configData);

    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/tasks/`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(configData.config)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'success') {
      ElMessage.success('任务创建成功');
      fetchTaskInfo(); // 刷新任务列表
    } else {
      throw new Error(data.message || '创建任务失败');
    }
  } catch (error) {
    console.error('创建任务失败:', error);
    ElMessage.error('创建任务失败');
  }
};

const handleClose = () => {
  visible.value = false;
};

const handleExport = () => {
  ElMessage.info('导出功能开发中');
};
</script>

<style scoped lang="scss">
.analysis-detail {
  display: flex;
  gap: 20px;
  height: 600px;

  .left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: hidden;
  }

  .right-panel {
    flex: 1;
    overflow: hidden;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>