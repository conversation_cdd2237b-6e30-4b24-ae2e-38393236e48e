/**
 * WMS图层加载示例
 * 展示如何在分析详情页面中加载WMS图层
 */

// 示例WMS图层配置对象
export const exampleWMSLayerConfig = {
  "defaultStyle": "flowtype",
  "id": "arableLand:20250705171601_2_1755828168",
  "name": "20250705171601_2_1755828168",
  "type": "raster",
  "protocol": "WMS",
  "workspace": "arableLand",
  "layerName": "20250705171601_2_1755828168",
  "opacity": 0.8,
  "zIndex": 2
};

// 使用随机样式的WMS图层配置
export const randomStyleWMSConfig = {
  "defaultStyle": "Random",
  "id": "testodm:20250705171601",
  "name": "测试图层",
  "type": "raster",
  "protocol": "WMS",
  "workspace": "testodm",
  "layerName": "20250705171601",
  "opacity": 1.0,
  "zIndex": 1
};

// 使用指定样式的WMS图层配置
export const customStyleWMSConfig = {
  "defaultStyle": "Blue",
  "id": "geoserver:example_layer",
  "name": "示例图层",
  "type": "raster",
  "protocol": "WMS",
  "workspace": "geoserver",
  "layerName": "example_layer",
  "opacity": 0.7,
  "zIndex": 3
};

/**
 * 在Vue组件中使用WMS图层加载的示例代码：
 * 
 * ```typescript
 * import { ref } from 'vue';
 * import { exampleWMSLayerConfig } from './examples/wmsLayerExample';
 * 
 * // 获取分析详情对话框的引用
 * const analysisDetailRef = ref();
 * 
 * // 加载WMS图层的方法
 * const loadExampleWMSLayer = () => {
 *   if (analysisDetailRef.value) {
 *     analysisDetailRef.value.loadWMSLayer(exampleWMSLayerConfig);
 *   }
 * };
 * ```
 * 
 * 在模板中：
 * ```vue
 * <template>
 *   <AnalysisDetailDialog 
 *     ref="analysisDetailRef"
 *     :visible="dialogVisible"
 *     :task="currentTask"
 *   />
 *   <el-button @click="loadExampleWMSLayer">加载WMS图层</el-button>
 * </template>
 * ```
 */

// 支持的样式列表
export const availableStyles = [
  "Blue", "Brown", "Cyan", "Gold", "Gray", "Green", "Lime", 
  "Magenta", "Navy", "Olive", "Orange", "Pink", "Purple", 
  "Red", "Teal", "White", "Yellow", "Coral", "Tomato"
];

// WMS图层配置接口定义
export interface WMSLayerConfig {
  /** 图层ID */
  id: string;
  /** 图层名称 */
  name: string;
  /** 图层类型，固定为 'raster' */
  type: 'raster';
  /** 协议类型，固定为 'WMS' */
  protocol: 'WMS';
  /** GeoServer工作空间名称 */
  workspace: string;
  /** 图层名称 */
  layerName: string;
  /** 默认样式，可以是具体样式名或 'Random' */
  defaultStyle?: string;
  /** 图层透明度，0-1之间 */
  opacity?: number;
  /** 图层层级 */
  zIndex?: number;
}

/**
 * 创建WMS图层配置的工厂函数
 * @param workspace GeoServer工作空间
 * @param layerName 图层名称
 * @param options 可选配置
 * @returns WMS图层配置对象
 */
export function createWMSLayerConfig(
  workspace: string, 
  layerName: string, 
  options: Partial<WMSLayerConfig> = {}
): WMSLayerConfig {
  return {
    id: `${workspace}:${layerName}`,
    name: layerName,
    type: 'raster',
    protocol: 'WMS',
    workspace,
    layerName,
    defaultStyle: 'Random',
    opacity: 1.0,
    zIndex: 1,
    ...options
  };
}
