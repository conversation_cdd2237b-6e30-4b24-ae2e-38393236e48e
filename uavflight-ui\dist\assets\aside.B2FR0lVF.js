const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.D-A8WGHf.js","assets/pigx-app.DmHLWGl6.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/index.q5pfyk-F.css","assets/vertical.C9lm88-7.js"])))=>i.map(i=>d[i]);
import{v as O,t as P,u as V,w as H,e as o,x as z,a as g,__tla as N}from"./index.BSP3cg_z.js";import{d as F,k as G,s as m,A as J,c as S,h as K,w,B as A,q as Q,x as U,u as y,a as X,b as x,t as f,v as E,e as Y,D as Z,j as M,g as $}from"./vue.CnN__PXn.js";let W,ee=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{let h,p;h={class:"h100"},p=F({name:"layoutAside"}),W=F({...p,setup(te){const R=M(()=>g(()=>import("./index.D-A8WGHf.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3,4,5]))),k=M(()=>g(()=>import("./vertical.C9lm88-7.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([6,2,3,4]))),{locale:_}=O.useI18n(),u=G(),v=P(),D=V(),I=H(),{routesList:T}=m(v),{themeConfig:s}=m(D),{isTagsViewCurrenFull:j}=m(I),l=J({menuList:[],clientWidth:0}),q=S(()=>{const{layout:e,isCollapse:t,menuBar:a}=s.value,i=["#FFFFFF","#FFF","#fff","#ffffff"].includes(a)?"layout-el-aside-br-color":"";if(l.clientWidth<=1e3){if(t){document.body.setAttribute("class","el-popup-parent--hidden");const c=document.querySelector(".layout-container"),d=document.createElement("div");return d.setAttribute("class","layout-aside-mobile-mode"),c.appendChild(d),d.addEventListener("click",n),[i,"layout-aside-mobile","layout-aside-mobile-open"]}return n(),[i,"layout-aside-mobile","layout-aside-mobile-close"]}return[i,e==="columns"?t?"layout-aside-pc-1":_.value==="en"?"layout-aside-pc-250":"layout-aside-pc-220":t?"layout-aside-pc-64":_.value==="en"?"layout-aside-pc-250":"layout-aside-pc-220"]}),B=S(()=>{let{layout:e,isShowLogo:t}=s.value;return t&&e==="defaults"||t&&e==="columns"}),n=()=>{const e=document.querySelector(".layout-aside-mobile-mode");e==null||e.setAttribute("style","animation: error-img-two 0.3s"),setTimeout(()=>{var t;(t=e==null?void 0:e.parentNode)==null||t.removeChild(e)},300),document.body.clientWidth<1e3&&(s.value.isCollapse=!1),document.body.setAttribute("class","")},r=()=>{if(s.value.layout==="columns")return!1;l.menuList=b(T.value)},b=e=>e.filter(t=>{var a;return!((a=t.meta)!=null&&a.isHide)}).map(t=>((t=Object.assign({},t)).children&&(t.children=b(t.children)),t)),C=e=>{l.clientWidth=e},L=e=>{let{layout:t}=s.value;if(t!=="columns")return!1;e||o.emit("restoreDefault"),v.setColumnsMenuHover(e)};return K(()=>{C(document.body.clientWidth),r(),o.on("setSendColumnsChildren",e=>{l.menuList=e.children}),o.on("setSendClassicChildren",e=>{let{layout:t,isClassicSplitMenu:a}=s.value;t==="classic"&&a&&(l.menuList=[],l.menuList=e.children)}),o.on("getBreadcrumbIndexSetFilterRoutes",()=>{r()}),o.on("layoutMobileResize",e=>{C(e.clientWidth),n()})}),w(s.value,e=>{e.isShowLogoChange!==e.isShowLogo&&u.value&&u.value.update()}),w(z.state,e=>{let{layout:t,isClassicSplitMenu:a}=e.themeConfig.themeConfig;if(t==="classic"&&a)return!1;r()},{deep:!0}),(e,t)=>{const a=A("el-scrollbar"),i=A("el-aside");return Q((x(),X("div",h,[f(i,{class:$(["layout-aside",q.value])},{default:E(()=>[B.value?(x(),Y(y(R),{key:0})):Z("",!0),f(a,{class:"flex-auto",ref_key:"layoutAsideScrollbarRef",ref:u,onMouseenter:t[0]||(t[0]=c=>L(!0)),onMouseleave:t[1]||(t[1]=c=>L(!1))},{default:E(()=>[f(y(k),{menuList:l.menuList},null,8,["menuList"])]),_:1},512)]),_:1},8,["class"])],512)),[[U,!y(j)]])}}})});export{ee as __tla,W as default};
