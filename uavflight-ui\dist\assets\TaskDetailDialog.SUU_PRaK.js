import{a2 as F,E as G,q as N,__tla as P}from"./index.BSP3cg_z.js";import{d as $,k as r,c as H,w as E,B as c,e as L,b as u,v as y,f as o,t as k,a as f,F as J,p as K,E as M,G as O}from"./vue.CnN__PXn.js";let S,Q=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{let _,b,h,I,w,V,D,x;_={class:"task-detail"},b={class:"select-container"},h={class:"content-box"},I={class:"content-header"},w={class:"content-body"},V={key:0,class:"loading-content"},D={key:1,class:"empty-content"},x={key:2,class:"task-info-content"},S=N($({name:"TaskDetailDialog",props:{visible:{type:<PERSON><PERSON>an,default:!1},taskId:{type:String,default:""},taskList:{type:Array,default:()=>[]},processName:{type:String,default:""},processType:{type:String,default:""}},emits:["update:visible","refresh"],setup(T,{emit:B}){const t=T,C=B,v=r(!1),m=r(""),n=r(""),d=r(""),p=r(!1),U=H(()=>{if(!t.taskList||!t.taskId)return[];const a=[...t.taskList],e=a.findIndex(l=>l===t.taskId);if(e>0){const l=a.splice(e,1)[0];a.unshift(l)}return a.map(l=>({value:l,label:l===t.taskId?`${l} (\u5F53\u524D\u6267\u884C)`:l}))});E(()=>t.visible,a=>{v.value=a,a&&t.taskId&&(m.value=t.taskId,n.value=t.taskId,g())}),E(()=>v.value,a=>{C("update:visible",a)});const q=()=>{m.value=n.value,g()},g=async()=>{var a;if(n.value){p.value=!0,d.value="";try{const e={odm_process:"batlog",geoserver_publish:"geolog",tif_process:"tiflog",map_config:"configlog"}[t.processType]||"batlog",l=n.value,i=await F.get("http://192.168.43.148:8091/api/map/logs/",{params:{log_type:e,log_id:l}});if(!i.data||i.data.status!=="success")throw new Error(((a=i.data)==null?void 0:a.message)||"\u83B7\u53D6\u65E5\u5FD7\u5931\u8D25");d.value=i.data.content||"\u6682\u65E0\u65E5\u5FD7\u4FE1\u606F"}catch{d.value="\u83B7\u53D6\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",G.error("\u83B7\u53D6\u65E5\u5FD7\u4FE1\u606F\u5931\u8D25")}finally{p.value=!1}}};return(a,e)=>{const l=c("el-option"),i=c("el-select"),j=c("el-button"),z=c("el-skeleton"),A=c("el-dialog");return u(),L(A,{modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=s=>v.value=s),title:"\u4EFB\u52A1\u8BE6\u60C5: "+m.value,width:"60%","destroy-on-close":""},{default:y(()=>[o("div",_,[o("div",b,[e[2]||(e[2]=o("span",{class:"label"},"\u9009\u62E9\u4EFB\u52A1ID:",-1)),k(i,{modelValue:n.value,"onUpdate:modelValue":e[0]||(e[0]=s=>n.value=s),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1ID",onChange:q},{default:y(()=>[(u(!0),f(J,null,K(U.value,s=>(u(),L(l,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",h,[o("div",I,[e[4]||(e[4]=o("span",null,"\u4EFB\u52A1\u65E5\u5FD7:",-1)),k(j,{type:"primary",size:"small",loading:p.value,onClick:g},{default:y(()=>e[3]||(e[3]=[M(" \u5237\u65B0 ")])),_:1},8,["loading"])]),o("div",w,[p.value?(u(),f("div",V,[k(z,{rows:10,animated:""})])):d.value?(u(),f("pre",x,O(d.value),1)):(u(),f("div",D," \u6682\u65E0\u4EFB\u52A1\u8BE6\u7EC6\u4FE1\u606F\uFF0C\u8BF7\u70B9\u51FB\u5237\u65B0\u6309\u94AE\u83B7\u53D6 "))])])])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-d9ecb5c6"]])});export{Q as __tla,S as default};
