const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.LdY_vOL4.js","assets/sortable.esm.BGML4dzN.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/index._dMqQDxz.css","assets/add-dict.BIErq55x.js","assets/dict.D9OX-VAS.js","assets/dict.DrX0Qdnc.js"])))=>i.map(i=>d[i]);
import{a as O,__tla as al}from"./index.BSP3cg_z.js";import{d as ol,k as n,A as _,o as tl,y as ul,B as b,a as v,b as r,t as a,v as t,u as i,j as B,H as D,F as f,p as h,e as y,Q as dl}from"./vue.CnN__PXn.js";import{u as rl,j as il,k as ml,__tla as pl}from"./table.BExdFBu3.js";import{l as nl,__tla as bl}from"./fieldtype.CAArssoY.js";import{S as vl}from"./sortable.esm.BGML4dzN.js";let H,fl=Promise.all([(()=>{try{return al}catch{}})(),(()=>{try{return pl}catch{}})(),(()=>{try{return bl}catch{}})()]).then(async()=>{H=ol({__name:"edit",props:{tableName:{type:String},dsName:{type:String}},setup(M,{expose:Q}){const x=B(()=>O(()=>import("./index.LdY_vOL4.js").then(async u=>(await u.__tla,u)),__vite__mapDeps([0,1,2,3,4,5]))),z=B(()=>O(()=>import("./add-dict.BIErq55x.js").then(async u=>(await u.__tla,u)),__vite__mapDeps([6,2,3,4,7,8]))),T=n(),N=n(""),S=M,G=n(!1),J=n(),q=n(),U=n([]),A=n([]),F=n(),k=n(),g=n(),m=n([]),K=_([{label:"DEFAULT",value:"DEFAULT"},{label:"INSERT",value:"INSERT"},{label:"UPDATE",value:"UPDATE"},{label:"INSERT_UPDATE",value:"INSERT_UPDATE"}]),W=_([{label:"=",value:"="},{label:"!=",value:"!="},{label:">",value:">"},{label:">=",value:">="},{label:"<",value:"<"},{label:"<=",value:"<="},{label:"like",value:"like"},{label:"left like",value:"left like"},{label:"right like",value:"right like"}]),X=_([{label:"\u5355\u884C\u6587\u672C",value:"text"},{label:"\u591A\u884C\u6587\u672C",value:"textarea"},{label:"\u6570\u5B57",value:"number"},{label:"\u5BCC\u6587\u672C\u7F16\u8F91\u5668",value:"editor"},{label:"\u4E0B\u62C9\u6846",value:"select"},{label:"\u5355\u9009\u6309\u94AE",value:"radio"},{label:"\u590D\u9009\u6846",value:"checkbox"},{label:"\u65E5\u671F",value:"date"},{label:"\u65E5\u671F\u65F6\u95F4",value:"datetime"},{label:"\u6587\u4EF6\u4E0A\u4F20",value:"upload-file"},{label:"\u56FE\u7247\u4E0A\u4F20",value:"upload-img"}]),Y=_([{label:"\u5355\u884C\u6587\u672C",value:"text"},{label:"\u591A\u884C\u6587\u672C",value:"textarea"},{label:"\u6570\u5B57",value:"number"},{label:"\u4E0B\u62C9\u6846",value:"select"},{label:"\u5355\u9009\u6309\u94AE",value:"radio"},{label:"\u590D\u9009\u6846",value:"checkbox"},{label:"\u65E5\u671F",value:"date"},{label:"\u65E5\u671F\u65F6\u95F4",value:"datetime"}]),Z=_([{label:"\u53BB\u91CD",value:"duplicate"},{label:"\u6570\u5B57",value:"number"},{label:"\u5B57\u6BCD",value:"letter"},{label:"\u5B57\u6BCD\u548C\u6570\u5B57",value:"letterAndNumber"},{label:"\u624B\u673A\u53F7\u7801",value:"mobilePhone"},{label:"\u5B57\u6BCD\u5F00\u5934\uFF0C\u4EC5\u53EF\u5305\u542B\u6570\u5B57",value:"letterStartNumberIncluded"},{label:"\u7981\u6B62\u4E2D\u6587\u8F93\u5165",value:"noChinese"},{label:"\u5FC5\u987B\u4E2D\u6587\u8F93\u5165",value:"chinese"},{label:"\u7535\u5B50\u90AE\u7BB1",value:"email"},{label:"URL\u7F51\u5740",value:"url"}]),P=_({tinyint:"number",smallint:"number",mediumint:"number",int:"number",integer:"number",bigint:"number",float:"number",datetime:"datetime",LocalDateTime:"datetime",date:"date",LocalDate:"date",Long:"number",Float:"number",Double:"number",BigDecimal:"number",text:"textarea",String:"text",longtext:"editor",bit:"radio",Boolean:"radio",char:"radio",varchar:"text"}),$=async u=>{await I(),F.value.fieldDict=u};tl(()=>{g.value=String(S.tableName),k.value=String(S.dsName),T.value="field",R(),L(k.value,g.value),j(),I()});const R=()=>{ul(()=>{const u=window.document.querySelector("#pane-field");J.value=vl.create(u,{handle:".drag-btn",onEnd:d=>{const{newIndex:p,oldIndex:o}=d,w=m.value.splice(o,1)[0];m.value.splice(p,0,w)}})})},C=n(!1),L=(u,d)=>{m.value=[],C.value=!0,rl(u,d).then(p=>{N.value=p.data.id,m.value=p.data.fieldList.map(o=>(o.queryFormType?o.queryFormType:P[o.fieldType],o.formType?o.formType:P[o.fieldType],o))}).finally(()=>{C.value=!1})},j=async()=>{U.value=[];const{data:u}=await nl(),d=new Map;u.forEach(p=>{const{attrType:o,columnType:w}=p;d.has(o)||(d.set(o,w),U.value.push({label:o,value:o}))}),U.value.push({label:"Object",value:"Object"})},I=()=>{il().then(u=>{for(const d of u.data)A.value.push({label:d.description,value:d.dictType})})};return Q({openDialog:(u,d)=>{G.value=!0,g.value=d,k.value=u,T.value="field",R(),L(u,d),j(),I()},submitHandle:()=>new Promise(u=>{ml(k.value,g.value,m.value).then(()=>{u(N.value)})})}),(u,d)=>{const p=b("el-checkbox"),o=b("el-table-column"),w=b("el-input"),s=b("el-option"),V=b("el-select"),ll=b("el-button"),E=b("el-tab-pane"),el=b("el-tabs");return r(),v(f,null,[a(el,{modelValue:i(T),"onUpdate:modelValue":d[3]||(d[3]=l=>D(T)?T.value=l:null)},{default:t(()=>[a(E,{label:"\u5C5E\u6027\u8BBE\u7F6E",name:"field"},{default:t(()=>[a(i(x),{ref:"fieldTable",modelValue:i(m),"onUpdate:modelValue":d[0]||(d[0]=l=>D(m)?m.value=l:null),hideAdd:!0,hideDelete:!0,"drag-sort":"",placeholder:"\u6682\u65E0\u6570\u636E"},{default:t(()=>[a(o,{label:"\u4E3B\u952E",prop:"primaryPk",width:"80","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.primaryPk,"onUpdate:modelValue":e=>l.primaryPk=e,"true-value":"1","false-value":"0",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(o,{label:"\u5B57\u6BB5\u540D",prop:"fieldName","show-overflow-tooltip":""}),a(o,{label:"\u8BF4\u660E",prop:"fieldComment","show-overflow-tooltip":""},{default:t(({row:l})=>[a(w,{modelValue:l.fieldComment,"onUpdate:modelValue":e=>l.fieldComment=e,placeholder:"\u8BF7\u8F93\u5165\u8BF4\u660E"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(o,{label:"\u5B57\u6BB5\u7C7B\u578B",prop:"fieldType","show-overflow-tooltip":""}),a(o,{label:"\u5C5E\u6027\u540D",prop:"attrName","show-overflow-tooltip":""},{default:t(({row:l})=>[a(w,{modelValue:l.attrName,"onUpdate:modelValue":e=>l.attrName=e,placeholder:"\u8BF7\u8F93\u5165\u5C5E\u6027\u540D"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(o,{label:"\u5C5E\u6027\u7C7B\u578B",prop:"attrType","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.attrType,"onUpdate:modelValue":e=>l.attrType=e,placeholder:"\u8BF7\u9009\u62E9\u5C5E\u6027\u7C7B\u578B",onChange:e=>(c=>{c.queryFormType=P[c.attrType],c.formType=P[c.attrType]})(l)},{default:t(()=>[(r(!0),v(f,null,h(i(U),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(o,{label:"\u81EA\u52A8\u586B\u5145",prop:"autoFill","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.autoFill,"onUpdate:modelValue":e=>l.autoFill=e,placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B"},{default:t(()=>[(r(!0),v(f,null,h(i(K),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(o,{label:"\u5B57\u5178\u540D\u79F0",prop:"fieldDict","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.fieldDict,"onUpdate:modelValue":e=>l.fieldDict=e,placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",filterable:"",clearable:"",disabled:l.primaryPk==="1"},{prefix:t(()=>[a(ll,{icon:"Plus",type:"primary",link:"",onClick:dl(e=>(c=>{F.value=c,q.value.openDialog()})(l),["stop"])},null,8,["onClick"])]),default:t(()=>[(r(!0),v(f,null,h(i(A),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(E,{label:"\u5217\u8868\u67E5\u8BE2",name:"third"},{default:t(()=>[a(i(x),{ref:"gridTable",modelValue:i(m),"onUpdate:modelValue":d[1]||(d[1]=l=>D(m)?m.value=l:null),hideAdd:!0,hideDelete:!0,placeholder:"\u6682\u65E0\u6570\u636E"},{default:t(()=>[a(o,{label:"\u5C5E\u6027\u540D",prop:"attrName","show-overflow-tooltip":""}),a(o,{label:"\u8BF4\u660E",prop:"fieldComment","show-overflow-tooltip":""}),a(o,{label:"\u5217\u8868\u663E\u793A",prop:"gridItem",width:"100","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.gridItem,"onUpdate:modelValue":e=>l.gridItem=e,"true-value":"1","false-value":"0",disabled:l.primaryPk==="1"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u662F\u5426\u6392\u5E8F",prop:"gridSort",width:"100","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.gridSort,"onUpdate:modelValue":e=>l.gridSort=e,"true-value":"1","false-value":"0",disabled:l.primaryPk==="1"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u67E5\u8BE2\u663E\u793A",prop:"gridSort",width:"100","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.queryItem,"onUpdate:modelValue":e=>l.queryItem=e,"true-value":"1","false-value":"0",disabled:l.primaryPk==="1"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u67E5\u8BE2\u8868\u5355\u7C7B\u578B",prop:"queryFormType","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.queryFormType,"onUpdate:modelValue":e=>l.queryFormType=e,placeholder:"\u8BF7\u9009\u62E9\u67E5\u8BE2\u8868\u5355\u7C7B\u578B",disabled:l.primaryPk==="1"},{default:t(()=>[(r(!0),v(f,null,h(i(Y),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u67E5\u8BE2\u65B9\u5F0F",prop:"queryType","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.queryType,"onUpdate:modelValue":e=>l.queryType=e,placeholder:"\u8BF7\u9009\u62E9\u67E5\u8BE2\u65B9\u5F0F",disabled:l.primaryPk==="1"},{default:t(()=>[(r(!0),v(f,null,h(i(W),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(E,{label:"\u8868\u5355\u9875\u9762",name:"form"},{default:t(()=>[a(i(x),{ref:"formTable",modelValue:i(m),"onUpdate:modelValue":d[2]||(d[2]=l=>D(m)?m.value=l:null),hideAdd:!0,hideDelete:!0,placeholder:"\u6682\u65E0\u6570\u636E"},{default:t(()=>[a(o,{label:"\u5C5E\u6027\u540D",prop:"attrName","show-overflow-tooltip":""}),a(o,{label:"\u8BF4\u660E",prop:"fieldComment","show-overflow-tooltip":""}),a(o,{label:"\u8868\u5355\u7C7B\u578B",prop:"formType","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.formType,"onUpdate:modelValue":e=>l.formType=e,placeholder:"\u8BF7\u9009\u62E9\u8868\u5355\u7C7B\u578B",disabled:l.primaryPk==="1"},{default:t(()=>[(r(!0),v(f,null,h(i(X),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u662F\u5426\u663E\u793A",prop:"formItem",width:"100","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.formItem,"onUpdate:modelValue":e=>l.formItem=e,"true-value":"1","false-value":"0",disabled:l.primaryPk==="1"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u8868\u5355\u5FC5\u586B",prop:"formRequired",width:"100","show-overflow-tooltip":""},{default:t(({row:l})=>[a(p,{modelValue:l.formRequired,"onUpdate:modelValue":e=>l.formRequired=e,"true-value":"1","false-value":"0",disabled:l.primaryPk==="1"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(o,{label:"\u8868\u5355\u6548\u9A8C",prop:"formValidator","show-overflow-tooltip":""},{default:t(({row:l})=>[a(V,{modelValue:l.formValidator,"onUpdate:modelValue":e=>l.formValidator=e,placeholder:"\u8BF7\u9009\u62E9\u8868\u5355\u6548\u9A8C",disabled:l.primaryPk==="1"},{default:t(()=>[(r(!0),v(f,null,h(i(Z),e=>(r(),y(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["modelValue"]),a(i(z),{ref_key:"formDialogRef",ref:q,onRefresh:$},null,512)],64)}}})});export{fl as __tla,H as default};
