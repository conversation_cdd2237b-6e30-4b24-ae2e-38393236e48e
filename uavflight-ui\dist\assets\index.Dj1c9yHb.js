const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/form.D8lD-R-x.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/datasource.DryyxFrZ.js","assets/dict.DrX0Qdnc.js","assets/dict.D9OX-VAS.js"])))=>i.map(i=>d[i]);
import{v as P,a as O,f as M,d as W,c as N,__tla as X}from"./index.BSP3cg_z.js";import{d as q,k as f,A as Y,B as r,m as Z,a as ee,b as F,f as R,t as l,q as L,x as le,u as e,v as o,I as ae,E as u,G as m,H as te,e as oe,J as ne,j as re}from"./vue.CnN__PXn.js";import{u as se,__tla as ce}from"./table.CCFM44Zd.js";import{f as de,d as ie,__tla as ue}from"./datasource.DryyxFrZ.js";let V,me=Promise.all([(()=>{try{return X}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{let b,g,w,C;b={class:"layout-padding"},g={class:"layout-padding-auto layout-padding-view"},w={class:"mb8",style:{width:"100%"}},C=q({name:"systemDatasourceConf"}),V=q({...C,setup(pe){const z=re(()=>O(()=>import("./form.D8lD-R-x.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4,5,6]))),{t:s}=P.useI18n(),h=f(),v=f(),y=f(!0),k=f([]),$=f(!0),d=Y({queryForm:{},pageList:de,descs:["create_time"]}),{getDataList:p,currentChangeHandle:E,sizeChangeHandle:H,tableStyle:x}=se(d),I=()=>{v.value.resetFields(),p()},j=a=>{k.value=a.map(({id:t})=>t),$.value=!a.length},S=async a=>{try{await W().confirm(s("common.delConfirmText"))}catch{return}try{await ie(a),p(),N().success(s("common.delSuccessText"))}catch(t){N().error(t.msg)}};return(a,t)=>{const A=r("el-input"),T=r("el-form-item"),i=r("el-button"),K=r("el-form"),B=r("el-row"),Q=r("right-toolbar"),c=r("el-table-column"),U=r("el-table"),G=r("pagination"),J=Z("loading");return F(),ee("div",b,[R("div",g,[L(l(B,{class:"ml10"},{default:o(()=>[l(K,{inline:!0,model:e(d).queryForm,onKeyup:ae(e(p),["enter"]),ref_key:"queryRef",ref:v},{default:o(()=>[l(T,{label:a.$t("datasourceconf.dsName"),prop:"dsName"},{default:o(()=>[l(A,{placeholder:a.$t("datasourceconf.inputdsNameTip"),modelValue:e(d).queryForm.dsName,"onUpdate:modelValue":t[0]||(t[0]=n=>e(d).queryForm.dsName=n)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),l(T,null,{default:o(()=>[l(i,{onClick:e(p),icon:"search",type:"primary"},{default:o(()=>[u(m(a.$t("common.queryBtn")),1)]),_:1},8,["onClick"]),l(i,{onClick:I,icon:"Refresh"},{default:o(()=>[u(m(a.$t("common.resetBtn")),1)]),_:1})]),_:1})]),_:1},8,["model","onKeyup"])]),_:1},512),[[le,e(y)]]),l(B,null,{default:o(()=>[R("div",w,[l(i,{onClick:t[1]||(t[1]=n=>e(h).openDialog()),class:"ml10",icon:"folder-add",type:"primary"},{default:o(()=>[u(m(a.$t("common.addBtn")),1)]),_:1}),l(i,{disabled:e($),onClick:t[2]||(t[2]=n=>S(e(k))),class:"ml10",icon:"Delete",type:"primary"},{default:o(()=>[u(m(a.$t("common.delBtn")),1)]),_:1},8,["disabled"]),l(Q,{onQueryTable:e(p),class:"ml10",style:{float:"right","margin-right":"20px"},showSearch:e(y),"onUpdate:showSearch":t[3]||(t[3]=n=>te(y)?y.value=n:null)},null,8,["onQueryTable","showSearch"])])]),_:1}),L((F(),oe(U,{data:e(d).dataList,onSelectionChange:j,style:{width:"100%"},border:"","cell-style":e(x).cellStyle,"header-cell-style":e(x).headerCellStyle},{default:o(()=>[l(c,{align:"center",type:"selection",width:"40"}),l(c,{label:e(s)("datasourceconf.index"),type:"index",width:"60"},null,8,["label"]),l(c,{label:e(s)("datasourceconf.name"),prop:"name","show-overflow-tooltip":""},null,8,["label"]),l(c,{label:e(s)("datasourceconf.dsName"),prop:"dsName","show-overflow-tooltip":""},null,8,["label"]),l(c,{label:e(s)("datasourceconf.dsType"),prop:"dsType","show-overflow-tooltip":""},null,8,["label"]),l(c,{label:e(s)("datasourceconf.username"),prop:"username","show-overflow-tooltip":""},null,8,["label"]),l(c,{label:e(s)("datasourceconf.createTime"),prop:"createTime","show-overflow-tooltip":""},null,8,["label"]),l(c,{label:a.$t("common.action"),width:"250"},{default:o(n=>[l(i,{icon:"document",onClick:D=>{return _=n.row.name,void M("/gen/dsconf/doc",{dsName:_},`${_}.html`);var _},text:"",type:"primary"},{default:o(()=>[u(m(a.$t("datasourceconf.docBtn")),1)]),_:2},1032,["onClick"]),l(i,{icon:"edit",onClick:D=>e(h).openDialog(n.row.id),text:"",type:"primary"},{default:o(()=>[u(m(a.$t("common.editBtn")),1)]),_:2},1032,["onClick"]),l(i,{icon:"delete",onClick:D=>S([n.row.id]),text:"",type:"primary"},{default:o(()=>[u(m(a.$t("common.delBtn")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[J,e(d).loading]]),l(G,ne({onCurrentChange:e(E),onSizeChange:e(H)},e(d).pagination),null,16,["onCurrentChange","onSizeChange"])]),l(e(z),{onRefresh:t[4]||(t[4]=n=>e(p)()),ref_key:"formDialogRef",ref:h},null,512)])}}})});export{me as __tla,V as default};
