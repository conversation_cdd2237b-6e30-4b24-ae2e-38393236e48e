import{P as ca,a3 as da,E as y,X as ma,Y as pa,$ as fa,a1 as ga,a0 as ya,Q as V,ai as te,a2 as Ze,U as de,W as me,q as ha,__tla as wa}from"./index.BSP3cg_z.js";import{d as ba,k as o,c as Ae,w as G,S as Ue,o as xa,B as E,e as pe,b as k,v as _,f as v,a as X,D as La,t as w,u as qe,E as j,G as Oe,F as Pe,p as Be,n as Ge,y as _a}from"./vue.CnN__PXn.js";let Xe,Ca=Promise.all([(()=>{try{return wa}catch{}})()]).then(async()=>{let fe,ge,ye,he,we,be,xe,Le,_e,<PERSON>,Ve,$e,Ee,Se,ze,ke,Ie,Te;fe={class:"dialog-header"},ge={class:"mode-toggle"},ye={class:"map-compare-container"},he={class:"content-wrapper"},we={key:0,class:"loading-overlay"},be={class:"layer-selection"},xe={class:"layer-select"},Le={class:"layer-header"},_e={class:"search-input"},Ce={class:"spatial-search-btn"},Ve={class:"divider-controls"},$e={class:"layer-select"},Ee={class:"layer-header"},Se={class:"search-input"},ze={class:"spatial-search-btn"},ke={class:"map-container"},Ie={key:0,class:"maps-container split-mode"},Te={key:1,class:"maps-container swipe-mode"},Xe=ha(ba({name:"MapCompareDialog",props:{visible:{type:Boolean,default:!1},initialLayerId:{type:String,default:""}},emits:["update:visible"],setup(je,{emit:Je}){const J=[],I=new globalThis.Map;function Ke(e,a){const t=e.getImage(),l=function(i){return i.getTileCoord().join("/")}(e);let u=!1;t.onload=()=>{u=!0;const i=I.get(l);i&&clearTimeout(i),I.delete(l),e.setState(te.LOADED)},t.onerror=()=>{u=!0;const i=I.get(l);i&&clearTimeout(i),I.delete(l),e.setState(te.ERROR)};const n=setTimeout(()=>{u||e.getState()!==te.LOADING||(J.push(l),e.setState(te.ERROR))},3e3);I.set(l,n),t.src=a}const q=je,Ne=Je,D=o(!1),$=o(!1),K=o(!1),b=o(null),C=o(null),d=o(null),m=o(null);o(null);const ne=o(null),R=o(null),F=o(null),s=o(null),Z=o([]),p=o(""),h=o(""),x=o(50),g=o("split"),ue=o(!1),se=o(null),N=o(null),A=o(""),U=o(""),S=o(!1),z=o(!1),Q=o([]),W=o([]),Y=o(!1),H=o(!1),Qe=Ae(()=>{let e=Z.value;if(S.value&&Q.value.length>0&&(e=Z.value.filter(t=>Q.value.includes(t.id))),!A.value)return e;const a=A.value.toLowerCase();return e.filter(t=>t.id.toLowerCase().includes(a)||t.date.toLowerCase().includes(a))}),We=Ae(()=>{let e=Z.value;if(z.value&&W.value.length>0&&(e=Z.value.filter(t=>W.value.includes(t.id))),!U.value)return e;const a=U.value.toLowerCase();return e.filter(t=>t.id.toLowerCase().includes(a)||t.date.toLowerCase().includes(a))}),Ye=()=>{},He=()=>{},Me=async(e,a)=>{var t;try{const l="http://**************:8091/api/layer_intersections",u=await Ze.get(l,{params:{workspace:e,layer:a}});if(u.data&&u.data.status==="success")return u.data.intersecting_layers.map(i=>i.id).filter(i=>Z.value.some(c=>c.id===i));throw new Error(((t=u.data)==null?void 0:t.message)||"\u67E5\u8BE2\u56FE\u5C42\u76F8\u4EA4\u4FE1\u606F\u5931\u8D25")}catch{return y.error("\u7A7A\u95F4\u641C\u7D22\u67E5\u8BE2\u5931\u8D25"),[]}},ea=async()=>{if(S.value=!S.value,S.value)if(h.value){const e=h.value.split(":");if(e.length===2){const a=e[0],t=e[1];Y.value=!0;try{const l=await Me(a,t);Q.value=l,l.length>0?y.success(`\u627E\u5230 ${l.length} \u4E2A\u76F8\u4EA4\u56FE\u5C42`):y.info("\u672A\u627E\u5230\u76F8\u4EA4\u7684\u56FE\u5C42")}catch{}finally{Y.value=!1}}else y.warning("\u56FE\u5C42ID\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u8FDB\u884C\u7A7A\u95F4\u641C\u7D22")}else y.warning("\u8BF7\u5148\u9009\u62E9\u53F3\u4FA7\u56FE\u5C42\u518D\u8FDB\u884C\u7A7A\u95F4\u641C\u7D22"),S.value=!1;else Q.value=[],Y.value=!1},aa=async()=>{if(z.value=!z.value,z.value)if(p.value){const e=p.value.split(":");if(e.length===2){const a=e[0],t=e[1];H.value=!0;try{const l=await Me(a,t);W.value=l,l.length>0?y.success(`\u627E\u5230 ${l.length} \u4E2A\u76F8\u4EA4\u56FE\u5C42`):y.info("\u672A\u627E\u5230\u76F8\u4EA4\u7684\u56FE\u5C42")}catch{}finally{H.value=!1}}else y.warning("\u56FE\u5C42ID\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u8FDB\u884C\u7A7A\u95F4\u641C\u7D22")}else y.warning("\u8BF7\u5148\u9009\u62E9\u5DE6\u4FA7\u56FE\u5C42\u518D\u8FDB\u884C\u7A7A\u95F4\u641C\u7D22"),z.value=!1;else W.value=[],H.value=!1};G(()=>q.visible,e=>{D.value=e,e&&setTimeout(()=>{g.value==="split"?ie():oe(),De(),q.initialLayerId&&(p.value=q.initialLayerId,M())},300)}),G(()=>g.value,e=>{le(),setTimeout(()=>{e==="split"?ie():oe(),(p.value||h.value)&&M()},100)}),G(()=>x.value,e=>{if(g.value==="split")b.value&&C.value&&(b.value.style.width=`${e}%`,C.value.style.width=100-e+"%",d.value&&d.value.updateSize(),m.value&&m.value.updateSize());else if(s.value&&(T(),se.value))try{s.value.render(),setTimeout(ae,200)}catch{ae()}},{flush:"post"}),G(()=>D.value,e=>{Ne("update:visible",e),e?setTimeout(()=>{g.value==="split"?ie():oe(),De(),q.initialLayerId&&(p.value=q.initialLayerId,M()),setTimeout(()=>{s.value&&(s.value.updateSize(),T(),s.value.render())},300)},300):le()});const De=async()=>{var e;K.value=!0;try{const a="**************",t="8091",l=await Ze.get(`http://${a}:${t}/api/map/odm/tasks`);if(!l.data||l.data.status!=="success"||!l.data.tasks)throw new Error(((e=l.data)==null?void 0:e.message)||"\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25");{const u=l.data.tasks.filter(n=>n.status==="\u5B8C\u6210").map(n=>{var i,c,f,L;return{id:((c=(i=n.details)==null?void 0:i.geoserver_publish)==null?void 0:c.layer_name)||((L=(f=n.details)==null?void 0:f.map_config)==null?void 0:L.layer_id)||"",date:n.start_time?new Date(n.start_time).toLocaleDateString():"\u672A\u77E5"}}).filter(n=>n.id);Z.value=u}}catch{y.error("\u83B7\u53D6\u53EF\u7528\u56FE\u5C42\u5217\u8868\u5931\u8D25")}finally{K.value=!1}},ie=()=>{if(!(d.value&&m.value||!b.value)&&C.value)try{$.value=!0;const e=V([108.3,22.8]);d.value=new de({target:b.value,layers:[],view:new me({center:e,zoom:10}),controls:[]}),m.value=new de({target:C.value,layers:[],view:new me({center:e,zoom:10}),controls:[]}),la(),$.value=!1}catch{y.error("\u521D\u59CB\u5316\u5730\u56FE\u5931\u8D25"),$.value=!1}},oe=()=>{if(!s.value&&ne.value)try{$.value=!0,F.value=ne.value;const e=V([108.3,22.8]);s.value=new de({target:F.value,layers:[],view:new me({center:e,zoom:10}),controls:[]}),s.value.once("rendercomplete",()=>{_a(()=>{var a,t;T(),(a=s.value)==null||a.updateSize(),(t=s.value)==null||t.render()})}),T(),ta(),setTimeout(()=>{s.value&&(s.value.updateSize(),T(),s.value.render())},500),$.value=!1}catch{y.error("\u521D\u59CB\u5316\u5730\u56FE\u5931\u8D25"),$.value=!1}},la=()=>{if(!d.value||!m.value)return;const e=d.value.getView(),a=m.value.getView();d.value.on("moveend",()=>{const t=e.getCenter(),l=e.getZoom(),u=e.getRotation();t&&l!==void 0&&(a.setCenter(t),a.setZoom(l),a.setRotation(u))}),m.value.on("moveend",()=>{const t=a.getCenter(),l=a.getZoom(),u=a.getRotation();t&&l!==void 0&&(e.setCenter(t),e.setZoom(l),e.setRotation(u))})},ta=()=>{R.value&&(document.addEventListener("mousemove",re),document.addEventListener("mouseup",ve))},na=e=>{ue.value=!0,e.preventDefault()};let ee=null;const re=e=>{if(ue.value&&F.value&&s.value)try{const a=F.value.getBoundingClientRect(),t=a.width,l=Math.max(0,Math.min(t,e.clientX-a.left)),u=parseFloat((l/t*100).toFixed(2));ee!==null&&cancelAnimationFrame(ee),ee=requestAnimationFrame(()=>{R.value&&(R.value.style.left=`${u}%`),x.value=Math.round(u),s.value&&s.value.render(),ee=null})}catch{}},ae=()=>{if(s.value&&g.value==="swipe")try{!s.value.getLayers().getArray().some(a=>a.getVisible())&&p.value&&h.value&&(s.value.getLayers().clear(),M(),s.value.updateSize(),s.value.render())}catch{}},ve=()=>{ue.value=!1,setTimeout(ae,100)},T=()=>{if(g.value==="swipe"&&R.value){try{R.value.style.left=`${x.value}%`}catch{}s.value&&requestAnimationFrame(()=>{var e;(e=s.value)==null||e.render(),setTimeout(ae,100)})}},ua=e=>{const a=Math.max(10,Math.min(90,e));x.value=a,g.value==="split"?b.value&&C.value&&(b.value.style.width=`${a}%`,C.value.style.width=100-a+"%",d.value&&d.value.updateSize(),m.value&&m.value.updateSize()):(T(),s.value&&s.value.render())},M=()=>{if(p.value&&h.value){$.value=!0;try{if(g.value==="split")d.value&&(d.value.getLayers().clear(),Re(p.value,d.value)),m.value&&(m.value.getLayers().clear(),Re(h.value,m.value));else if(s.value){s.value.getLayers().clear();const e=ce(h.value);e&&(e.setZIndex(0),e.on("prerender",function(l){const u=l.context,n=u.canvas;try{const i=n.width,c=i*x.value/100;u.save(),u.beginPath(),u.rect(c,0,i-c,n.height),u.clip()}catch{u.save()}}),e.on("postrender",function(l){l.context.restore()}),s.value.addLayer(e),N.value=e);const a=ce(p.value);a&&(a.setZIndex(1),a.on("prerender",function(l){const u=l.context,n=u.canvas;try{const i=n.width,c=i*x.value/100;u.save(),u.beginPath(),u.rect(0,0,c,n.height),u.clip()}catch{u.save()}}),a.on("postrender",function(l){l.context.restore()}),s.value.addLayer(a),se.value=a,N.value&&N.value.setZIndex(0));const t=p.value.split(":");t.length===2&&Fe(t[0],t[1])}}catch{y.error("\u66F4\u65B0\u56FE\u5C42\u5931\u8D25")}finally{$.value=!1}}},ce=e=>{if(!e)return null;try{const a=e.split(":");if(a.length!==2)throw new Error(`\u56FE\u5C42ID\u683C\u5F0F\u4E0D\u6B63\u786E: ${e}`);a[0],a[1];const t="http://**************:8085/geoserver",l="EPSG:4326",u="image/png",n="",i=ma(l);if(!i)throw new Error(`\u65E0\u6CD5\u83B7\u53D6\u6295\u5F71\u7CFB\u7EDF: ${l}`);const c=new pa({url:`${t}/gwc/service/wmts`,layer:e,matrixSet:l,format:u,projection:i,style:n,requestEncoding:"KVP",tileGrid:sa(i,l),wrapX:!0,transition:0,crossOrigin:"anonymous",tileLoadFunction:Ke});return c.on("tileloadend",()=>{J.length>0&&(J.shift(),c.refresh())}),new fa({preload:1/0,source:c,visible:!0,opacity:1})}catch{return null}},Re=(e,a)=>{if(e&&a)try{const t=ce(e);if(t){a.addLayer(t);const l=e.split(":");l.length===2&&Fe(l[0],l[1])}}catch(t){throw t}},sa=(e,a)=>{let t,l,u;e.getExtent(),t=[-180,90],l=[.703125,.3515625,.17578125,.087890625,.0439453125,.02197265625,.010986328125,.0054931640625,.00274658203125,.001373291015625,.0006866455078125,.0003433227539062,.0001716613769531,858306884766e-16,429153442383e-16,214576721191e-16,107288360596e-16,53644180298e-16,26822090149e-16,13411045074e-16,6705522537e-16,3352761269e-16,16763806345e-17,8381903173e-17,4190951586e-17,2095475793e-17],u=[];for(let n=0;n<l.length;n++)u.push(`${a}:${n}`);return new ga({origin:t,resolutions:l,matrixIds:u})},Fe=async(e,a)=>{var l,u;const t=g.value==="split"?d.value:s.value;if(t)try{const n=await ya(e,a);if(n&&n.status==="success"){let c;if(!((l=n.bbox)!=null&&l.latLon)||n.bbox.latLon.minx===-180&&n.bbox.latLon.miny===-90&&n.bbox.latLon.maxx===180&&n.bbox.latLon.maxy===90){if((u=n.bbox)!=null&&u.native&&(n.bbox.native.minx!==-180||n.bbox.native.miny!==-90||n.bbox.native.maxx!==180||n.bbox.native.maxy!==90)){const{minx:f,miny:L,maxx:O,maxy:r}=n.bbox.native,P=V([f,L]),B=V([O,r]);c=[P[0],P[1],B[0],B[1]]}}else{const{minx:f,miny:L,maxx:O,maxy:r}=n.bbox.latLon,P=V([f,L]),B=V([O,r]);c=[P[0],P[1],B[0],B[1]]}if(c&&!c.some(f=>!isFinite(f)))return void t.getView().fit(c,{padding:[50,50,50,50],maxZoom:18})}const i=V([108.2,22.7]).concat(V([108.5,23]));t.getView().fit(i,{padding:[50,50,50,50],maxZoom:15})}catch{if(t){const i=V([108.2,22.7]).concat(V([108.5,23]));t.getView().fit(i,{padding:[50,50,50,50],maxZoom:15})}}},ia=e=>{g.value=e},oa=()=>{if(!p.value||!h.value)return;const e=p.value,a=A.value;p.value=h.value,h.value=e,A.value=U.value,U.value=a,M(),y.success("\u5DF2\u4EA4\u6362\u5DE6\u53F3\u56FE\u5C42")},le=()=>{d.value&&(d.value.setTarget(void 0),d.value=null),m.value&&(m.value.setTarget(void 0),m.value=null),s.value&&(s.value.setTarget(void 0),s.value=null),se.value=null,N.value=null},ra=()=>{document.removeEventListener("mousemove",re),document.removeEventListener("mouseup",ve),le(),D.value=!1};Ue(()=>{I.forEach(e=>{clearTimeout(e)}),I.clear(),J.length=0,document.removeEventListener("mousemove",re),document.removeEventListener("mouseup",ve),le()}),xa(()=>{const e=new ResizeObserver(a=>{for(const t of a)g.value==="swipe"&&s.value&&(s.value.updateSize(),T(),s.value.render())});G(()=>D.value,a=>{a&&F.value?e.observe(F.value):e.disconnect()}),Ue(()=>{e.disconnect()})});const va=e=>{if(!b.value||!C.value)return;let a=null;const t=u=>{if(!b.value||!C.value)return;const n=b.value.parentElement;if(!n)return;const i=n.getBoundingClientRect(),c=u.clientX-i.left,f=Math.max(10,Math.min(90,c/i.width*100));a!==null&&cancelAnimationFrame(a),a=requestAnimationFrame(()=>{b.value.style.width=`${f}%`,C.value.style.width=100-f+"%";const L=document.querySelector(".map-divider");L&&(L.style.left=`${f}%`),x.value=Math.round(f),d.value&&d.value.updateSize(),m.value&&m.value.updateSize(),a=null})},l=()=>{document.removeEventListener("mousemove",t),document.removeEventListener("mouseup",l)};document.addEventListener("mousemove",t),document.addEventListener("mouseup",l),e.preventDefault()};return(e,a)=>{const t=E("el-radio-button"),l=E("el-radio-group"),u=E("el-icon"),n=E("el-input"),i=E("el-button"),c=E("el-option"),f=E("el-select"),L=E("el-input-number"),O=E("el-dialog");return k(),pe(O,{modelValue:D.value,"onUpdate:modelValue":a[6]||(a[6]=r=>D.value=r),width:"90%",top:"5vh","destroy-on-close":"","before-close":ra,class:"map-compare-dialog"},{header:_(()=>[v("div",fe,[a[9]||(a[9]=v("div",{class:"dialog-title"},"\u5730\u56FE\u56FE\u5C42\u6BD4\u8F83",-1)),v("div",ge,[w(l,{modelValue:g.value,"onUpdate:modelValue":a[0]||(a[0]=r=>g.value=r),onChange:ia,size:"default"},{default:_(()=>[w(t,{label:"split"},{default:_(()=>a[7]||(a[7]=[j("\u5BF9\u6BD4\u6A21\u5F0F")])),_:1}),w(t,{label:"swipe"},{default:_(()=>a[8]||(a[8]=[j("\u5377\u5E18\u6A21\u5F0F")])),_:1})]),_:1},8,["modelValue"])])])]),default:_(()=>[v("div",ye,[v("div",he,[$.value?(k(),X("div",we,[w(u,{class:"loading-icon"},{default:_(()=>[w(qe(ca))]),_:1}),a[10]||(a[10]=v("p",null,"\u6B63\u5728\u52A0\u8F7D\u56FE\u5C42...",-1))])):La("",!0),v("div",be,[v("div",xe,[v("div",Le,[a[11]||(a[11]=v("span",null,"\u5DE6\u4FA7\u56FE\u5C42:",-1)),v("div",_e,[w(n,{modelValue:A.value,"onUpdate:modelValue":a[1]||(a[1]=r=>A.value=r),placeholder:"\u641C\u7D22",clearable:"","prefix-icon":"Search",size:"small",onInput:Ye},null,8,["modelValue"])]),v("div",Ce,[w(i,{type:S.value?"warning":"primary",size:"small",onClick:ea,icon:S.value?"Close":"Location",loading:Y.value},{default:_(()=>[j(Oe(S.value?"\u53D6\u6D88\u641C\u7D22":"\u7A7A\u95F4\u641C\u7D22"),1)]),_:1},8,["type","icon","loading"])])]),w(f,{modelValue:p.value,"onUpdate:modelValue":a[2]||(a[2]=r=>p.value=r),placeholder:"\u9009\u62E9\u5DE6\u4FA7\u56FE\u5C42",filterable:"",loading:K.value,onChange:M,class:"layer-selector"},{default:_(()=>[(k(!0),X(Pe,null,Be(Qe.value,r=>(k(),pe(c,{key:r.id,label:`${r.id} (${r.date})`,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),v("div",Ve,[w(i,{type:"primary",circle:"",icon:qe(da),onClick:oa,disabled:!p.value||!h.value,title:"\u4EA4\u6362\u5DE6\u53F3\u56FE\u5C42",class:"swap-button"},null,8,["icon","disabled"]),w(L,{modelValue:x.value,"onUpdate:modelValue":a[3]||(a[3]=r=>x.value=r),min:10,max:90,step:1,size:"small",onChange:ua,controls:!1,class:"ratio-input"},{append:_(()=>a[12]||(a[12]=[j("%")])),_:1},8,["modelValue"])]),v("div",$e,[v("div",Ee,[a[13]||(a[13]=v("span",null,"\u53F3\u4FA7\u56FE\u5C42:",-1)),v("div",Se,[w(n,{modelValue:U.value,"onUpdate:modelValue":a[4]||(a[4]=r=>U.value=r),placeholder:"\u641C\u7D22",clearable:"","prefix-icon":"Search",size:"small",onInput:He},null,8,["modelValue"])]),v("div",ze,[w(i,{type:z.value?"warning":"primary",size:"small",onClick:aa,icon:z.value?"Close":"Location",loading:H.value},{default:_(()=>[j(Oe(z.value?"\u53D6\u6D88\u641C\u7D22":"\u7A7A\u95F4\u641C\u7D22"),1)]),_:1},8,["type","icon","loading"])])]),w(f,{modelValue:h.value,"onUpdate:modelValue":a[5]||(a[5]=r=>h.value=r),placeholder:"\u9009\u62E9\u53F3\u4FA7\u56FE\u5C42",filterable:"",loading:K.value,onChange:M,class:"layer-selector"},{default:_(()=>[(k(!0),X(Pe,null,Be(We.value,r=>(k(),pe(c,{key:r.id,label:`${r.id} (${r.date})`,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])])]),v("div",ke,[g.value==="split"?(k(),X("div",Ie,[v("div",{ref_key:"leftMapContainer",ref:b,class:"left-map"},null,512),v("div",{class:"map-divider",style:Ge({left:`${x.value}%`}),onMousedown:va},null,36),v("div",{ref_key:"rightMapContainer",ref:C,class:"right-map"},null,512)])):(k(),X("div",Te,[v("div",{ref_key:"singleMapContainer",ref:ne,class:"single-map"},[v("div",{ref_key:"swipeContainer",ref:R,id:"swipeContainer",style:Ge({left:`${x.value}%`}),onMousedown:na},a[14]||(a[14]=[v("div",{id:"swipeDiv"},[v("div",{class:"handle"})],-1)]),36)],512)]))])])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-c209011a"]])});export{Ca as __tla,Xe as default};
