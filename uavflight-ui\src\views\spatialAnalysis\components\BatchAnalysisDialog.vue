<template>
  <el-dialog
    v-model="visible"
    title="批量分析"
    width="90%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-analysis-content">
      <div class="content-layout">
        <!-- 左侧：影像选择 -->
        <div class="left-section">
          <div class="section-header">
            <h4>选择影像</h4>
            <div class="selection-info">
              已选择 <span class="count">{{ selectedTasks.length }}</span> 个影像
            </div>
          </div>

          <!-- 搜索和筛选 -->
          <div class="search-section">
            <el-row :gutter="12">
              <el-col :span="12">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索影像名称或ID"
                  :prefix-icon="Search"
                  clearable
                  @input="handleSearch"
                />
              </el-col>
              <el-col :span="8">
                <el-select
                  v-model="statusFilter"
                  placeholder="筛选状态"
                  clearable
                  @change="handleFilter"
                >
                  <el-option label="全部" value="" />
                  <el-option label="完成" value="完成" />
                  <el-option label="进行中" value="进行中" />
                  <el-option label="等待中" value="等待中" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-button type="primary" :icon="Refresh" @click="refreshTasks">刷新</el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 影像列表 -->
          <div class="task-list">
            <el-table
              v-loading="loading"
              :data="filteredTasks"
              @selection-change="handleSelectionChange"
              height="400"
              stripe
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="task_id" label="影像ID" width="150" />
              <el-table-column prop="name" label="影像名称" min-width="200" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="end_time" label="完成时间" width="180" />
            </el-table>

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50]"
                :total="filteredTasks.length"
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>

        <!-- 右侧：任务配置和预览 -->
        <div class="right-section">
          <!-- 任务配置 -->
          <div class="config-section">
            <div class="section-header">
              <h4>分析配置</h4>
            </div>

            <el-form :model="batchConfig" label-width="120px" size="default">
              <el-form-item label="任务类型" required>
                <el-select v-model="batchConfig.taskType" placeholder="请选择任务类型" @change="handleTaskTypeChange">
                  <el-option
                    v-for="option in taskTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="模型类型" required>
                <el-select
                  v-model="batchConfig.modelType"
                  placeholder="请选择模型类型"
                  :disabled="!batchConfig.taskType"
                  @change="handleModelTypeChange"
                >
                  <el-option
                    v-for="option in modelTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="模型名称" required>
                <el-select
                  v-model="batchConfig.modelName"
                  placeholder="请选择模型名称"
                  :disabled="!batchConfig.modelType"
                >
                  <el-option
                    v-for="option in modelNameOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="待对比对象" required>
                <el-select
                  v-model="batchConfig.shpFile"
                  placeholder="请选择待对比的对象"
                  :disabled="!batchConfig.taskType"
                >
                  <el-option
                    v-for="option in shpFileOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="过滤面积" required>
                <el-input-number
                  v-model="batchConfig.areaThreshold"
                  :min="1"
                  :max="10000"
                  :step="10"
                  style="width: 100%"
                  placeholder="请输入过滤面积阈值"
                />
                <div class="form-item-tip">单位：平方米，用于过滤小于该面积的区域</div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 任务预览 -->
          <div class="preview-section">
            <div class="section-header">
              <h4>任务预览</h4>
              <div class="preview-info">
                将创建 <span class="count">{{ taskPreviewList.length }}</span> 个分析任务
              </div>
            </div>

            <div class="preview-list">
              <el-scrollbar height="200px">
                <div v-if="taskPreviewList.length === 0" class="empty-preview">
                  <el-empty description="请先选择影像和配置分析参数" :image-size="80" />
                </div>
                <div v-else>
                  <div 
                    v-for="(preview, index) in taskPreviewList" 
                    :key="index"
                    class="preview-item"
                  >
                    <div class="preview-content">
                      <div class="task-info">
                        <span class="task-name">{{ preview.taskName }}</span>
                        <span class="image-name">{{ preview.imageName }}</span>
                      </div>
                      <div class="config-info">
                        <el-tag size="small" type="info">{{ preview.analysisType }}</el-tag>
                        <el-tag size="small" type="success">{{ preview.modelName }}</el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!canSubmit"
          :loading="submitting"
          @click="handleBatchSubmit"
        >
          {{ submitting ? '提交中...' : `批量提交 (${taskPreviewList.length})` }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';

// 接口定义
interface Task {
  task_id: string;
  name: string;
  status: string;
  end_time: string;
  start_time: string;
  bbox?: any;
}

interface BatchConfig {
  taskType: string;
  modelType: string;
  modelName: string;
  shpFile: string;
  areaThreshold: number;
}

interface TaskPreview {
  taskName: string;
  imageName: string;
  analysisType: string;
  modelName: string;
  imageId: string;
}

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const submitting = ref(false);
const allTasks = ref<Task[]>([]);
const selectedTasks = ref<Task[]>([]);

// 搜索和筛选
const searchKeyword = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(20);

// 批量配置
const batchConfig = ref<BatchConfig>({
  taskType: '',
  modelType: '',
  modelName: '',
  shpFile: '',
  areaThreshold: 400
});

// 权重信息（模拟数据，实际应该从API获取）
const weightInfo = ref<any>({});

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    loadTasks();
    loadWeightInfo();
  }
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 计算属性
const filteredTasks = computed(() => {
  let tasks = allTasks.value;

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    tasks = tasks.filter(task =>
      task.name.toLowerCase().includes(keyword) ||
      task.task_id.toLowerCase().includes(keyword)
    );
  }

  // 状态过滤
  if (statusFilter.value) {
    tasks = tasks.filter(task => task.status === statusFilter.value);
  }

  return tasks;
});

// 计算属性 - 参考TaskConfigDialog的实现
const taskTypeOptions = computed(() => {
  return Object.keys(weightInfo.value).map(key => ({
    value: key,
    label: weightInfo.value[key].display_name
  }));
});

const modelTypeOptions = computed(() => {
  if (!batchConfig.value.taskType || !weightInfo.value[batchConfig.value.taskType]) {
    return [];
  }

  const models = weightInfo.value[batchConfig.value.taskType].models;
  return Object.keys(models).map(key => ({
    value: key,
    label: key
  }));
});

const modelNameOptions = computed(() => {
  if (!batchConfig.value.taskType || !batchConfig.value.modelType) {
    return [];
  }

  const taskTypeInfo = weightInfo.value[batchConfig.value.taskType];
  const models = taskTypeInfo.models[batchConfig.value.modelType] || [];

  return models.map((model: any) => ({
    value: model.name,
    label: model.name,
    path: model.path
  }));
});

const shpFileOptions = computed(() => {
  if (!batchConfig.value.taskType || !weightInfo.value[batchConfig.value.taskType]) {
    return [];
  }

  const shpFiles = weightInfo.value[batchConfig.value.taskType].shp_files;
  return shpFiles.map((file: any) => ({
    value: file,
    label: file.split(/[/\\]/).pop()?.replace(/\.[^/.]+$/, '') || file
  }));
});

const taskPreviewList = computed(() => {
  if (selectedTasks.value.length === 0 || !batchConfig.value.taskType || !batchConfig.value.modelName) {
    return [];
  }

  // 获取任务类型的显示名称
  const taskTypeInfo = weightInfo.value[batchConfig.value.taskType];
  const displayName = taskTypeInfo ? taskTypeInfo.display_name : batchConfig.value.taskType;

  return selectedTasks.value.map(task => ({
    taskName: `${displayName}_${task.task_id}`,
    imageName: task.name,
    analysisType: displayName,
    modelName: batchConfig.value.modelName,
    imageId: task.task_id
  }));
});

const canSubmit = computed(() => {
  return selectedTasks.value.length > 0 &&
         batchConfig.value.taskType &&
         batchConfig.value.modelType &&
         batchConfig.value.modelName &&
         batchConfig.value.shpFile &&
         !submitting.value;
});

// 方法
const handleClose = () => {
  visible.value = false;
  resetForm();
};

const resetForm = () => {
  selectedTasks.value = [];
  searchKeyword.value = '';
  statusFilter.value = '';
  batchConfig.value = {
    taskType: '',
    modelType: '',
    modelName: '',
    shpFile: '',
    areaThreshold: 400
  };
  currentPage.value = 1;
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleFilter = () => {
  currentPage.value = 1;
};

const handleSelectionChange = (selection: Task[]) => {
  selectedTasks.value = selection;
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '完成': 'success',
    '进行中': 'warning',
    '等待中': 'info',
    '失败': 'danger'
  };
  return statusMap[status] || 'info';
};

// 事件处理方法 - 参考TaskConfigDialog
const handleTaskTypeChange = () => {
  if (batchConfig.value.taskType && weightInfo.value[batchConfig.value.taskType]) {
    const taskTypeInfo = weightInfo.value[batchConfig.value.taskType];
    const defaultModel = taskTypeInfo.default;

    // 解析默认模型
    const [modelType, modelName] = defaultModel.split('/');

    batchConfig.value.modelType = modelType;
    batchConfig.value.modelName = modelName;

    // 默认选择第一个SHP文件
    if (taskTypeInfo.shp_files.length > 0) {
      batchConfig.value.shpFile = taskTypeInfo.shp_files[0];
    }

    // 设置默认过滤面积
    batchConfig.value.areaThreshold = taskTypeInfo.default_area || 400;
  } else {
    batchConfig.value.modelType = '';
    batchConfig.value.modelName = '';
    batchConfig.value.shpFile = '';
    batchConfig.value.areaThreshold = 400;
  }
};

const handleModelTypeChange = () => {
  batchConfig.value.modelName = '';
};

// 加载任务列表
const loadTasks = async () => {
  loading.value = true;
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/map/odm/tasks`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`获取任务列表失败: ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'success' && data.tasks) {
      allTasks.value = data.tasks;
      console.log('成功加载任务列表:', allTasks.value);
    } else {
      throw new Error('任务列表数据格式错误');
    }
  } catch (error) {
    console.error('加载任务列表失败:', error);
    ElMessage.error(`加载任务列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const refreshTasks = () => {
  loadTasks();
};

// 加载权重信息
const loadWeightInfo = async () => {
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/weight-info/`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`获取权重信息失败: ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'success' && data.data) {
      weightInfo.value = data.data;
      console.log('成功加载权重信息:', weightInfo.value);
    } else {
      throw new Error('权重信息数据格式错误');
    }
  } catch (error) {
    console.error('加载权重信息失败:', error);
    ElMessage.error(`加载权重信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 批量提交
const handleBatchSubmit = async () => {
  if (!canSubmit.value) {
    return;
  }

  try {
    // 获取任务类型显示名称
    const taskTypeInfo = weightInfo.value[batchConfig.value.taskType];
    const displayName = taskTypeInfo ? taskTypeInfo.display_name : batchConfig.value.taskType;

    await ElMessageBox.confirm(
      `确定要为 ${selectedTasks.value.length} 个影像创建 ${displayName} 任务吗？`,
      '确认批量提交',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    submitting.value = true;
    ElMessage.info(`开始批量提交 ${taskPreviewList.value.length} 个分析任务...`);

    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const baseUrl = `http://${geoserverIp}:${geoserverPort}`;

    let successCount = 0;
    let failCount = 0;
    const errors: string[] = [];

    // 逐个提交任务
    for (let i = 0; i < selectedTasks.value.length; i++) {
      const task = selectedTasks.value[i];

      try {
        // 获取模型路径
        const selectedModel = modelNameOptions.value.find((model: any) => model.value === batchConfig.value.modelName);
        const modelPath = selectedModel?.path || '';

        if (!modelPath) {
          throw new Error('无法获取模型路径');
        }

        // 生成影像路径
        const imagePath = `D:/Drone_Project/nginxData/ODM/Output/${task.task_id}/${task.task_id}_out.tif`;

        // 构建请求参数 - 参考TaskConfigDialog的实现
        const requestParams = {
          id: task.task_id,
          image: imagePath,
          model: modelPath,
          old_data_path: batchConfig.value.shpFile,
          area_threshold: batchConfig.value.areaThreshold.toString()
        };

        console.log(`提交第 ${i + 1} 个任务参数:`, requestParams);

        const params = new URLSearchParams(requestParams);
        const apiUrl = `${baseUrl}/api/analysis/queued-combined-ai-spatial-analysis/?${params.toString()}`;

        console.log(`提交第 ${i + 1} 个任务:`, {
          taskId: task.task_id,
          taskName: task.name,
          apiUrl
        });

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`第 ${i + 1} 个任务API响应:`, result);

        if (result.success === true) {
          successCount++;
          console.log(`任务 ${task.name} 提交成功`);
        } else {
          throw new Error(result.message || 'API返回失败状态');
        }

      } catch (error) {
        failCount++;
        const errorMsg = `任务 ${task.name}: ${error instanceof Error ? error.message : '未知错误'}`;
        errors.push(errorMsg);
        console.error(`任务 ${task.name} 提交失败:`, error);
      }

      // 更新进度
      ElMessage.info(`批量提交进度: ${i + 1}/${selectedTasks.value.length}`);
    }

    // 显示最终结果
    if (successCount === selectedTasks.value.length) {
      ElMessage.success(`批量提交完成！成功提交 ${successCount} 个分析任务`);
    } else if (successCount > 0) {
      ElMessage.warning(`批量提交完成！成功 ${successCount} 个，失败 ${failCount} 个`);
      if (errors.length > 0) {
        console.warn('失败的任务:', errors);
      }
    } else {
      ElMessage.error(`批量提交失败！所有 ${failCount} 个任务都提交失败`);
      if (errors.length > 0) {
        console.error('失败详情:', errors);
      }
    }

    handleClose();

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量提交失败:', error);
      ElMessage.error(`批量提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  } finally {
    submitting.value = false;
  }
};

// 组件挂载
onMounted(() => {
  // 组件挂载时不自动加载，等待弹窗打开时再加载
});
</script>

<style scoped lang="scss">
.batch-analysis-content {
  .content-layout {
    display: flex;
    gap: 24px;
    height: 600px;

    .left-section {
      flex: 0 0 55%;
      display: flex;
      flex-direction: column;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .selection-info {
          font-size: 14px;
          color: #606266;

          .count {
            color: #409eff;
            font-weight: 600;
          }
        }
      }

      .search-section {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
      }

      .task-list {
        flex: 1;
        display: flex;
        flex-direction: column;

        .pagination {
          margin-top: 12px;
          display: flex;
          justify-content: center;
        }
      }
    }

    .right-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .preview-info {
          font-size: 14px;
          color: #606266;

          .count {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }

      .config-section {
        .el-form {
          background-color: #f8f9fa;
          padding: 16px;
          border-radius: 6px;
        }
      }

      .preview-section {
        flex: 1;

        .preview-list {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          background-color: #fff;

          .empty-preview {
            padding: 20px;
            text-align: center;
          }

          .preview-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .preview-content {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .task-info {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .task-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: #303133;
                }

                .image-name {
                  font-size: 12px;
                  color: #909399;
                }
              }

              .config-info {
                display: flex;
                gap: 6px;
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
