import{v as I,r as g,c as k,__tla as N}from"./index.BSP3cg_z.js";import{b as O,c as P,e as z,h as J,__tla as K}from"./dict.D9OX-VAS.js";import{u as M,__tla as Q}from"./dict.DrX0Qdnc.js";import{d as $,k as b,A as x,B as i,m as W,a as F,b as m,t as s,v as d,q as X,e as w,u as t,F as Y,p as Z,E as _,G as f,f as ee,H as le,y as ae}from"./vue.CnN__PXn.js";let D,te=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{let h,v,V;h={class:"system-dic-dialog-container"},v={class:"dialog-footer"},V=$({name:"systemDicDialog"}),D=$({...V,emits:["refresh"],setup(re,{expose:B,emit:U}){const q=U,{dict_type:C}=M("dict_type"),{t:L}=I.useI18n(),n=b(),o=b(!1),u=b(!1),a=x({id:"",dictType:"",description:"",systemFlag:"0",remarks:""}),R=x({dictType:[{validator:g.overLength,trigger:"blur"},{required:!0,message:"\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:g.validatorNameCn,trigger:"blur"},{validator:(e,l,p)=>{O(e,l,p,a.id!=="")},trigger:"blur"}],systemFlag:[{required:!0,message:"\u5B57\u5178\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{validator:g.overLength,trigger:"blur"},{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),S=async()=>{if(!await n.value.validate().catch(()=>{}))return!1;try{u.value=!0;const e=a.id?await z(a):await J(a);k().success(L(a.id?"common.editSuccessText":"common.addSuccessText")),o.value=!1,q("refresh",e.data)}catch(e){k().error(e.msg)}finally{u.value=!1}};return B({openDialog:e=>{o.value=!0,a.id="",ae(()=>{var l;(l=n.value)==null||l.resetFields()}),e&&P(e).then(l=>{Object.assign(a,l.data)})}}),(e,l)=>{const p=i("el-radio"),j=i("el-radio-group"),c=i("el-form-item"),y=i("el-input"),A=i("el-form"),T=i("el-button"),E=i("el-dialog"),G=W("loading");return m(),F("div",h,[s(E,{title:t(a).id?e.$t("common.editBtn"):e.$t("common.addBtn"),modelValue:t(o),"onUpdate:modelValue":l[5]||(l[5]=r=>le(o)?o.value=r:null),width:"600"},{footer:d(()=>[ee("span",v,[s(T,{onClick:l[4]||(l[4]=r=>o.value=!1)},{default:d(()=>[_(f(e.$t("common.cancelButtonText")),1)]),_:1}),s(T,{onClick:S,type:"primary",disabled:t(u)},{default:d(()=>[_(f(e.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:d(()=>[X((m(),w(A,{model:t(a),rules:t(R),"label-width":"90px",ref_key:"dicDialogFormRef",ref:n},{default:d(()=>[s(c,{label:e.$t("sysdict.systemFlag"),prop:"systemFlag"},{default:d(()=>[s(j,{modelValue:t(a).systemFlag,"onUpdate:modelValue":l[0]||(l[0]=r=>t(a).systemFlag=r)},{default:d(()=>[(m(!0),F(Y,null,Z(t(C),(r,H)=>(m(),w(p,{border:"",key:H,label:r.value},{default:d(()=>[_(f(r.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(c,{label:e.$t("sysdict.dictType"),prop:"dictType"},{default:d(()=>[s(y,{placeholder:e.$t("sysdict.inputDictTypeTip"),disabled:t(a).id!=="",clearable:"",modelValue:t(a).dictType,"onUpdate:modelValue":l[1]||(l[1]=r=>t(a).dictType=r)},null,8,["placeholder","disabled","modelValue"])]),_:1},8,["label"]),s(c,{label:e.$t("sysdict.description"),prop:"description"},{default:d(()=>[s(y,{placeholder:e.$t("sysdict.inputDescriptionTip"),clearable:"",modelValue:t(a).description,"onUpdate:modelValue":l[2]||(l[2]=r=>t(a).description=r)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),s(c,{label:e.$t("sysdict.remarks"),prop:"remarks"},{default:d(()=>[s(y,{type:"textarea",maxlength:"150",rows:"3",placeholder:e.$t("sysdict.inputRemarksTip"),modelValue:t(a).remarks,"onUpdate:modelValue":l[3]||(l[3]=r=>t(a).remarks=r)},null,8,["placeholder","modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[G,t(u)]])]),_:1},8,["title","modelValue"])])}}})});export{te as __tla,D as default};
