const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.B2FR0lVF.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/header.CDsBv0mY.js","assets/main.DP48xYwf.js","assets/tagsView.MRY9Mxxh.js","assets/sortable.esm.BGML4dzN.js","assets/tagsView.77LB7UQf.css"])))=>i.map(i=>d[i]);
import{u as k,a as e,__tla as A}from"./index.BSP3cg_z.js";import{d as y,k as P,l as V,s as g,c as x,o as I,w as f,B as L,e as p,b as h,v as m,t as s,u as t,j as l,f as O,D as M,y as j}from"./vue.CnN__PXn.js";let E,C=Promise.all([(()=>{try{return A}catch{}})()]).then(async()=>{let r,n;r={class:"flex-center layout-backtop"},n=y({name:"layoutClassic"}),E=y({...n,setup(S){const R=l(()=>e(()=>import("./aside.B2FR0lVF.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),T=l(()=>e(()=>import("./header.CDsBv0mY.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([4,1,2,3]))),d=l(()=>e(()=>import("./main.DP48xYwf.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([5,1,2,3]))),v=l(()=>e(()=>import("./tagsView.MRY9Mxxh.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([6,1,2,3,7,8]))),_=P(),w=V(),D=k(),{themeConfig:o}=g(D),b=x(()=>o.value.isTagsview),u=()=>{var a;(a=_.value)==null||a.layoutMainScrollbarRef.update()},i=()=>{j(()=>{setTimeout(()=>{u(),_.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return I(()=>{i()}),f(()=>w.path,()=>{i()}),f(o,()=>{u()},{deep:!0}),(a,B)=>{const c=L("el-container");return h(),p(c,{class:"layout-container flex-center"},{default:m(()=>[s(t(T)),s(c,{class:"layout-mian-height-50"},{default:m(()=>[s(t(R)),O("div",r,[t(b)?(h(),p(t(v),{key:0})):M("",!0),s(t(d),{ref_key:"layoutMainRef",ref:_},null,512)])]),_:1})]),_:1})}}})});export{C as __tla,E as default};
