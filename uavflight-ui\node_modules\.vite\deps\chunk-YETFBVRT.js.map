{"version": 3, "sources": ["../../ol/source/XYZ.js"], "sourcesContent": ["/**\n * @module ol/source/XYZ\n */\n\nimport TileImage from './TileImage.js';\nimport {createXYZ, extentFromProjection} from '../tilegrid.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least the number of tiles in the viewport.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {boolean} [opaque=false] Whether the layer is opaque.\n * @property {import(\"../proj.js\").ProjectionLike} [projection='EPSG:3857'] Projection.\n * @property {number} [reprojectionErrorThreshold=0.5] Maximum allowed reprojection error (in pixels).\n * Higher values can increase reprojection performance, but decrease precision.\n * @property {number} [maxZoom=42] Optional max zoom level. Not used if `tileGrid` is provided.\n * @property {number} [minZoom=0] Optional min zoom level. Not used if `tileGrid` is provided.\n * @property {number} [maxResolution] Optional tile grid resolution at level zero. Not used if `tileGrid` is provided.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] Tile grid.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction] Optional function to load a tile given a URL. The default is\n * ```js\n * function(imageTile, src) {\n *   imageTile.getImage().src = src;\n * };\n * ```\n * @property {number} [tilePixelRatio=1] The pixel ratio used by the tile service.\n * For example, if the tile service advertizes 256px by 256px tiles but actually sends 512px\n * by 512px images (for retina/hidpi devices) then `tilePixelRatio`\n * should be set to `2`.\n * @property {number|import(\"../size.js\").Size} [tileSize=[256, 256]] The tile size used by the tile service.\n * Not used if `tileGrid` is provided.\n * @property {number} [gutter=0] The size in pixels of the gutter around image tiles to ignore.\n * This allows artifacts of rendering at tile edges to be ignored.\n * Supported images should be wider and taller than the tile size by a value of `2 x gutter`.\n * @property {import(\"../Tile.js\").UrlFunction} [tileUrlFunction] Optional function to get\n * tile URL given a tile coordinate and the projection.\n * Required if `url` or `urls` are not provided.\n * @property {string} [url] URL template. Must include `{x}`, `{y}` or `{-y}`,\n * and `{z}` placeholders. A `{?-?}` template pattern, for example `subdomain{a-f}.domain.com`,\n * may be used instead of defining each one separately in the `urls` option.\n * @property {Array<string>} [urls] An array of URL templates.\n * @property {boolean} [wrapX=true] Whether to wrap the world horizontally.\n * @property {number} [transition=250] Duration of the opacity transition for rendering.\n * To disable the opacity transition, pass `transition: 0`.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Layer source for tile data with URLs in a set XYZ format that are\n * defined in a URL template. By default, this follows the widely-used\n * Google grid where `x` 0 and `y` 0 are in the top left. Grids like\n * TMS where `x` 0 and `y` 0 are in the bottom left can be used by\n * using the `{-y}` placeholder in the URL template, so long as the\n * source does not have a custom tile grid. In this case\n * a `tileUrlFunction` can be used, such as:\n * ```js\n *  tileUrlFunction: function(coordinate) {\n *    return 'http://mapserver.com/' + coordinate[0] + '/' +\n *      coordinate[1] + '/' + (-coordinate[2] - 1) + '.png';\n *  }\n * ```\n * @api\n */\nclass XYZ extends TileImage {\n  /**\n   * @param {Options} [options] XYZ options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    const projection =\n      options.projection !== undefined ? options.projection : 'EPSG:3857';\n\n    const tileGrid =\n      options.tileGrid !== undefined\n        ? options.tileGrid\n        : createXYZ({\n            extent: extentFromProjection(projection),\n            maxResolution: options.maxResolution,\n            maxZoom: options.maxZoom,\n            minZoom: options.minZoom,\n            tileSize: options.tileSize,\n          });\n\n    super({\n      attributions: options.attributions,\n      cacheSize: options.cacheSize,\n      crossOrigin: options.crossOrigin,\n      interpolate: options.interpolate,\n      opaque: options.opaque,\n      projection: projection,\n      reprojectionErrorThreshold: options.reprojectionErrorThreshold,\n      tileGrid: tileGrid,\n      tileLoadFunction: options.tileLoadFunction,\n      tilePixelRatio: options.tilePixelRatio,\n      tileUrlFunction: options.tileUrlFunction,\n      url: options.url,\n      urls: options.urls,\n      wrapX: options.wrapX !== undefined ? options.wrapX : true,\n      transition: options.transition,\n      attributionsCollapsible: options.attributionsCollapsible,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.gutter_ = options.gutter !== undefined ? options.gutter : 0;\n  }\n\n  /**\n   * @return {number} Gutter.\n   */\n  getGutter() {\n    return this.gutter_;\n  }\n}\n\nexport default XYZ;\n"], "mappings": ";;;;;;;;;AAwEA,IAAM,MAAN,cAAkB,kBAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAEtB,UAAM,aACJ,QAAQ,eAAe,SAAY,QAAQ,aAAa;AAE1D,UAAM,WACJ,QAAQ,aAAa,SACjB,QAAQ,WACR,UAAU;AAAA,MACR,QAAQ,qBAAqB,UAAU;AAAA,MACvC,eAAe,QAAQ;AAAA,MACvB,SAAS,QAAQ;AAAA,MACjB,SAAS,QAAQ;AAAA,MACjB,UAAU,QAAQ;AAAA,IACpB,CAAC;AAEP,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,aAAa,QAAQ;AAAA,MACrB,QAAQ,QAAQ;AAAA,MAChB;AAAA,MACA,4BAA4B,QAAQ;AAAA,MACpC;AAAA,MACA,kBAAkB,QAAQ;AAAA,MAC1B,gBAAgB,QAAQ;AAAA,MACxB,iBAAiB,QAAQ;AAAA,MACzB,KAAK,QAAQ;AAAA,MACb,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,YAAY,QAAQ;AAAA,MACpB,yBAAyB,QAAQ;AAAA,MACjC,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,cAAQ;", "names": []}