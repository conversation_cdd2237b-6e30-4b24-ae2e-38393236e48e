const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/form.C_yIF8Su.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
import{v as oe,p as j,a as ne,d as se,j as re,c as Q,__tla as de}from"./index.BSP3cg_z.js";import{d as G,k as w,A as ie,B as r,m as H,a as ue,b as d,f as T,t,q as _,x as me,u as n,v as l,I as ce,E as s,G as p,e as i,H as pe,D as B,j as ye}from"./vue.CnN__PXn.js";import{u as fe,__tla as _e}from"./table.CCFM44Zd.js";let <PERSON>,he=<PERSON>.all([(()=>{try{return de}catch{}})(),(()=>{try{return _e}catch{}})()]).then(async()=>{let R,S,q,D,L;R={class:"layout-padding"},S={class:"layout-padding-auto layout-padding-view"},q={class:"mb8",style:{width:"100%"}},D={style:{"margin-left":"12px"}},L=G({name:"systemMenu"}),M=G({...L,setup(we){const z=ye(()=>ne(()=>import("./form.C_yIF8Su.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3]))),{t:E}=oe.useI18n(),F=w(),C=w(),N=w(),b=w(!0),x=w(!1),u=ie({pageList:j,queryForm:{menuName:""},isPage:!1}),{getDataList:y,tableStyle:A}=fe(u),I=(e,a)=>{C.value.openDialog(e,a)},V=e=>(e.children||[]).length>0,J=async()=>{x.value=!x.value;const e=await j();K(e.data,x.value)},K=(e,a=!0)=>{var v;for(const f in e)(v=F.value)==null||v.toggleRowExpansion(e[f],a),e[f].children&&K(e[f].children,a)},W=()=>{u.dataList=[],y()},X=()=>{N.value.resetFields(),u.dataList=[],y()};return(e,a)=>{var P;const v=r("el-input"),f=r("el-form-item"),c=r("el-button"),Y=r("el-form"),O=r("el-row"),Z=r("right-toolbar"),m=r("el-table-column"),ee=r("SvgIcon"),h=r("el-tag"),le=r("el-tooltip"),ae=r("el-table"),g=H("auth"),te=H("loading");return d(),ue("div",R,[T("div",S,[_(t(O,{shadow:"hover",class:"ml10"},{default:l(()=>[t(Y,{inline:!0,model:n(u).queryForm,onKeyup:ce(n(y),["enter"]),ref_key:"queryRef",ref:N},{default:l(()=>[t(f,{label:e.$t("sysmenu.name"),prop:"menuName"},{default:l(()=>[t(v,{placeholder:e.$t("sysmenu.inputNameTip"),clearable:"",style:{"max-width":"180px"},modelValue:n(u).queryForm.menuName,"onUpdate:modelValue":a[0]||(a[0]=o=>n(u).queryForm.menuName=o)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),t(f,null,{default:l(()=>[t(c,{onClick:W,class:"ml10",icon:"search",type:"primary"},{default:l(()=>[s(p(e.$t("common.queryBtn")),1)]),_:1}),t(c,{onClick:X,icon:"Refresh"},{default:l(()=>[s(p(e.$t("common.resetBtn")),1)]),_:1})]),_:1})]),_:1},8,["model","onKeyup"])]),_:1},512),[[me,n(b)]]),t(O,null,{default:l(()=>[T("div",q,[_((d(),i(c,{onClick:I,class:"ml10",icon:"folder-add",type:"primary"},{default:l(()=>[s(p(e.$t("common.addBtn")),1)]),_:1})),[[g,"sys_menu_add"]]),t(c,{onClick:J},{default:l(()=>[s(p(e.$t("common.expandBtn")),1)]),_:1}),t(Z,{showSearch:n(b),"onUpdate:showSearch":a[1]||(a[1]=o=>pe(b)?b.value=o:null),class:"ml10",style:{float:"right","margin-right":"20px"},onQueryTable:n(y)},null,8,["showSearch","onQueryTable"])])]),_:1}),_((d(),i(ae,{ref_key:"tableRef",ref:F,data:n(u).dataList,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"path",style:{width:"100%"},border:"","cell-style":n(A).cellStyle,"header-cell-style":(P=n(A))==null?void 0:P.headerCellStyle},{default:l(()=>[t(m,{label:e.$t("sysmenu.name"),fixed:"",prop:"name","show-overflow-tooltip":""},null,8,["label"]),t(m,{label:e.$t("sysmenu.sortOrder"),prop:"sortOrder","show-overflow-tooltip":""},null,8,["label"]),t(m,{label:e.$t("sysmenu.icon"),prop:"icon","show-overflow-tooltip":""},{default:l(o=>[t(ee,{name:o.row.meta.icon},null,8,["name"])]),_:1},8,["label"]),t(m,{label:e.$t("sysmenu.path"),prop:"path","show-overflow-tooltip":""},null,8,["label"]),t(m,{label:e.$t("sysmenu.menuType"),"show-overflow-tooltip":""},{default:l(o=>[o.row.menuType==="0"?(d(),i(h,{key:0},{default:l(()=>a[3]||(a[3]=[s("\u5DE6\u83DC\u5355")])),_:1})):B("",!0),o.row.menuType==="2"?(d(),i(h,{key:1},{default:l(()=>a[4]||(a[4]=[s("\u9876\u83DC\u5355")])),_:1})):B("",!0),o.row.menuType==="1"?(d(),i(h,{key:2,type:"success"},{default:l(()=>a[5]||(a[5]=[s("\u6309\u94AE")])),_:1})):B("",!0)]),_:1},8,["label"]),t(m,{label:e.$t("sysmenu.keepAlive"),"show-overflow-tooltip":""},{default:l(o=>[o.row.meta.isKeepAlive?(d(),i(h,{key:0},{default:l(()=>a[6]||(a[6]=[s("\u5F00\u542F")])),_:1})):(d(),i(h,{key:1,type:"info"},{default:l(()=>a[7]||(a[7]=[s("\u5173\u95ED")])),_:1}))]),_:1},8,["label"]),t(m,{label:e.$t("sysmenu.permission"),"show-overflow-tooltip":!0,prop:"permission"},null,8,["label"]),t(m,{label:e.$t("common.action"),"show-overflow-tooltip":"",width:"250"},{default:l(o=>[_((d(),i(c,{icon:"folder-add",onClick:U=>I("add",o.row),text:"",type:"primary"},{default:l(()=>[s(p(e.$t("common.addBtn")),1)]),_:2},1032,["onClick"])),[[g,"sys_menu_add"]]),_((d(),i(c,{icon:"edit-pen",onClick:U=>{return k="edit",$=o.row,void C.value.openDialog(k,$);var k,$},text:"",type:"primary"},{default:l(()=>[s(p(e.$t("common.editBtn")),1)]),_:2},1032,["onClick"])),[[g,"sys_menu_edit"]]),t(le,{icon:"delete",content:e.$t("sysmenu.deleteDisabledTip"),disabled:!V(o.row),placement:"top"},{default:l(()=>[T("span",D,[_((d(),i(c,{icon:"delete",disabled:V(o.row),onClick:U=>(async k=>{try{await se().confirm(E("common.delConfirmText"))}catch{return}try{await re(k.id),y(),Q().success(E("common.delSuccessText"))}catch($){Q().error($.msg)}})(o.row),text:"",type:"primary"},{default:l(()=>[s(p(e.$t("common.delBtn")),1)]),_:2},1032,["disabled","onClick"])),[[g,"sys_menu_del"]])])]),_:2},1032,["content","disabled"])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[te,n(u).loading]])]),t(n(z),{onRefresh:a[2]||(a[2]=o=>n(y)()),ref_key:"menuDialogRef",ref:C},null,512)])}}})});export{he as __tla,M as default};
