<template>
  <div class="basic-info">
    <el-descriptions :column="3" border>
      <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
      <el-descriptions-item label="完成时间">{{ formatDateTime(task.endTime) }}</el-descriptions-item>
      <el-descriptions-item label="图层名称">{{ task.layer_name }}</el-descriptions-item>
      <el-descriptions-item label="主题">{{ task.theme }}</el-descriptions-item>
      <el-descriptions-item label="分析数">{{ analysisCount }}</el-descriptions-item>
      <el-descriptions-item label="空间范围">{{ formatBoundingBox(task.bbox) }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
interface Task {
  id: string;
  endTime: string;
  layer_name: string;
  theme: string;
  bbox: any;
}

interface Props {
  task: Task;
  analysisCount: number;
}

const props = defineProps<Props>();

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 格式化边界框信息
const formatBoundingBox = (bbox: any): string => {
  if (!bbox) return '-';
  
  try {
    const { minx, miny, maxx, maxy } = bbox;
    if (minx !== undefined && miny !== undefined && maxx !== undefined && maxy !== undefined) {
      return `${minx.toFixed(6)}, ${miny.toFixed(6)}, ${maxx.toFixed(6)}, ${maxy.toFixed(6)}`;
    }
    return '-';
  } catch (error) {
    console.error('格式化边界框失败:', error);
    return '-';
  }
};
</script>

<style scoped lang="scss">
.basic-info {
  margin-bottom: 20px;
}
</style>
