import{s as n,__tla as d}from"./index.BSP3cg_z.js";let e,r,a,o,s,u,l=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{a=function(t){return n({url:"/gen/dsconf/page",method:"get",params:t})},s=function(t){return n({url:"/gen/dsconf/list",method:"get",params:t})},e=function(t){return n({url:"/gen/dsconf",method:"post",data:t})},o=function(t){return n({url:"/gen/dsconf/"+t,method:"get"})},r=function(t){return n({url:"/gen/dsconf",method:"delete",data:t})},u=function(t){return n({url:"/gen/dsconf",method:"put",data:t})}});export{l as __tla,e as a,r as d,a as f,o as g,s as l,u as p};
