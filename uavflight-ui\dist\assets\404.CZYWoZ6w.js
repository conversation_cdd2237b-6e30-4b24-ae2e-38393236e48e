import{d as u,z as F,B as b,a as y,b as h,f as t,G as s,t as x,v as w,E as B}from"./vue.CnN__PXn.js";import{q as $,__tla as k}from"./index.BSP3cg_z.js";let _,z=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{let i,l,n,o,d,r,m,c,f;i={class:"error layout-padding"},l={class:"layout-padding-auto layout-padding-view"},n={class:"error-flex"},o={class:"left"},d={class:"left-item"},r={class:"left-item-animation left-item-title"},m={class:"left-item-animation left-item-msg"},c={class:"left-item-animation left-item-btn"},f=u({name:"notFound"}),_=$(u({...f,setup(A){const p=F(),v=()=>{p.push("/")};return(e,a)=>{const g=b("el-button");return h(),y("div",i,[t("div",l,[t("div",n,[t("div",o,[t("div",d,[a[0]||(a[0]=t("div",{class:"left-item-animation left-item-num"},"404",-1)),t("div",r,s(e.$t("notFound.foundTitle")),1),t("div",m,s(e.$t("notFound.foundMsg")),1),t("div",c,[x(g,{type:"primary",round:"",onClick:v},{default:w(()=>[B(s(e.$t("notFound.foundBtn")),1)]),_:1})])])]),a[1]||(a[1]=t("div",{class:"right"},[t("img",{src:"https://img-blog.csdnimg.cn/9eb1d85a417f4ed1ba7107f149ce3da1.png?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,shadow_50,text_Q1NETiBAbHl0LXRvcA==,size_16,color_FFFFFF,t_70,g_se,x_16"})],-1))])])])}}}),[["__scopeId","data-v-a3bea88a"]])});export{z as __tla,_ as default};
