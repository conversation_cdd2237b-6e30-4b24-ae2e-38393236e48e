import{v as C,c as h,r as f,__tla as R}from"./index.BSP3cg_z.js";import{p as S,a as A,g as E,v as G,__tla as H}from"./fieldtype.CAArssoY.js";import{d as v,k as c,A as I,B as p,m as O,e as b,b as k,v as r,q as P,u as e,t as d,f as z,E as V,G as x,H as J,y as K}from"./vue.CnN__PXn.js";let N,M=Promise.all([(()=>{try{return R}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let y,T;y={class:"dialog-footer"},T=v({name:"systemFieldTypeDialog"}),N=v({...T,emits:["refresh"],setup(Q,{expose:w,emit:B}){const U=B,{t:i}=C.useI18n(),n=c(),u=c(!1),s=c(!1),l=I({id:"",columnType:"",attrType:"",packageName:"",createTime:""}),$=c({columnType:[{required:!0,message:"\u5B57\u6BB5\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:f.overLength,trigger:"blur"},{validator:(t,a,m)=>{G(t,a,m,l.id!=="")}}],attrType:[{validator:f.overLength,trigger:"blur"},{required:!0,message:"\u5C5E\u6027\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],packageName:[{validator:f.overLength,trigger:"blur"}]}),q=async()=>{if(!await n.value.validate().catch(()=>{}))return!1;try{s.value=!0,l.id?await S(l):await A(l),h().success(i(l.id?"common.editSuccessText":"common.addSuccessText")),u.value=!1,U("refresh")}catch(t){h().error(t.msg)}finally{s.value=!1}},D=t=>{E(t).then(a=>{Object.assign(l,a.data)})};return w({openDialog:t=>{u.value=!0,K(()=>{var a;(a=n.value)==null||a.resetFields()}),t?(l.id=t,D(t)):l.id=""}}),(t,a)=>{const m=p("el-input"),g=p("el-form-item"),F=p("el-form"),_=p("el-button"),L=p("el-dialog"),j=O("loading");return k(),b(L,{title:e(l).id?t.$t("common.editBtn"):t.$t("common.addBtn"),modelValue:e(u),"onUpdate:modelValue":a[4]||(a[4]=o=>J(u)?u.value=o:null),width:"600","close-on-click-modal":!1,draggable:""},{footer:r(()=>[z("span",y,[d(_,{onClick:a[3]||(a[3]=o=>u.value=!1)},{default:r(()=>[V(x(t.$t("common.cancelButtonText")),1)]),_:1}),d(_,{type:"primary",onClick:q,disabled:e(s)},{default:r(()=>[V(x(t.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:r(()=>[P((k(),b(F,{ref_key:"dataFormRef",ref:n,model:e(l),rules:e($),formDialogRef:"","label-width":"90px"},{default:r(()=>[d(g,{label:e(i)("fieldtype.columnType"),prop:"columnType"},{default:r(()=>[d(m,{modelValue:e(l).columnType,"onUpdate:modelValue":a[0]||(a[0]=o=>e(l).columnType=o),placeholder:e(i)("fieldtype.inputcolumnTypeTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(g,{label:e(i)("fieldtype.attrType"),prop:"attrType"},{default:r(()=>[d(m,{modelValue:e(l).attrType,"onUpdate:modelValue":a[1]||(a[1]=o=>e(l).attrType=o),placeholder:e(i)("fieldtype.inputattrTypeTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(g,{label:e(i)("fieldtype.packageName"),prop:"packageName"},{default:r(()=>[d(m,{modelValue:e(l).packageName,"onUpdate:modelValue":a[2]||(a[2]=o=>e(l).packageName=o),placeholder:e(i)("fieldtype.inputpackageNameTip")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[j,e(s)]])]),_:1},8,["title","modelValue"])}}})});export{M as __tla,N as default};
