<template>
  <el-dialog
    v-model="visible"
    title="批量下载"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-download-content">
      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务名称或ID"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="筛选状态"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="完成" value="完成" />
              <el-option label="进行中" value="进行中" />
              <el-option label="等待中" value="等待中" />
              <el-option label="失败" value="失败" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="categoryFilter"
              placeholder="筛选分析类型"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="耕地分析" value="arableLand" />
              <el-option label="建设用地分析" value="constructionLand" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" :icon="Refresh" @click="refreshData">刷新</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总任务数" :value="totalTasks" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="可下载任务" :value="downloadableTasks" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已选择" :value="selectedTasks.length" />
          </el-col>
          <el-col :span="6">
            <el-button 
              type="success" 
              :disabled="selectedTasks.length === 0"
              @click="handleBatchDownload"
            >
              下载选中项 ({{ selectedTasks.length }})
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 任务列表 -->
      <div class="task-list-section">
        <el-table
          v-loading="loading"
          :data="filteredTasks"
          @selection-change="handleSelectionChange"
          height="400"
          stripe
        >
          <el-table-column type="selection" width="55" :selectable="isTaskSelectable" />
          <el-table-column prop="name" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell">
                <span>{{ row.name }}</span>
                <el-tag 
                  :type="getStatusType(row.status)" 
                  size="small"
                  class="status-tag"
                >
                  {{ row.status }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="analysis_category" label="分析类型" width="120">
            <template #default="{ row }">
              {{ getCategoryName(row.analysis_category) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="可下载文件" min-width="200">
            <template #default="{ row }">
              <div class="download-files">
                <el-tag 
                  v-if="row.output_files?.ai_output_path" 
                  size="small" 
                  type="info"
                  class="file-tag"
                >
                  AI分析结果
                </el-tag>
                <el-tag 
                  v-if="row.output_files?.final_output_path" 
                  size="small" 
                  type="success"
                  class="file-tag"
                >
                  最终结果
                </el-tag>
                <span v-if="!hasDownloadableFiles(row)" class="no-files">无可下载文件</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                :disabled="!hasDownloadableFiles(row)"
                @click="downloadSingle(row)"
              >
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredTasks.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          :disabled="selectedTasks.length === 0"
          @click="handleBatchDownload"
        >
          批量下载 ({{ selectedTasks.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';

// 接口定义
interface DownloadableTask {
  task_id: string;
  name: string;
  status: string;
  createTime: string;
  analysis_category: string;
  timestamp: number;
  output_files?: {
    ai_output_path?: string;
    final_output_path?: string;
  };
  parent_task_id?: string;
}

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const allTasks = ref<DownloadableTask[]>([]);
const selectedTasks = ref<DownloadableTask[]>([]);

// 搜索和筛选
const searchKeyword = ref('');
const statusFilter = ref('');
const categoryFilter = ref('');

// 分页
const currentPage = ref(1);
const pageSize = ref(20);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    loadAllTasks();
  }
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 计算属性
const totalTasks = computed(() => allTasks.value.length);
const downloadableTasks = computed(() => 
  allTasks.value.filter(task => hasDownloadableFiles(task)).length
);

const filteredTasks = computed(() => {
  let tasks = allTasks.value;

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    tasks = tasks.filter(task => 
      task.name.toLowerCase().includes(keyword) ||
      task.task_id.toLowerCase().includes(keyword)
    );
  }

  // 状态过滤
  if (statusFilter.value) {
    tasks = tasks.filter(task => task.status === statusFilter.value);
  }

  // 分类过滤
  if (categoryFilter.value) {
    tasks = tasks.filter(task => task.analysis_category === categoryFilter.value);
  }

  // 只显示有可下载文件的任务
  tasks = tasks.filter(task => hasDownloadableFiles(task));

  return tasks;
});

// 方法
const handleClose = () => {
  visible.value = false;
  selectedTasks.value = [];
  searchKeyword.value = '';
  statusFilter.value = '';
  categoryFilter.value = '';
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleFilter = () => {
  currentPage.value = 1;
};

const handleSelectionChange = (selection: DownloadableTask[]) => {
  selectedTasks.value = selection;
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

const isTaskSelectable = (row: DownloadableTask) => {
  return hasDownloadableFiles(row);
};

const hasDownloadableFiles = (task: DownloadableTask) => {
  return task.output_files && 
         (task.output_files.ai_output_path || task.output_files.final_output_path);
};

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '完成': 'success',
    '进行中': 'warning',
    '等待中': 'info',
    '失败': 'danger'
  };
  return statusMap[status] || 'info';
};

const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    'arableLand': '耕地分析',
    'constructionLand': '建设用地分析'
  };
  return categoryMap[category] || category;
};

// 加载所有任务数据
const loadAllTasks = async () => {
  loading.value = true;
  try {
    console.log('开始加载所有任务数据...');
    
    // 1. 获取所有主任务
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const tasksUrl = `http://${geoserverIp}:${geoserverPort}/api/map/odm/tasks`;
    
    const tasksResponse = await fetch(tasksUrl);
    if (!tasksResponse.ok) {
      throw new Error(`获取任务列表失败: ${tasksResponse.status}`);
    }
    
    const tasksData = await tasksResponse.json();
    console.log('获取到主任务列表:', tasksData);

    if (tasksData.status !== 'success' || !tasksData.tasks) {
      throw new Error('主任务列表数据格式错误');
    }

    // 2. 为每个主任务获取详细的分析任务信息
    const allTaskDetails: DownloadableTask[] = [];

    for (const mainTask of tasksData.tasks) {
      try {
        const taskInfoUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/taskinfo/?id=${mainTask.task_id}`;
        const taskInfoResponse = await fetch(taskInfoUrl);
        
        if (taskInfoResponse.ok) {
          const taskInfoData = await taskInfoResponse.json();
          
          if (taskInfoData.status === 'success' && taskInfoData.data) {
            // 处理每个分析任务
            taskInfoData.data.forEach((analysisTask: any) => {
              const categoryMap: Record<string, string> = {
                'arableLand': '耕地分析',
                'constructionLand': '建设用地分析'
              };
              const categoryName = categoryMap[analysisTask.analysis_category] || '分析';
              const date = new Date(analysisTask.timestamp * 1000);
              const timeStr = date.getFullYear().toString() +
                             (date.getMonth() + 1).toString().padStart(2, '0') +
                             date.getDate().toString().padStart(2, '0') +
                             date.getHours().toString().padStart(2, '0') +
                             date.getMinutes().toString().padStart(2, '0');

              allTaskDetails.push({
                task_id: analysisTask.task_id,
                name: `${categoryName}_${timeStr}`,
                status: analysisTask.status,
                createTime: analysisTask.datetime,
                analysis_category: analysisTask.analysis_category,
                timestamp: analysisTask.timestamp,
                output_files: analysisTask.output_files,
                parent_task_id: mainTask.task_id
              });
            });
          }
        }
      } catch (error) {
        console.warn(`获取任务 ${mainTask.task_id} 的详细信息失败:`, error);
      }
    }

    // 按时间戳倒序排列
    allTasks.value = allTaskDetails.sort((a, b) => b.timestamp - a.timestamp);
    console.log('成功加载所有任务数据:', allTasks.value);
    
  } catch (error) {
    console.error('加载任务数据失败:', error);
    ElMessage.error(`加载任务数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const refreshData = () => {
  loadAllTasks();
};

// 单个任务下载
const downloadSingle = async (task: DownloadableTask) => {
  await downloadTasks([task]);
};

// 批量下载
const handleBatchDownload = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要下载的任务');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要下载选中的 ${selectedTasks.value.length} 个任务吗？`,
      '确认批量下载',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    await downloadTasks(selectedTasks.value);
  } catch {
    // 用户取消操作
  }
};

// 下载任务文件
const downloadTasks = async (tasks: DownloadableTask[]) => {
  try {
    // 收集所有文件路径
    const allFilePaths: string[] = [];
    
    tasks.forEach(task => {
      if (task.output_files?.ai_output_path) {
        allFilePaths.push(task.output_files.ai_output_path);
      }
      if (task.output_files?.final_output_path) {
        allFilePaths.push(task.output_files.final_output_path);
      }
    });

    if (allFilePaths.length === 0) {
      ElMessage.error('没有找到可下载的文件');
      return;
    }

    console.log('准备下载文件:', allFilePaths);
    ElMessage.info(`正在准备下载 ${tasks.length} 个任务的文件...`);

    // 调用下载API
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const downloadUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/download-data/`;

    const response = await fetch(downloadUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_paths: allFilePaths
      })
    });

    if (!response.ok) {
      throw new Error(`下载请求失败: ${response.status} ${response.statusText}`);
    }

    // 获取文件blob
    const blob = await response.blob();
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // 设置下载文件名
    const fileName = tasks.length === 1 
      ? `analysis_data_${tasks[0].task_id}_${new Date().getTime()}.zip`
      : `batch_analysis_data_${tasks.length}tasks_${new Date().getTime()}.zip`;
    link.download = fileName;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success(`成功下载 ${tasks.length} 个任务的文件`);
    console.log('批量下载完成:', fileName);

  } catch (error) {
    console.error('批量下载失败:', error);
    ElMessage.error(`下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 组件挂载时的初始化
onMounted(() => {
  // 组件挂载时不自动加载数据，等待弹窗打开时再加载
});
</script>

<style scoped lang="scss">
.batch-download-content {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .statistics-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
  }

  .task-list-section {
    .task-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-tag {
        flex-shrink: 0;
      }
    }

    .download-files {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .file-tag {
        font-size: 11px;
      }

      .no-files {
        color: #909399;
        font-size: 12px;
      }
    }

    .pagination-section {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
