import{aa as ga,a3 as _a,E as h,a8 as qe,q as ka,__tla as wa}from"./index.BSP3cg_z.js";import{d as ba,k,w as K,c as U,o as $a,B as v,m as Ta,e as S,b as c,v as u,f as o,a as V,D as Q,G as b,E as m,t as s,u as Ie,q as Me,F as P,p as R,y as Be}from"./vue.CnN__PXn.js";let Pe,Va=Promise.all([(()=>{try{return wa}catch{}})()]).then(async()=>{let W,X,Z,ee,ae,le,te,se,ue,ne,oe,ie,re,de,ve,ce,pe,me,he,ye,fe,ge,_e,ke,we,be,$e,Te,Ve,Ee;W={class:"dialog-header"},X={class:"page-switch"},Z={class:"batch-analysis-content"},ee={class:"content-layout"},ae={class:"left-section"},le={class:"section-header"},te={class:"selection-info"},se={class:"count"},ue={class:"search-section"},ne={key:0,class:"batch-actions"},oe={class:"task-list"},ie={class:"pagination"},re={class:"right-section"},de={key:0,class:"config-section"},ve={key:1,class:"delete-section"},ce={class:"delete-warning"},pe={class:"delete-statistics"},me={key:2,class:"preview-section"},he={class:"section-header"},ye={class:"preview-info"},fe={class:"count"},ge={class:"preview-list"},_e={key:0,class:"empty-preview"},ke={key:1},we={class:"preview-content"},be={class:"task-info"},$e={class:"task-name"},Te={class:"image-name"},Ve={class:"config-info"},Ee={class:"dialog-footer"},Pe=ka(ba({__name:"BatchAnalysisDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(Re,{emit:Ge}){const Oe=Re,He=Ge,I=k(!1),G=k(!1),O=k(!1),N=k(!1),Ce=k([]),Se=k([]),i=k([]),D=k(),L=k(""),Ae=k(""),Y=k(null),z=k(1),H=k(20),f=k("analysis"),l=k({taskType:"",modelType:"",modelName:"",shpFile:"",areaThreshold:400}),T=k({});K(()=>Oe.modelValue,a=>{I.value=a,a&&(je(),ia())}),K(()=>I.value,a=>{He("update:modelValue",a)});const C=U(()=>{let a=Ce.value;if(a=a.filter(e=>e.status==="\u5B8C\u6210"),L.value){const e=L.value.toLowerCase();a=a.filter(n=>n.name.toLowerCase().includes(e)||n.task_id.toLowerCase().includes(e))}if(Y.value&&Y.value.length===2){const[e,n]=Y.value;a=a.filter(d=>{if(!d.end_time)return!1;const g=d.end_time.split(" ")[0];return g>=e&&g<=n})}return a});K(()=>C.value,()=>{Be(()=>{ze()})},{deep:!0});const Ne=U(()=>{let a=Se.value;if(L.value){const e=L.value.toLowerCase();a=a.filter(n=>n.name.toLowerCase().includes(e)||n.task_id.toLowerCase().includes(e))}return a}),Je=U(()=>Object.keys(T.value).map(a=>({value:a,label:T.value[a].display_name}))),Ke=U(()=>{if(!l.value.taskType||!T.value[l.value.taskType])return[];const a=T.value[l.value.taskType].models;return Object.keys(a).map(e=>({value:e,label:e}))}),De=U(()=>!l.value.taskType||!l.value.modelType?[]:(T.value[l.value.taskType].models[l.value.modelType]||[]).map(a=>({value:a.name,label:a.name,path:a.path}))),Qe=U(()=>!l.value.taskType||!T.value[l.value.taskType]?[]:T.value[l.value.taskType].shp_files.map(a=>{var e;return{value:a,label:((e=a.split(/[/\\]/).pop())==null?void 0:e.replace(/\.[^/.]+$/,""))||a}})),M=U(()=>{if(i.value.length===0||!l.value.taskType||!l.value.modelName)return[];const a=T.value[l.value.taskType],e=a?a.display_name:l.value.taskType;return i.value.map(n=>({taskName:`${e}_${n.task_id}`,imageName:n.name,analysisType:e,modelName:l.value.modelName,imageId:n.task_id}))}),xe=U(()=>i.value.length>0&&l.value.taskType&&l.value.modelType&&l.value.modelName&&l.value.shpFile&&!N.value),A=()=>{I.value=!1,We()},We=()=>{i.value=[],L.value="",Ae.value="",Y.value=null,l.value={taskType:"",modelType:"",modelName:"",shpFile:"",areaThreshold:400},z.value=1},Xe=()=>{z.value=1},Ze=()=>{z.value=1},ea=()=>{i.value=[...C.value],D.value&&(D.value.clearSelection(),C.value.forEach(a=>{D.value.toggleRowSelection(a,!0)})),h.success(`\u5DF2\u9009\u62E9 ${C.value.length} \u4E2A\u5F71\u50CF`)},aa=()=>{i.value=[],D.value&&D.value.clearSelection(),h.info("\u5DF2\u6E05\u7A7A\u9009\u62E9")},ze=()=>{!D.value||f.value!=="analysis"||(D.value.clearSelection(),C.value.forEach(a=>{i.value.some(e=>e.task_id===a.task_id)&&D.value.toggleRowSelection(a,!0)}))},Ue=a=>{if(f.value==="analysis"){const e=C.value;i.value=i.value.filter(n=>!e.some(d=>d.task_id===n.task_id)),i.value=[...i.value,...a]}else i.value=a},la=a=>{H.value=a,z.value=1},ta=a=>{z.value=a,Be(()=>{ze()})},sa=()=>{i.value=[],z.value=1,f.value==="delete"&&J()},Le=a=>({\u5B8C\u6210:"success",\u8FDB\u884C\u4E2D:"warning",\u7B49\u5F85\u4E2D:"info",\u5931\u8D25:"danger"})[a]||"info",ua=()=>{if(l.value.taskType&&T.value[l.value.taskType]){const a=T.value[l.value.taskType],e=a.default,[n,d]=e.split("/");l.value.modelType=n,l.value.modelName=d,a.shp_files.length>0&&(l.value.shpFile=a.shp_files[0]),l.value.areaThreshold=a.default_area||400}else l.value.modelType="",l.value.modelName="",l.value.shpFile="",l.value.areaThreshold=400},na=()=>{l.value.modelName=""},je=async()=>{G.value=!0;try{const a="http://192.168.43.148:8091/api/map/odm/tasks",e=await fetch(a);if(!e.ok)throw new Error(`\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25: ${e.status}`);const n=await e.json();if(n.status!=="success"||!n.tasks)throw new Error("\u4EFB\u52A1\u5217\u8868\u6570\u636E\u683C\u5F0F\u9519\u8BEF");Ce.value=n.tasks}catch(a){h.error(`\u52A0\u8F7D\u4EFB\u52A1\u5217\u8868\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}finally{G.value=!1}},oa=()=>{f.value==="analysis"?je():J()},J=async()=>{O.value=!0;try{const a="192.168.43.148",e="8091",n=`http://${a}:${e}/api/map/odm/tasks`,d=await fetch(n);if(!d.ok)throw new Error(`\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25: ${d.status}`);const g=await d.json();if(g.status!=="success"||!g.tasks)throw new Error("\u4E3B\u4EFB\u52A1\u5217\u8868\u6570\u636E\u683C\u5F0F\u9519\u8BEF");const w=[];for(const $ of g.tasks)try{const p=`http://${a}:${e}/api/analysis/taskinfo/?id=${$.task_id}`,y=await fetch(p);if(y.ok){const r=await y.json();r.status==="success"&&r.data&&r.data.forEach(_=>{const q={arableLand:"\u8015\u5730\u5206\u6790",constructionLand:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790"}[_.analysis_category]||"\u5206\u6790",x=new Date(1e3*_.timestamp),E=x.getFullYear().toString()+(x.getMonth()+1).toString().padStart(2,"0")+x.getDate().toString().padStart(2,"0")+x.getHours().toString().padStart(2,"0")+x.getMinutes().toString().padStart(2,"0");w.push({task_id:_.task_id,name:`${q}_${E}`,status:_.status,createTime:_.datetime,analysis_category:_.analysis_category,timestamp:_.timestamp,parent_task_id:$.task_id})})}}catch{}Se.value=w}catch(a){h.error(`\u52A0\u8F7D\u5220\u9664\u4EFB\u52A1\u6570\u636E\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}finally{O.value=!1}},ia=async()=>{try{const a="http://192.168.43.148:8091/api/analysis/weight-info/",e=await fetch(a);if(!e.ok)throw new Error(`\u83B7\u53D6\u6743\u91CD\u4FE1\u606F\u5931\u8D25: ${e.status}`);const n=await e.json();if(n.status!=="success"||!n.data)throw new Error("\u6743\u91CD\u4FE1\u606F\u6570\u636E\u683C\u5F0F\u9519\u8BEF");T.value=n.data}catch(a){h.error(`\u52A0\u8F7D\u6743\u91CD\u4FE1\u606F\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}},ra=async()=>{if(xe.value)try{const a=T.value[l.value.taskType],e=a?a.display_name:l.value.taskType;await qe.confirm(`\u786E\u5B9A\u8981\u4E3A ${i.value.length} \u4E2A\u5F71\u50CF\u521B\u5EFA ${e} \u4EFB\u52A1\u5417\uFF1F`,"\u786E\u8BA4\u6279\u91CF\u63D0\u4EA4",{confirmButtonText:"\u786E\u5B9A\u63D0\u4EA4",cancelButtonText:"\u53D6\u6D88",type:"info"}),N.value=!0,h.info(`\u5F00\u59CB\u6279\u91CF\u63D0\u4EA4 ${M.value.length} \u4E2A\u5206\u6790\u4EFB\u52A1...`);const n="http://192.168.43.148:8091";let d=0,g=0;const w=[];for(let $=0;$<i.value.length;$++){const p=i.value[$];try{const y=De.value.find(F=>F.value===l.value.modelName),r=(y==null?void 0:y.path)||"";if(!r)throw new Error("\u65E0\u6CD5\u83B7\u53D6\u6A21\u578B\u8DEF\u5F84");const _=`D:/Drone_Project/nginxData/ODM/Output/${p.task_id}/${p.task_id}_out.tif`,q={id:p.task_id,image:_,model:r,old_data_path:l.value.shpFile,area_threshold:l.value.areaThreshold.toString()},x=`${n}/api/analysis/queued-combined-ai-spatial-analysis/?${new URLSearchParams(q).toString()}`,E=await fetch(x,{method:"GET",headers:{"Content-Type":"application/json"}});if(!E.ok)throw new Error(`HTTP ${E.status}: ${E.statusText}`);const j=await E.json();if(j.success!==!0)throw new Error(j.message||"API\u8FD4\u56DE\u5931\u8D25\u72B6\u6001");d++}catch(y){g++;const r=`\u4EFB\u52A1 ${p.name}: ${y instanceof Error?y.message:"\u672A\u77E5\u9519\u8BEF"}`;w.push(r)}h.info(`\u6279\u91CF\u63D0\u4EA4\u8FDB\u5EA6: ${$+1}/${i.value.length}`)}d===i.value.length?h.success(`\u6279\u91CF\u63D0\u4EA4\u5B8C\u6210\uFF01\u6210\u529F\u63D0\u4EA4 ${d} \u4E2A\u5206\u6790\u4EFB\u52A1`):d>0?(h.warning(`\u6279\u91CF\u63D0\u4EA4\u5B8C\u6210\uFF01\u6210\u529F ${d} \u4E2A\uFF0C\u5931\u8D25 ${g} \u4E2A`),w.length):(h.error(`\u6279\u91CF\u63D0\u4EA4\u5931\u8D25\uFF01\u6240\u6709 ${g} \u4E2A\u4EFB\u52A1\u90FD\u63D0\u4EA4\u5931\u8D25`),w.length),A()}catch(a){a!=="cancel"&&h.error(`\u6279\u91CF\u63D0\u4EA4\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}finally{N.value=!1}},da=async()=>{if(i.value.length!==0)try{await qe.confirm(`\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ${i.value.length} \u4E2A\u5206\u6790\u4EFB\u52A1\u5417\uFF1F

\u5220\u9664\u540E\u5C06\u65E0\u6CD5\u6062\u590D\u4EFB\u52A1\u6570\u636E\u548C\u5206\u6790\u7ED3\u679C\u3002`,"\u786E\u8BA4\u6279\u91CF\u5220\u9664",{confirmButtonText:"\u786E\u5B9A\u5220\u9664",cancelButtonText:"\u53D6\u6D88",type:"warning",dangerouslyUseHTMLString:!1}),N.value=!0,h.info(`\u5F00\u59CB\u6279\u91CF\u5220\u9664 ${i.value.length} \u4E2A\u5206\u6790\u4EFB\u52A1...`);const a="192.168.43.148",e="8091";let n=0,d=0;const g=[];for(let w=0;w<i.value.length;w++){const $=i.value[w];try{const p=`http://${a}:${e}/api/analysis/delete-task/?id=${$.parent_task_id}&task_id=${$.task_id}`,y=await fetch(p,{method:"GET"});let r;try{r=await y.json()}catch{throw new Error(`\u5220\u9664\u8BF7\u6C42\u5931\u8D25: ${y.status} ${y.statusText}`)}if(r.success!==!0)throw new Error(r.message||"\u5220\u9664\u5931\u8D25\uFF0C\u670D\u52A1\u5668\u8FD4\u56DE\u5F02\u5E38\u72B6\u6001");n++}catch(p){d++;const y=`\u4EFB\u52A1 ${$.name}: ${p instanceof Error?p.message:"\u672A\u77E5\u9519\u8BEF"}`;g.push(y)}h.info(`\u6279\u91CF\u5220\u9664\u8FDB\u5EA6: ${w+1}/${i.value.length}`)}n===i.value.length?h.success(`\u6279\u91CF\u5220\u9664\u5B8C\u6210\uFF01\u6210\u529F\u5220\u9664 ${n} \u4E2A\u5206\u6790\u4EFB\u52A1`):n>0?(h.warning(`\u6279\u91CF\u5220\u9664\u5B8C\u6210\uFF01\u6210\u529F ${n} \u4E2A\uFF0C\u5931\u8D25 ${d} \u4E2A`),g.length):(h.error(`\u6279\u91CF\u5220\u9664\u5931\u8D25\uFF01\u6240\u6709 ${d} \u4E2A\u4EFB\u52A1\u90FD\u5220\u9664\u5931\u8D25`),g.length),await J(),i.value=[]}catch(a){a!=="cancel"&&h.error(`\u6279\u91CF\u5220\u9664\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}finally{N.value=!1}else h.warning("\u8BF7\u5148\u9009\u62E9\u8981\u5220\u9664\u7684\u4EFB\u52A1")};return $a(()=>{}),(a,e)=>{const n=v("el-radio-button"),d=v("el-radio-group"),g=v("el-input"),w=v("el-col"),$=v("el-date-picker"),p=v("el-button"),y=v("el-row"),r=v("el-table-column"),_=v("el-tag"),q=v("el-table"),x=v("el-pagination"),E=v("el-option"),j=v("el-select"),F=v("el-form-item"),va=v("el-input-number"),ca=v("el-form"),pa=v("el-alert"),Fe=v("el-descriptions-item"),ma=v("el-descriptions"),ha=v("el-empty"),ya=v("el-scrollbar"),fa=v("el-dialog"),Ye=Ta("loading");return c(),S(fa,{modelValue:I.value,"onUpdate:modelValue":e[10]||(e[10]=t=>I.value=t),width:"90%","close-on-click-modal":!1,onClose:A},{header:u(()=>[o("div",W,[e[13]||(e[13]=o("span",{class:"dialog-title"},"\u6279\u91CF\u5206\u6790\u4E0E\u5220\u9664",-1)),o("div",X,[s(d,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=t=>f.value=t),onChange:sa},{default:u(()=>[s(n,{value:"analysis"},{default:u(()=>e[11]||(e[11]=[m("\u6279\u91CF\u5206\u6790")])),_:1}),s(n,{value:"delete"},{default:u(()=>e[12]||(e[12]=[m("\u6279\u91CF\u5220\u9664")])),_:1})]),_:1},8,["modelValue"])])])]),footer:u(()=>[o("div",Ee,[s(p,{onClick:A},{default:u(()=>e[25]||(e[25]=[m("\u53D6\u6D88")])),_:1}),f.value==="analysis"?(c(),S(p,{key:0,type:"primary",disabled:!xe.value,loading:N.value,onClick:ra},{default:u(()=>[m(b(N.value?"\u63D0\u4EA4\u4E2D...":`\u6279\u91CF\u63D0\u4EA4 (${M.value.length})`),1)]),_:1},8,["disabled","loading"])):(c(),S(p,{key:1,type:"danger",disabled:i.value.length===0,loading:N.value,onClick:da},{default:u(()=>[m(b(N.value?"\u5220\u9664\u4E2D...":`\u6279\u91CF\u5220\u9664 (${i.value.length})`),1)]),_:1},8,["disabled","loading"]))])]),default:u(()=>[o("div",Z,[o("div",ee,[o("div",ae,[o("div",le,[o("h4",null,b(f.value==="analysis"?"\u9009\u62E9\u5F71\u50CF":"\u9009\u62E9\u8981\u5220\u9664\u7684\u4EFB\u52A1"),1),o("div",te,[e[14]||(e[14]=m(" \u5DF2\u9009\u62E9 ")),o("span",se,b(i.value.length),1),m(" \u4E2A"+b(f.value==="analysis"?"\u5F71\u50CF":"\u4EFB\u52A1"),1)])]),o("div",ue,[s(y,{gutter:12},{default:u(()=>[s(w,{span:12},{default:u(()=>[s(g,{modelValue:L.value,"onUpdate:modelValue":e[1]||(e[1]=t=>L.value=t),placeholder:f.value==="analysis"?"\u641C\u7D22\u5F71\u50CF\u540D\u79F0\u6216ID":"\u641C\u7D22\u4EFB\u52A1\u540D\u79F0\u6216ID","prefix-icon":Ie(ga),clearable:"",onInput:Xe},null,8,["modelValue","placeholder","prefix-icon"])]),_:1}),f.value==="analysis"?(c(),S(w,{key:0,span:8},{default:u(()=>[s($,{modelValue:Y.value,"onUpdate:modelValue":e[2]||(e[2]=t=>Y.value=t),type:"daterange","range-separator":"\u81F3","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:Ze,clearable:""},null,8,["modelValue"])]),_:1})):Q("",!0),s(w,{span:4},{default:u(()=>[s(p,{type:"primary",icon:Ie(_a),onClick:oa},{default:u(()=>e[15]||(e[15]=[m("\u5237\u65B0")])),_:1},8,["icon"])]),_:1})]),_:1})]),f.value==="analysis"?(c(),V("div",ne,[s(p,{size:"small",type:"primary",onClick:ea,disabled:C.value.length===0},{default:u(()=>[m(" \u5168\u9009\u5F53\u524D\u7B5B\u9009\u7ED3\u679C ("+b(C.value.length)+") ",1)]),_:1},8,["disabled"]),s(p,{size:"small",onClick:aa,disabled:i.value.length===0},{default:u(()=>e[16]||(e[16]=[m(" \u6E05\u7A7A\u9009\u62E9 ")])),_:1},8,["disabled"])])):Q("",!0),o("div",oe,[f.value==="analysis"?Me((c(),S(q,{key:0,ref_key:"analysisTableRef",ref:D,data:C.value,onSelectionChange:Ue,height:"350",stripe:""},{default:u(()=>[s(r,{type:"selection",width:"55"}),s(r,{prop:"task_id",label:"\u5F71\u50CFID",width:"350"}),s(r,{prop:"status",label:"\u72B6\u6001",width:"100"},{default:u(({row:t})=>[s(_,{type:Le(t.status),size:"small"},{default:u(()=>[m(b(t.status),1)]),_:2},1032,["type"])]),_:1}),s(r,{prop:"end_time",label:"\u5B8C\u6210\u65F6\u95F4",width:"180"})]),_:1},8,["data"])),[[Ye,G.value]]):Me((c(),S(q,{key:1,data:Ne.value,onSelectionChange:Ue,height:"350",stripe:""},{default:u(()=>[s(r,{type:"selection",width:"55"}),s(r,{prop:"parent_task_id",label:"\u5F71\u50CFID",width:"200"}),s(r,{prop:"name",label:"\u4EFB\u52A1\u540D\u79F0","min-width":"200"}),s(r,{prop:"status",label:"\u72B6\u6001",width:"100"},{default:u(({row:t})=>[s(_,{type:Le(t.status),size:"small"},{default:u(()=>[m(b(t.status),1)]),_:2},1032,["type"])]),_:1}),s(r,{prop:"createTime",label:"\u521B\u5EFA\u65F6\u95F4",width:"180"}),s(r,{prop:"analysis_category",label:"\u5206\u6790\u7C7B\u578B",width:"120"},{default:u(({row:t})=>{return[m(b((B=t.analysis_category,{arableLand:"\u8015\u5730\u5206\u6790",constructionLand:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790"}[B]||B)),1)];var B}),_:1})]),_:1},8,["data"])),[[Ye,O.value]]),o("div",ie,[s(x,{"current-page":z.value,"onUpdate:currentPage":e[3]||(e[3]=t=>z.value=t),"page-size":H.value,"onUpdate:pageSize":e[4]||(e[4]=t=>H.value=t),"page-sizes":[10,20,50],total:f.value==="analysis"?C.value.length:Ne.value.length,layout:"total, sizes, prev, pager, next",onSizeChange:la,onCurrentChange:ta},null,8,["current-page","page-size","total"])])])]),o("div",re,[f.value==="analysis"?(c(),V("div",de,[e[18]||(e[18]=o("div",{class:"section-header"},[o("h4",null,"\u5206\u6790\u914D\u7F6E")],-1)),s(ca,{model:l.value,"label-width":"120px",size:"default"},{default:u(()=>[s(F,{label:"\u4EFB\u52A1\u7C7B\u578B",required:""},{default:u(()=>[s(j,{modelValue:l.value.taskType,"onUpdate:modelValue":e[5]||(e[5]=t=>l.value.taskType=t),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u7C7B\u578B",onChange:ua},{default:u(()=>[(c(!0),V(P,null,R(Je.value,t=>(c(),S(E,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(F,{label:"\u6A21\u578B\u7C7B\u578B",required:""},{default:u(()=>[s(j,{modelValue:l.value.modelType,"onUpdate:modelValue":e[6]||(e[6]=t=>l.value.modelType=t),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B\u7C7B\u578B",disabled:!l.value.taskType,onChange:na},{default:u(()=>[(c(!0),V(P,null,R(Ke.value,t=>(c(),S(E,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),s(F,{label:"\u6A21\u578B\u540D\u79F0",required:""},{default:u(()=>[s(j,{modelValue:l.value.modelName,"onUpdate:modelValue":e[7]||(e[7]=t=>l.value.modelName=t),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B\u540D\u79F0",disabled:!l.value.modelType},{default:u(()=>[(c(!0),V(P,null,R(De.value,t=>(c(),S(E,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),s(F,{label:"\u5F85\u5BF9\u6BD4\u5BF9\u8C61",required:""},{default:u(()=>[s(j,{modelValue:l.value.shpFile,"onUpdate:modelValue":e[8]||(e[8]=t=>l.value.shpFile=t),placeholder:"\u8BF7\u9009\u62E9\u5F85\u5BF9\u6BD4\u7684\u5BF9\u8C61",disabled:!l.value.taskType},{default:u(()=>[(c(!0),V(P,null,R(Qe.value,t=>(c(),S(E,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),s(F,{label:"\u8FC7\u6EE4\u9762\u79EF",required:""},{default:u(()=>[s(va,{modelValue:l.value.areaThreshold,"onUpdate:modelValue":e[9]||(e[9]=t=>l.value.areaThreshold=t),min:1,max:1e4,step:10,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u8FC7\u6EE4\u9762\u79EF\u9608\u503C"},null,8,["modelValue"]),e[17]||(e[17]=o("div",{class:"form-item-tip"},"\u5355\u4F4D\uFF1A\u5E73\u65B9\u7C73\uFF0C\u7528\u4E8E\u8FC7\u6EE4\u5C0F\u4E8E\u8BE5\u9762\u79EF\u7684\u533A\u57DF",-1))]),_:1})]),_:1},8,["model"])])):(c(),V("div",ve,[e[21]||(e[21]=o("div",{class:"section-header"},[o("h4",null,"\u5220\u9664\u786E\u8BA4")],-1)),o("div",ce,[s(pa,{title:"\u5220\u9664\u8B66\u544A",type:"warning",closable:!1,"show-icon":""},{default:u(()=>e[19]||(e[19]=[o("p",null,"\u60A8\u5373\u5C06\u6279\u91CF\u5220\u9664\u9009\u4E2D\u7684\u5206\u6790\u4EFB\u52A1\uFF0C\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\uFF01",-1),o("p",null,"\u5220\u9664\u540E\u5C06\u65E0\u6CD5\u6062\u590D\u4EFB\u52A1\u6570\u636E\u548C\u5206\u6790\u7ED3\u679C\u3002",-1),o("p",null,"\u8BF7\u786E\u8BA4\u60A8\u8981\u5220\u9664\u7684\u4EFB\u52A1\u65E0\u8BEF\u540E\u518D\u6267\u884C\u64CD\u4F5C\u3002",-1)])),_:1})]),o("div",pe,[s(ma,{column:2,border:""},{default:u(()=>[s(Fe,{label:"\u9009\u4E2D\u4EFB\u52A1\u6570"},{default:u(()=>[m(b(i.value.length),1)]),_:1}),s(Fe,{label:"\u5220\u9664\u98CE\u9669"},{default:u(()=>[s(_,{type:"danger"},{default:u(()=>e[20]||(e[20]=[m("\u9AD8\u98CE\u9669\u64CD\u4F5C")])),_:1})]),_:1})]),_:1})])])),f.value==="analysis"?(c(),V("div",me,[o("div",he,[e[24]||(e[24]=o("h4",null,"\u4EFB\u52A1\u9884\u89C8",-1)),o("div",ye,[e[22]||(e[22]=m(" \u5C06\u521B\u5EFA ")),o("span",fe,b(M.value.length),1),e[23]||(e[23]=m(" \u4E2A\u5206\u6790\u4EFB\u52A1 "))])]),o("div",ge,[s(ya,{height:"150px"},{default:u(()=>[M.value.length===0?(c(),V("div",_e,[s(ha,{description:"\u8BF7\u5148\u9009\u62E9\u5F71\u50CF\u548C\u914D\u7F6E\u5206\u6790\u53C2\u6570","image-size":80})])):(c(),V("div",ke,[(c(!0),V(P,null,R(M.value,(t,B)=>(c(),V("div",{key:B,class:"preview-item"},[o("div",we,[o("div",be,[o("span",$e,b(t.taskName),1),o("span",Te,b(t.imageName),1)]),o("div",Ve,[s(_,{size:"small",type:"info"},{default:u(()=>[m(b(t.analysisType),1)]),_:2},1024),s(_,{size:"small",type:"success"},{default:u(()=>[m(b(t.modelName),1)]),_:2},1024)])])]))),128))]))]),_:1})])])):Q("",!0)])])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-98330410"]])});export{Va as __tla,Pe as default};
