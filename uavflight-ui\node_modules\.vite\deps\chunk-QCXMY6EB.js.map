{"version": 3, "sources": ["../../ol/tilegrid/WMTS.js"], "sourcesContent": ["/**\n * @module ol/tilegrid/WMTS\n */\n\nimport TileGrid from './TileGrid.js';\nimport {get as getProjection} from '../proj.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../extent.js\").Extent} [extent] Extent for the tile grid. No tiles\n * outside this extent will be requested by {@link module:ol/source/Tile~TileSource} sources.\n * When no `origin` or `origins` are configured, the `origin` will be set to the\n * top-left corner of the extent.\n * @property {import(\"../coordinate.js\").Coordinate} [origin] The tile grid origin, i.e.\n * where the `x` and `y` axes meet (`[z, 0, 0]`). Tile coordinates increase left\n * to right and downwards. If not specified, `extent` or `origins` must be provided.\n * @property {Array<import(\"../coordinate.js\").Coordinate>} [origins] Tile grid origins,\n * i.e. where the `x` and `y` axes meet (`[z, 0, 0]`), for each zoom level. If\n * given, the array length should match the length of the `resolutions` array, i.e.\n * each resolution can have a different origin. Tile coordinates increase left to\n * right and downwards. If not specified, `extent` or `origin` must be provided.\n * @property {!Array<number>} resolutions Resolutions. The array index of each\n * resolution needs to match the zoom level. This means that even if a `minZoom`\n * is configured, the resolutions array will have a length of `maxZoom + 1`\n * @property {!Array<string>} matrixIds matrix IDs. The length of this array needs\n * to match the length of the `resolutions` array.\n * @property {Array<import(\"../size.js\").Size>} [sizes] Number of tile rows and columns\n * of the grid for each zoom level. The values here are the `TileMatrixWidth` and\n * `TileMatrixHeight` advertised in the GetCapabilities response of the WMTS, and\n * define each zoom level's extent together with the `origin` or `origins`.\n * A grid `extent` can be configured in addition, and will further limit the extent for\n * which tile requests are made by sources. If the bottom-left corner of\n * an extent is used as `origin` or `origins`, then the `y` value must be\n * negative because OpenLayers tile coordinates use the top left as the origin.\n * @property {number|import(\"../size.js\").Size} [tileSize] Tile size.\n * @property {Array<number|import(\"../size.js\").Size>} [tileSizes] Tile sizes. The length of\n * this array needs to match the length of the `resolutions` array.\n */\n\n/**\n * @classdesc\n * Set the grid pattern for sources accessing WMTS tiled-image servers.\n * @api\n */\nclass WMTSTileGrid extends TileGrid {\n  /**\n   * @param {Options} options WMTS options.\n   */\n  constructor(options) {\n    super({\n      extent: options.extent,\n      origin: options.origin,\n      origins: options.origins,\n      resolutions: options.resolutions,\n      tileSize: options.tileSize,\n      tileSizes: options.tileSizes,\n      sizes: options.sizes,\n    });\n\n    /**\n     * @private\n     * @type {!Array<string>}\n     */\n    this.matrixIds_ = options.matrixIds;\n  }\n\n  /**\n   * @param {number} z Z.\n   * @return {string} MatrixId..\n   */\n  getMatrixId(z) {\n    return this.matrixIds_[z];\n  }\n\n  /**\n   * Get the list of matrix identifiers.\n   * @return {Array<string>} MatrixIds.\n   * @api\n   */\n  getMatrixIds() {\n    return this.matrixIds_;\n  }\n}\n\nexport default WMTSTileGrid;\n\n/**\n * Create a tile grid from a WMTS capabilities matrix set and an\n * optional TileMatrixSetLimits.\n * @param {Object} matrixSet An object representing a matrixSet in the\n *     capabilities document.\n * @param {import(\"../extent.js\").Extent} [extent] An optional extent to restrict the tile\n *     ranges the server provides.\n * @param {Array<Object>} [matrixLimits] An optional object representing\n *     the available matrices for tileGrid.\n * @return {WMTSTileGrid} WMTS tileGrid instance.\n * @api\n */\nexport function createFromCapabilitiesMatrixSet(\n  matrixSet,\n  extent,\n  matrixLimits\n) {\n  /** @type {!Array<number>} */\n  const resolutions = [];\n  /** @type {!Array<string>} */\n  const matrixIds = [];\n  /** @type {!Array<import(\"../coordinate.js\").Coordinate>} */\n  const origins = [];\n  /** @type {!Array<number|import(\"../size.js\").Size>} */\n  const tileSizes = [];\n  /** @type {!Array<import(\"../size.js\").Size>} */\n  const sizes = [];\n\n  matrixLimits = matrixLimits !== undefined ? matrixLimits : [];\n\n  const supportedCRSPropName = 'SupportedCRS';\n  const matrixIdsPropName = 'TileMatrix';\n  const identifierPropName = 'Identifier';\n  const scaleDenominatorPropName = 'ScaleDenominator';\n  const topLeftCornerPropName = 'TopLeftCorner';\n  const tileWidthPropName = 'TileWidth';\n  const tileHeightPropName = 'TileHeight';\n\n  const code = matrixSet[supportedCRSPropName];\n  const projection = getProjection(code);\n  const metersPerUnit = projection.getMetersPerUnit();\n  // swap origin x and y coordinates if axis orientation is lat/long\n  const switchOriginXY = projection.getAxisOrientation().substr(0, 2) == 'ne';\n\n  matrixSet[matrixIdsPropName].sort(function (a, b) {\n    return b[scaleDenominatorPropName] - a[scaleDenominatorPropName];\n  });\n\n  matrixSet[matrixIdsPropName].forEach(function (elt) {\n    let matrixAvailable;\n    // use of matrixLimits to filter TileMatrices from GetCapabilities\n    // TileMatrixSet from unavailable matrix levels.\n    if (matrixLimits.length > 0) {\n      matrixAvailable = matrixLimits.find(function (elt_ml) {\n        if (elt[identifierPropName] == elt_ml[matrixIdsPropName]) {\n          return true;\n        }\n        // Fallback for tileMatrix identifiers that don't get prefixed\n        // by their tileMatrixSet identifiers.\n        if (!elt[identifierPropName].includes(':')) {\n          return (\n            matrixSet[identifierPropName] + ':' + elt[identifierPropName] ===\n            elt_ml[matrixIdsPropName]\n          );\n        }\n        return false;\n      });\n    } else {\n      matrixAvailable = true;\n    }\n\n    if (matrixAvailable) {\n      matrixIds.push(elt[identifierPropName]);\n      const resolution =\n        (elt[scaleDenominatorPropName] * 0.28e-3) / metersPerUnit;\n      const tileWidth = elt[tileWidthPropName];\n      const tileHeight = elt[tileHeightPropName];\n      if (switchOriginXY) {\n        origins.push([\n          elt[topLeftCornerPropName][1],\n          elt[topLeftCornerPropName][0],\n        ]);\n      } else {\n        origins.push(elt[topLeftCornerPropName]);\n      }\n      resolutions.push(resolution);\n      tileSizes.push(\n        tileWidth == tileHeight ? tileWidth : [tileWidth, tileHeight]\n      );\n      sizes.push([elt['MatrixWidth'], elt['MatrixHeight']]);\n    }\n  });\n\n  return new WMTSTileGrid({\n    extent: extent,\n    origins: origins,\n    resolutions: resolutions,\n    matrixIds: matrixIds,\n    tileSizes: tileSizes,\n    sizes: sizes,\n  });\n}\n"], "mappings": ";;;;;;;;AA4CA,IAAM,eAAN,cAA2B,iBAAS;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB,SAAS,QAAQ;AAAA,MACjB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,MAClB,WAAW,QAAQ;AAAA,MACnB,OAAO,QAAQ;AAAA,IACjB,CAAC;AAMD,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG;AACb,WAAO,KAAK,WAAW,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,eAAQ;AAcR,SAAS,gCACd,WACA,QACA,cACA;AAEA,QAAM,cAAc,CAAC;AAErB,QAAM,YAAY,CAAC;AAEnB,QAAM,UAAU,CAAC;AAEjB,QAAM,YAAY,CAAC;AAEnB,QAAM,QAAQ,CAAC;AAEf,iBAAe,iBAAiB,SAAY,eAAe,CAAC;AAE5D,QAAM,uBAAuB;AAC7B,QAAM,oBAAoB;AAC1B,QAAM,qBAAqB;AAC3B,QAAM,2BAA2B;AACjC,QAAM,wBAAwB;AAC9B,QAAM,oBAAoB;AAC1B,QAAM,qBAAqB;AAE3B,QAAM,OAAO,UAAU,oBAAoB;AAC3C,QAAM,aAAa,IAAc,IAAI;AACrC,QAAM,gBAAgB,WAAW,iBAAiB;AAElD,QAAM,iBAAiB,WAAW,mBAAmB,EAAE,OAAO,GAAG,CAAC,KAAK;AAEvE,YAAU,iBAAiB,EAAE,KAAK,SAAU,GAAG,GAAG;AAChD,WAAO,EAAE,wBAAwB,IAAI,EAAE,wBAAwB;AAAA,EACjE,CAAC;AAED,YAAU,iBAAiB,EAAE,QAAQ,SAAU,KAAK;AAClD,QAAI;AAGJ,QAAI,aAAa,SAAS,GAAG;AAC3B,wBAAkB,aAAa,KAAK,SAAU,QAAQ;AACpD,YAAI,IAAI,kBAAkB,KAAK,OAAO,iBAAiB,GAAG;AACxD,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,IAAI,kBAAkB,EAAE,SAAS,GAAG,GAAG;AAC1C,iBACE,UAAU,kBAAkB,IAAI,MAAM,IAAI,kBAAkB,MAC5D,OAAO,iBAAiB;AAAA,QAE5B;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,wBAAkB;AAAA,IACpB;AAEA,QAAI,iBAAiB;AACnB,gBAAU,KAAK,IAAI,kBAAkB,CAAC;AACtC,YAAM,aACH,IAAI,wBAAwB,IAAI,QAAW;AAC9C,YAAM,YAAY,IAAI,iBAAiB;AACvC,YAAM,aAAa,IAAI,kBAAkB;AACzC,UAAI,gBAAgB;AAClB,gBAAQ,KAAK;AAAA,UACX,IAAI,qBAAqB,EAAE,CAAC;AAAA,UAC5B,IAAI,qBAAqB,EAAE,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,KAAK,IAAI,qBAAqB,CAAC;AAAA,MACzC;AACA,kBAAY,KAAK,UAAU;AAC3B,gBAAU;AAAA,QACR,aAAa,aAAa,YAAY,CAAC,WAAW,UAAU;AAAA,MAC9D;AACA,YAAM,KAAK,CAAC,IAAI,aAAa,GAAG,IAAI,cAAc,CAAC,CAAC;AAAA,IACtD;AAAA,EACF,CAAC;AAED,SAAO,IAAI,aAAa;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;", "names": []}