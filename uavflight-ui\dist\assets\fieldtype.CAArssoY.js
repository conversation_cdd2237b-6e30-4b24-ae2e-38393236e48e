import{s as e,__tla as g}from"./index.BSP3cg_z.js";let r,l,u,d,o,i,s,c=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{u=function(t){return e({url:"/gen/fieldtype/page",method:"get",params:t})},o=function(t){return e({url:"/gen/fieldtype/list",method:"get",params:t})},r=function(t){return e({url:"/gen/fieldtype",method:"post",data:t})},d=function(t){return e({url:"/gen/fieldtype/details/"+t,method:"get"})},l=function(t){return e({url:"/gen/fieldtype",method:"delete",data:t})},i=function(t){return e({url:"/gen/fieldtype",method:"put",data:t})},s=function(t,f,n,p){if(p)return n();var a;(a={columnType:f},e({url:"/gen/fieldtype/details",method:"get",params:a})).then(m=>{m.data!==null?n(new Error("\u7C7B\u578B\u5DF2\u7ECF\u5B58\u5728")):n()})}});export{c as __tla,r as a,l as d,u as f,d as g,o as l,i as p,s as v};
