const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BT5m4j5J.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/index.BfwuS9c-.css"])))=>i.map(i=>d[i]);
import{w as o,a as c,__tla as i}from"./index.BSP3cg_z.js";import{d as e,s as d,B as m,q as p,x as f,u as s,e as h,b as y,v as w,t as x,j as E}from"./vue.CnN__PXn.js";let r,P=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{let t;t=e({name:"layoutHeader"}),r=e({...t,setup(T){const l=E(()=>c(()=>import("./index.BT5m4j5J.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4]))),_=o(),{isTagsViewCurrenFull:n}=d(_);return(a,V)=>{const u=m("el-header");return p((y(),h(u,{class:"layout-header"},{default:w(()=>[x(s(l))]),_:1},512)),[[f,!s(n)]])}}})});export{P as __tla,r as default};
