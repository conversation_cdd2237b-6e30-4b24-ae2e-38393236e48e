import{E as k,q as E,__tla as P}from"./index.BSP3cg_z.js";import{d as H,c as h,k as Y,w as z,B as i,e as f,b as r,v as o,t as d,a as _,F as b,p as c,f as N,E as S}from"./vue.CnN__PXn.js";let q,A=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{let I;I={class:"dialog-footer"},q=E(H({__name:"TaskConfigDialog",props:{modelValue:{type:Boolean},task:{},weightInfo:{}},emits:["update:modelValue","confirm"],setup(C,{emit:U}){const u=C,F=U,g=h({get:()=>u.modelValue,set:t=>F("update:modelValue",t)}),e=Y({taskType:"",modelType:"",modelName:"",shpFile:"",areaThreshold:400}),x=h(()=>Object.keys(u.weightInfo).map(t=>({value:t,label:u.weightInfo[t].display_name}))),$=h(()=>{if(!e.value.taskType||!u.weightInfo[e.value.taskType])return[];const t=u.weightInfo[e.value.taskType].models;return Object.keys(t).map(a=>({value:a,label:a}))}),D=h(()=>!e.value.taskType||!e.value.modelType?[]:(u.weightInfo[e.value.taskType].models[e.value.modelType]||[]).map(t=>({value:t.name,label:t.name,path:t.path}))),O=h(()=>!e.value.taskType||!u.weightInfo[e.value.taskType]?[]:u.weightInfo[e.value.taskType].shp_files.map(t=>{var a;return{value:t,label:((a=t.split(/[/\\]/).pop())==null?void 0:a.replace(/\.[^/.]+$/,""))||t}})),j=()=>{if(e.value.taskType&&u.weightInfo[e.value.taskType]){const t=u.weightInfo[e.value.taskType],a=t.default,[p,s]=a.split("/");e.value.modelType=p,e.value.modelName=s,t.shp_files.length>0&&(e.value.shpFile=t.shp_files[0]),e.value.areaThreshold=t.default_area||400}else e.value.modelType="",e.value.modelName="",e.value.shpFile="",e.value.areaThreshold=400},M=()=>{e.value.modelName=""},B=()=>{var y,T,v;if(!e.value.taskType)return void k.error("\u8BF7\u9009\u62E9\u4EFB\u52A1\u7C7B\u578B");if(!e.value.modelType)return void k.error("\u8BF7\u9009\u62E9\u6A21\u578B\u7C7B\u578B");if(!e.value.modelName)return void k.error("\u8BF7\u9009\u62E9\u6A21\u578B\u540D\u79F0");if(!e.value.shpFile)return void k.error("\u8BF7\u9009\u62E9\u5F85\u5BF9\u6BD4\u7684\u5BF9\u8C61");const t=D.value.find(w=>w.value===e.value.modelName),a=(t==null?void 0:t.path)||"",p=((y=u.weightInfo[e.value.taskType])==null?void 0:y.display_name)||"\u5206\u6790",s=new Date,m=`${p}_${s.getFullYear().toString()+(s.getMonth()+1).toString().padStart(2,"0")+s.getDate().toString().padStart(2,"0")+s.getHours().toString().padStart(2,"0")+s.getMinutes().toString().padStart(2,"0")}`,n={image:(T=u.task)!=null&&T.id?`D:/Drone_Project/nginxData/ODM/Output/${u.task.id}/${u.task.id}_out.tif`:"",model:a,old_data_path:e.value.shpFile,area_threshold:e.value.areaThreshold,model_type:e.value.modelType,id:((v=u.task)==null?void 0:v.id)||""};F("confirm",{name:m,type:p,config:n,formData:{...e.value}}),V()},V=()=>{e.value={taskType:"",modelType:"",modelName:"",shpFile:"",areaThreshold:400},g.value=!1};return z(()=>u.modelValue,t=>{t&&(e.value={taskType:"",modelType:"",modelName:"",shpFile:"",areaThreshold:400})}),(t,a)=>{const p=i("el-input"),s=i("el-form-item"),m=i("el-option"),n=i("el-select"),y=i("el-input-number"),T=i("el-form"),v=i("el-button"),w=i("el-dialog");return r(),f(w,{modelValue:g.value,"onUpdate:modelValue":a[5]||(a[5]=l=>g.value=l),title:"\u6DFB\u52A0\u5206\u6790\u4EFB\u52A1",width:"600px","close-on-click-modal":!1,onClose:V},{footer:o(()=>[N("div",I,[d(v,{onClick:V},{default:o(()=>a[7]||(a[7]=[S("\u53D6\u6D88")])),_:1}),d(v,{type:"primary",onClick:B},{default:o(()=>a[8]||(a[8]=[S("\u786E\u5B9A")])),_:1})])]),default:o(()=>[d(T,{model:e.value,"label-width":"120px","label-position":"left"},{default:o(()=>[d(s,{label:"\u4EFB\u52A1ID"},{default:o(()=>{var l;return[d(p,{value:((l=t.task)==null?void 0:l.id)||"",readonly:"",disabled:"",placeholder:"\u4EFB\u52A1ID"},null,8,["value"])]}),_:1}),d(s,{label:"\u4EFB\u52A1\u7C7B\u578B",required:""},{default:o(()=>[d(n,{modelValue:e.value.taskType,"onUpdate:modelValue":a[0]||(a[0]=l=>e.value.taskType=l),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u7C7B\u578B",style:{width:"100%"},onChange:j},{default:o(()=>[(r(!0),_(b,null,c(x.value,l=>(r(),f(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(s,{label:"\u6A21\u578B\u7C7B\u578B",required:""},{default:o(()=>[d(n,{modelValue:e.value.modelType,"onUpdate:modelValue":a[1]||(a[1]=l=>e.value.modelType=l),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B\u7C7B\u578B",style:{width:"100%"},disabled:!e.value.taskType,onChange:M},{default:o(()=>[(r(!0),_(b,null,c($.value,l=>(r(),f(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),d(s,{label:"\u6A21\u578B\u540D\u79F0",required:""},{default:o(()=>[d(n,{modelValue:e.value.modelName,"onUpdate:modelValue":a[2]||(a[2]=l=>e.value.modelName=l),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B\u540D\u79F0",style:{width:"100%"},disabled:!e.value.modelType},{default:o(()=>[(r(!0),_(b,null,c(D.value,l=>(r(),f(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),d(s,{label:"\u5F85\u5BF9\u6BD4\u5BF9\u8C61",required:""},{default:o(()=>[d(n,{modelValue:e.value.shpFile,"onUpdate:modelValue":a[3]||(a[3]=l=>e.value.shpFile=l),placeholder:"\u8BF7\u9009\u62E9\u5F85\u5BF9\u6BD4\u7684\u5BF9\u8C61",style:{width:"100%"},disabled:!e.value.taskType},{default:o(()=>[(r(!0),_(b,null,c(O.value,l=>(r(),f(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),d(s,{label:"\u8FC7\u6EE4\u9762\u79EF",required:""},{default:o(()=>[d(y,{modelValue:e.value.areaThreshold,"onUpdate:modelValue":a[4]||(a[4]=l=>e.value.areaThreshold=l),min:1,max:1e4,step:10,style:{width:"100%"},placeholder:"\u8BF7\u8F93\u5165\u8FC7\u6EE4\u9762\u79EF\u9608\u503C"},null,8,["modelValue"]),a[6]||(a[6]=N("div",{class:"form-item-tip"},"\u5355\u4F4D\uFF1A\u5E73\u65B9\u7C73\uFF0C\u7528\u4E8E\u8FC7\u6EE4\u5C0F\u4E8E\u8BE5\u9762\u79EF\u7684\u533A\u57DF",-1))]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-4f9d52c5"]])});export{A as __tla,q as default};
