import{m as I,q as j,__tla as z}from"./index.BSP3cg_z.js";import{g as D,__tla as L}from"./user.BGf96gmW.js";import{d as h,R,k as p,o as U,B as x,e as k,b as q,v as B,f as e,t as G,u as r,G as n}from"./vue.CnN__PXn.js";let g,P=Promise.all([(()=>{try{return z}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let i,c,m,u,d,v,y;i={style:{display:"flex","justify-content":"space-between"}},c={style:{display:"flex"}},m={class:"info"},u={style:{"font-weight":"600",margin:"2px","font-size":"18px"}},d={style:{color:"#6d737b",margin:"2px"}},v={style:{margin:"2px"}},y=h({name:"currentUser"}),g=j(h({...y,setup(T){const{proxy:N}=R(),f=p(new Date),a=p({postName:"",name:"",username:"",userId:"",avatar:"",deptName:""}),_=p(!1);setInterval(()=>{f.value=new Date},1e3),U(()=>{const t=I().userInfos;b(t.user.userId)});const b=async t=>{var o,l;try{_.value=!0;const s=await D(t);a.value=s.data,a.value.postName=((l=(o=s.data)==null?void 0:o.postList)==null?void 0:l.map(w=>w.postName).join(","))||"",a.value.avatar=N.baseURL+s.data.avatar}finally{_.value=!1}};return(t,o)=>{const l=x("el-avatar"),s=x("el-card");return q(),k(s,{style:{height:"100%"}},{default:B(()=>[e("div",i,[e("div",c,[G(l,{style:{width:"60px",height:"60px"},shape:"circle",size:100,fit:"cover",src:r(a).avatar},null,8,["src"]),e("div",m,[e("span",u,n(r(a).name),1),e("span",d,n(r(a).deptName)+" | "+n(r(a).postName),1)])]),e("span",v,n(t.parseTime(r(f))),1)])]),_:1})}}}),[["__scopeId","data-v-b8730a5e"]])});export{P as __tla,g as default};
