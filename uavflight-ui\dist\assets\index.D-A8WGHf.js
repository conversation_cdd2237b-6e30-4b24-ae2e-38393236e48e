import{l as u}from"./pigx-app.DmHLWGl6.js";import{u as v,q as _,__tla as f}from"./index.BSP3cg_z.js";import{d as n,s as C,c as b,a as m,u as s,b as g,f as t,G as h}from"./vue.CnN__PXn.js";let p,k=Promise.all([(()=>{try{return f}catch{}})()]).then(async()=>{let o,e,i;o={class:"title-text"},e=["src"],i=n({name:"layoutLogo"}),p=_(n({...i,setup(x){const d=v(),{themeConfig:a}=C(d),y=b(()=>{let{isCollapse:c,layout:l}=a.value;return!c||l==="classic"||document.body.clientWidth<1e3}),r=()=>{if(a.value.layout==="transverse")return!1;a.value.isCollapse=!a.value.isCollapse};return(c,l)=>s(y)?(g(),m("div",{key:0,class:"layout-logo",onClick:r},[l[0]||(l[0]=t("img",{src:u,alt:"Logo",class:"logo-image"},null,-1)),t("span",o,h(s(a).globalTitle),1)])):(g(),m("div",{key:1,class:"layout-logo-size",onClick:r},[t("img",{src:s(u),class:"layout-logo-size-img"},null,8,e)]))}}),[["__scopeId","data-v-87231cb2"]])});export{k as __tla,p as default};
