import{d as $,z as W,k as g,A as X,o as Y,B as r,m as Z,a as T,b,f as B,q as V,t as l,x as ee,u as e,v as t,I as le,F as ae,p as te,e as z,E as h,G as _,H as oe,J as ne}from"./vue.CnN__PXn.js";import{u as re,__tla as se}from"./table.CCFM44Zd.js";import{a as ue,u as me,b as de,__tla as ie}from"./table.BExdFBu3.js";import{l as ce,__tla as pe}from"./datasource.DryyxFrZ.js";import{v as ye,k as be,c as he,__tla as _e}from"./index.BSP3cg_z.js";let L,fe=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return _e}catch{}})()]).then(async()=>{let v,w,C,N;v={class:"layout-padding"},w={class:"layout-padding-auto layout-padding-view"},C={class:"mb8",style:{width:"100%"}},N=$({name:"systemTable"}),L=$({...N,setup(ge){const E=W(),{t:m}=ye.useI18n(),q=g(),i=g(!0),c=g(),a=X({queryForm:{dsName:"master"},pageList:ue,createdIsNeed:!1}),{getDataList:s,currentChangeHandle:H,sizeChangeHandle:I,downBlobFile:Q,tableStyle:x}=re(a);Y(()=>{ce().then(n=>{c.value=n.data,c.value.length>0&&(a.queryForm.dsName=c.value[0].name),s()})});const F=n=>{de(a.queryForm.dsName,n.name).then(()=>{he().success(m("common.optSuccessText"))})},R=()=>{q.value.resetFields(),s()},U=()=>{Q("/gen/table/export",a.queryForm,"table.xlsx")};return(n,u)=>{const k=r("el-option"),D=r("el-select"),f=r("el-form-item"),K=r("el-input"),p=r("el-button"),A=r("el-form"),S=r("el-row"),G=r("right-toolbar"),d=r("el-table-column"),J=r("el-table"),O=r("pagination"),P=Z("loading");return b(),T("div",v,[B("div",w,[V(l(S,{class:"ml10"},{default:t(()=>[l(A,{inline:!0,model:e(a).queryForm,onKeyup:le(e(s),["enter"]),ref_key:"queryRef",ref:q},{default:t(()=>[l(f,{label:"\u6570\u636E\u6E90",prop:"name"},{default:t(()=>[l(D,{onChange:e(s),placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u6E90",modelValue:e(a).queryForm.dsName,"onUpdate:modelValue":u[0]||(u[0]=o=>e(a).queryForm.dsName=o)},{default:t(()=>[l(k,{label:"\u9ED8\u8BA4\u6570\u636E\u6E90",value:"master"}),(b(!0),T(ae,null,te(e(c),o=>(b(),z(k,{key:o.id,label:o.name,value:o.name},null,8,["label","value"]))),128))]),_:1},8,["onChange","modelValue"])]),_:1}),l(f,{label:n.$t("table.tableName"),prop:"tableName"},{default:t(()=>[l(K,{placeholder:n.$t("table.inputtableNameTip"),modelValue:e(a).queryForm.tableName,"onUpdate:modelValue":u[1]||(u[1]=o=>e(a).queryForm.tableName=o)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),l(f,null,{default:t(()=>[l(p,{onClick:e(s),icon:"search",type:"primary"},{default:t(()=>[h(_(n.$t("common.queryBtn")),1)]),_:1},8,["onClick"]),l(p,{onClick:R,icon:"Refresh"},{default:t(()=>[h(_(n.$t("common.resetBtn")),1)]),_:1})]),_:1})]),_:1},8,["model","onKeyup"])]),_:1},512),[[ee,e(i)]]),l(S,null,{default:t(()=>[B("div",C,[l(G,{export:!0,onExportExcel:U,onQueryTable:e(s),class:"ml10",style:{float:"right","margin-right":"20px"},showSearch:e(i),"onUpdate:showSearch":u[2]||(u[2]=o=>oe(i)?i.value=o:null)},null,8,["onQueryTable","showSearch"])])]),_:1}),V((b(),z(J,{data:e(a).dataList,style:{width:"100%"},border:"","cell-style":e(x).cellStyle,"header-cell-style":e(x).headerCellStyle},{default:t(()=>[l(d,{label:e(m)("table.index"),type:"index",width:"60"},null,8,["label"]),l(d,{label:e(m)("table.tableName"),prop:"name","show-overflow-tooltip":""},null,8,["label"]),l(d,{label:e(m)("table.tableDesc"),prop:"comment","show-overflow-tooltip":""},null,8,["label"]),l(d,{label:e(m)("table.createTime"),prop:"createTime","show-overflow-tooltip":""},null,8,["label"]),l(d,{label:n.$t("common.action"),width:"250"},{default:t(o=>[l(p,{icon:"Refresh",onClick:j=>F(o.row),text:"",type:"primary"},{default:t(()=>[h(_(n.$t("gen.syncBtn")),1)]),_:2},1032,["onClick"]),l(p,{icon:"FolderOpened",onClick:j=>{return y=o.row,void me(a.queryForm.dsName,y.name).then(M=>{be(M.data.fieldList)&&F(y)}).finally(()=>{E.push({path:"/gen/gener/index",query:{tableName:y.name,dsName:a.queryForm.dsName}})});var y},text:"",type:"primary"},{default:t(()=>[h(_(n.$t("gen.genBtn")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[P,e(a).loading]]),l(O,ne({onCurrentChange:e(H),onSizeChange:e(I)},e(a).pagination),null,16,["onCurrentChange","onSizeChange"])])])}}})});export{fe as __tla,L as default};
