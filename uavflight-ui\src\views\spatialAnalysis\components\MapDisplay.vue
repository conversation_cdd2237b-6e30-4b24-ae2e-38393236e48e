<template>
  <div class="map-display">
    <h4>地图展示</h4>
    <div class="map-container">
      <div v-if="mapLoading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载图层...</p>
      </div>
      <div :id="`analysis-map-${Date.now()}`" ref="mapContainer" class="map-content"></div>
      
      <!-- 鼠标位置显示 -->
      <div v-if="mousePositionVisible" class="mouse-position">
        {{ mousePosition }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// OpenLayers imports
import Map from 'ol/Map';
import View from 'ol/View';
import WMTS from 'ol/source/WMTS';
import TileLayer from 'ol/layer/Tile';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import { fromLonLat, toLonLat } from 'ol/proj';

interface Task {
  id: string;
  layer_name: string;
  bbox: any;
}

interface Props {
  task: Task | null;
  visible: boolean;
}

const props = defineProps<Props>();

// 响应式数据
const mapLoading = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);
const mousePosition = ref<string>('');
const mousePositionVisible = ref<boolean>(false);

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.task) {
    console.log('地图组件显示，准备初始化地图，任务信息:', props.task);
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      console.log('开始初始化地图和加载图层');
      initMap();
      loadWMTSLayer();
    }, 300);
  }
});

// 地图相关方法
const initMap = () => {
  console.log('initMap 被调用');
  console.log('map.value:', map.value);
  console.log('mapContainer.value:', mapContainer.value);

  if (map.value) {
    console.log('地图已存在，跳过初始化');
    return;
  }

  if (!mapContainer.value) {
    console.error('地图容器不存在');
    return;
  }

  try {
    mapLoading.value = true;
    console.log('开始初始化地图...');

    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    console.log('初始中心点:', initialCenter);

    console.log('创建地图实例...');
    map.value = new Map({
      target: mapContainer.value,
      layers: [], // 不添加底图，只使用GeoServer图层
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: [] // 移除所有默认控件
    });

    // 添加鼠标位置监听
    map.value.on('pointermove', (event) => {
      const coordinate = map.value!.getCoordinateFromPixel(event.pixel);
      if (coordinate) {
        const lonLat = toLonLat(coordinate);
        mousePosition.value = `经度: ${lonLat[0].toFixed(6)}, 纬度: ${lonLat[1].toFixed(6)}`;
        mousePositionVisible.value = true;
      }
    });

    // 监听地图容器的鼠标离开事件
    if (mapContainer.value) {
      mapContainer.value.addEventListener('mouseleave', () => {
        mousePositionVisible.value = false;
      });
    }

    mapLoading.value = false;
    console.log('地图初始化成功，地图实例:', map.value);
  } catch (error) {
    console.error('地图初始化失败:', error);
    ElMessage.error('地图初始化失败');
    mapLoading.value = false;
  }
};

const loadWMTSLayer = () => {
  if (!map.value || !props.task?.layer_name) return;

  try {
    console.log('开始加载WMTS图层:', props.task.layer_name);

    const geoserverUrl = `http://${import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1'}:${import.meta.env.VITE_GEOSERVER_HD_PORT || '8091'}/geoserver`;
    const projection = getProjection('EPSG:3857')!;
    const projectionExtent = projection.getExtent();
    const size = Math.sqrt(projectionExtent[2] - projectionExtent[0]) / 256;
    const resolutions = new Array(19);
    const matrixIds = new Array(19);
    
    for (let z = 0; z < 19; ++z) {
      resolutions[z] = size / Math.pow(2, z);
      matrixIds[z] = z;
    }

    const tileMatrixSet = 'EPSG:3857';
    const format = 'image/png';

    const source = new WMTS({
      url: `${geoserverUrl}/gwc/service/wmts`,
      layer: props.task.layer_name,
      matrixSet: tileMatrixSet,
      format: format,
      projection: projection,
      tileGrid: new WMTSTileGrid({
        origin: getTopLeft(projectionExtent),
        resolutions: resolutions,
        matrixIds: matrixIds,
      }),
      style: '',
      wrapX: true,
    });

    // 添加事件处理
    source.on('tileloaderror', (event) => {
      console.warn(`WMTS图层 ${props.task?.layer_name} 加载失败:`, event);
    });

    source.on('tileloadend', () => {
      console.log(`WMTS图层 ${props.task?.layer_name} 部分加载成功`);
    });

    // 创建并添加图层
    const layer = new TileLayer({
      opacity: 1,
      source: source,
    });

    map.value.addLayer(layer);
    console.log('WMTS图层添加成功');

    // 如果有bbox信息，缩放到对应区域
    if (props.task.bbox) {
      const { minx, miny, maxx, maxy } = props.task.bbox;
      const extent = [minx, miny, maxx, maxy];
      const transformedExtent = [
        fromLonLat([minx, miny]),
        fromLonLat([maxx, maxy])
      ].flat();
      
      map.value.getView().fit(transformedExtent, {
        padding: [20, 20, 20, 20],
        duration: 1000
      });
      console.log('地图已缩放到图层范围');
    }

  } catch (error) {
    console.error('WMTS图层加载失败:', error);
    ElMessage.error('图层加载失败');
  }
};

// 清理资源
onBeforeUnmount(() => {
  if (map.value) {
    map.value.setTarget(undefined);
    map.value = null;
  }
});
</script>

<style scoped lang="scss">
.map-display {
  h4 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .map-container {
    position: relative;
    height: 450px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1000;

      .loading-icon {
        font-size: 32px;
        color: #409EFF;
        animation: rotate 2s linear infinite;
        margin-bottom: 12px;
      }

      p {
        color: #606266;
        font-size: 14px;
        margin: 0;
      }
    }

    .map-content {
      width: 100%;
      height: 100%;
    }

    .mouse-position {
      position: absolute;
      top: 8px;
      right: 8px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-family: monospace;
      z-index: 1000;
      pointer-events: none;
      white-space: nowrap;
    }
  }
}

// 动画效果
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
