import{a3 as De,a4 as Ue,a5 as $e,a6 as Se,a7 as qe,ab as Ee,a9 as Le,E as Pe,q as je,__tla as Fe}from"./index.BSP3cg_z.js";import Me,{__tla as Ne}from"./FilterDialog.B2Z1Iczi.js";import Re,{__tla as Ae}from"./AnalysisDetailDialog.eXZK61IR.js";import Be,{__tla as Ge}from"./BatchDownloadDialog.BLLY1Stc.js";import He,{__tla as Je}from"./BatchAnalysisDialog.BNS3c0Gs.js";import{d as Oe,k as u,c as S,o as Qe,S as Ke,B as f,m as We,a as q,b as h,f as i,t as r,D as E,E as s,G as c,F as Xe,v,u as y,e as L,q as Ye,y as Ze}from"./vue.CnN__PXn.js";import{__tla as ea}from"./TaskConfigDialog.DliaCU3T.js";import{__tla as aa}from"./LogViewDialog.Bv4eI9Ym.js";let ve,la=Promise.all([(()=>{try{return Fe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})()]).then(async()=>{let H,J,O,Q,K,W,X,Y,Z,ee,ae,le,te,ue;H={class:"spatial-analysis-container"},J={class:"header"},O={class:"actions"},Q={key:0,class:"refresh-info"},K={class:"page-header"},W={class:"filter-bar"},X={class:"search-area"},Y={key:0,class:"right-section"},Z={class:"task-count"},ee={class:"task-list-section"},ae={class:"bbox-info"},le={class:"bbox-row"},te={class:"bbox-value"},ue={class:"pagination-section"},ve=je(Oe({__name:"index",setup(ta){const b=u(!1),de=u(null),P=u([]),p=u(""),d=u(null),m=u("endTime"),x=u("desc"),V=u(0),_=u(1),w=u(20),j=u(!1),F=u(!1),M=u(!1),N=u(!1),ne=u(null),g=u(!0),R=u(null),k=u(60),C=u(null),T=u(null),ce=S(()=>P.value.length),re=S(()=>p.value.trim()!==""||d.value&&d.value[0]&&d.value[1]),A=S(()=>{V.value;let a=P.value.filter(e=>{var t,n;return e.status==="\u5B8C\u6210"&&((n=(t=e.details)==null?void 0:t.map_config)==null?void 0:n.bbox)}).map(e=>({id:e.details.id,endTime:e.details.endTime,bbox:e.details.map_config.bbox,analysisCount:0,layer_name:e.details.map_config.layer_id,theme:e.details.map_config.theme}));if(p.value.trim()){const e=p.value.trim().toLowerCase();a=a.filter(t=>t.id.toLowerCase().includes(e))}if(d.value&&d.value[0]&&d.value[1]){const[e,t]=d.value;a=a.filter(n=>{const o=n.endTime.split(" ")[0];return o>=e&&o<=t})}return a=[...a].sort((e,t)=>{let n,o;return m.value==="endTime"?(n=new Date(e.endTime).getTime(),o=new Date(t.endTime).getTime()):m.value==="id"?(n=e.id,o=t.id):m.value==="layer_name"?(n=e.layer_name||"",o=t.layer_name||""):m.value==="theme"?(n=e.theme||"",o=t.theme||""):(n=e[m.value],o=t[m.value]),n==null&&(n=""),o==null&&(o=""),x.value==="asc"?n>o?1:-1:n<o?1:-1}),a}),me=S(()=>{V.value;const a=(_.value-1)*w.value,e=a+w.value;return A.value.slice(a,e)}),z=async()=>{b.value=!0;try{const a="http://192.168.43.148:8091/api/map/odm/tasks",e=await fetch(a);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();if(t.status!=="success")throw new Error("API\u8FD4\u56DE\u72B6\u6001\u4E0D\u6B63\u786E");P.value=t.tasks,t.tasks.filter(n=>n.status==="\u5B8C\u6210").length,R.value=new Date,g.value&&I()}catch{Pe.error("\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5")}finally{b.value=!1}},pe=()=>{z(),g.value&&I()},_e=a=>{a?(ie(),I()):(B(),G())},ie=()=>{B(),C.value=window.setInterval(()=>{z()},6e4)},B=()=>{C.value!==null&&(clearInterval(C.value),C.value=null)},I=()=>{G(),k.value=60,T.value=window.setInterval(()=>{k.value>0?k.value--:z()},1e3)},G=()=>{T.value!==null&&(clearInterval(T.value),T.value=null)},fe=()=>{_.value=1},he=()=>{p.value="",d.value=null,_.value=1},ye=()=>{F.value=!0},ge=a=>{m.value,x.value,p.value=a.searchQuery,d.value=a.dateRange,m.value=a.sortField,x.value=a.sortOrder,_.value=1,V.value++,Ze(()=>{})},be=()=>{N.value=!0},we=()=>{M.value=!0},ke=a=>{w.value=a,_.value=1},xe=a=>{_.value=a},Ve=a=>{if(!a)return"";const e=new Date().getTime()-a.getTime();return e<6e4?`${Math.floor(e/1e3)}\u79D2\u524D`:e<36e5?`${Math.floor(e/6e4)}\u5206\u949F\u524D`:a.toLocaleTimeString("zh-CN")},D=a=>a.toFixed(6);return Qe(()=>{z(),g.value&&(ie(),I())}),Ke(()=>{B(),G()}),(a,e)=>{const t=f("el-button"),n=f("el-switch"),o=f("el-input"),oe=f("el-tag"),U=f("el-table-column"),Ce=f("el-icon"),Te=f("el-table"),ze=f("el-pagination"),Ie=We("loading");return h(),q("div",H,[i("div",J,[e[9]||(e[9]=i("h2",null,"\u7A7A\u95F4\u5206\u6790\u4EFB\u52A1\u7BA1\u7406",-1)),i("div",O,[R.value?(h(),q("span",Q,[s(" \u4E0A\u6B21\u5237\u65B0: "+c(Ve(R.value))+" ",1),g.value&&k.value>0?(h(),q(Xe,{key:0},[s(" | \u4E0B\u6B21\u5237\u65B0: "+c(k.value)+"\u79D2 ",1)],64)):E("",!0)])):E("",!0),r(t,{type:"primary",icon:y(De),onClick:pe,loading:b.value},{default:v(()=>e[8]||(e[8]=[s("\u5237\u65B0\u6570\u636E")])),_:1},8,["icon","loading"]),r(n,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=l=>g.value=l),"active-text":"\u81EA\u52A8\u5237\u65B0","inactive-text":"",onChange:_e},null,8,["modelValue"])])]),i("div",K,[i("div",W,[i("div",X,[r(o,{modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=l=>p.value=l),placeholder:"\u8F93\u5165\u4EFB\u52A1ID\u641C\u7D22",clearable:"","prefix-icon":"Search",style:{width:"300px"},onInput:fe},null,8,["modelValue"]),re.value?(h(),L(t,{key:1,type:"warning",icon:y($e),onClick:he},{default:v(()=>e[11]||(e[11]=[s("\u53D6\u6D88\u7B5B\u9009")])),_:1},8,["icon"])):(h(),L(t,{key:0,type:"primary",icon:y(Ue),onClick:ye},{default:v(()=>e[10]||(e[10]=[s("\u9AD8\u7EA7\u7B5B\u9009")])),_:1},8,["icon"])),r(t,{type:"success",icon:y(Se),onClick:be},{default:v(()=>e[12]||(e[12]=[s("\u6279\u91CF\u5206\u6790\u4E0E\u5220\u9664")])),_:1},8,["icon"])])]),b.value||de.value?E("",!0):(h(),q("div",Y,[r(t,{type:"warning",icon:y(qe),onClick:we},{default:v(()=>e[13]||(e[13]=[s("\u6279\u91CF\u4E0B\u8F7D")])),_:1},8,["icon"]),i("div",Z,[e[15]||(e[15]=s(" \u5171 ")),i("strong",null,c(ce.value),1),e[16]||(e[16]=s(" \u4E2A\u4EFB\u52A1\uFF0C\u5F53\u524D\u663E\u793A ")),i("strong",null,c(A.value.length),1),e[17]||(e[17]=s(" \u4E2A ")),re.value?(h(),L(oe,{key:0,size:"small",type:"info"},{default:v(()=>e[14]||(e[14]=[s("\u5DF2\u5E94\u7528\u7B5B\u9009")])),_:1})):E("",!0)])]))]),i("div",ee,[Ye((h(),L(Te,{data:me.value,key:`table-${V.value}`,stripe:"",style:{width:"100%"}},{default:v(()=>[r(U,{prop:"id",label:"\u4EFB\u52A1ID",width:"180"},{default:v(({row:l})=>[r(oe,{type:"primary"},{default:v(()=>[s(c(l.id),1)]),_:2},1024)]),_:1}),r(U,{prop:"endTime",label:"\u53D1\u5E03\u65F6\u95F4",width:"200"},{default:v(({row:l})=>{return[r(Ce,null,{default:v(()=>[r(y(Ee))]),_:1}),s(" "+c(($=l.endTime,$?new Date($).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-")),1)];var $}),_:1}),r(U,{label:"\u7A7A\u95F4\u8303\u56F4","min-width":"300"},{default:v(({row:l})=>[i("div",ae,[i("div",le,[e[18]||(e[18]=i("span",{class:"bbox-label"},"\u7ECF\u7EAC\u5EA6\uFF1A",-1)),i("span",te,c(D(l.bbox.minx))+" ~ "+c(D(l.bbox.maxx))+", "+c(D(l.bbox.miny))+" ~ "+c(D(l.bbox.maxy)),1)])])]),_:1}),r(U,{label:"\u64CD\u4F5C",width:"150",align:"center"},{default:v(({row:l})=>[r(t,{type:"primary",size:"small",onClick:$=>{return se=l,ne.value=se,void(j.value=!0);var se},icon:y(Le)},{default:v(()=>e[19]||(e[19]=[s(" \u64CD\u4F5C ")])),_:2},1032,["onClick","icon"])]),_:1})]),_:1},8,["data"])),[[Ie,b.value]]),i("div",ue,[r(ze,{"current-page":_.value,"onUpdate:currentPage":e[2]||(e[2]=l=>_.value=l),"page-size":w.value,"onUpdate:pageSize":e[3]||(e[3]=l=>w.value=l),"page-sizes":[10,20,50,100],total:A.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ke,onCurrentChange:xe},null,8,["current-page","page-size","total"])])]),r(Me,{modelValue:F.value,"onUpdate:modelValue":e[4]||(e[4]=l=>F.value=l),"search-query":p.value,"date-range":d.value,"sort-field":m.value,"sort-order":x.value,onConfirm:ge},null,8,["modelValue","search-query","date-range","sort-field","sort-order"]),r(Re,{modelValue:j.value,"onUpdate:modelValue":e[5]||(e[5]=l=>j.value=l),task:ne.value},null,8,["modelValue","task"]),r(Be,{modelValue:M.value,"onUpdate:modelValue":e[6]||(e[6]=l=>M.value=l)},null,8,["modelValue"]),r(He,{modelValue:N.value,"onUpdate:modelValue":e[7]||(e[7]=l=>N.value=l)},null,8,["modelValue"])])}}}),[["__scopeId","data-v-945a33f1"]])});export{la as __tla,ve as default};
