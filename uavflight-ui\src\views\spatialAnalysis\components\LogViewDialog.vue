<template>
  <el-dialog
    v-model="visible"
    :title="'任务日志: ' + taskId"
    width="80%"
    destroy-on-close
  >
    <div class="log-dialog">
      <div class="content-box">
        <div class="content-header">
          <span>任务日志:</span>
          <el-button type="primary" size="small" :loading="loading" @click="refreshLog">
            刷新
          </el-button>
        </div>
        <div class="content-body">
          <div v-if="loading" class="loading-content">
            <el-skeleton :rows="10" animated />
          </div>
          <div v-else-if="!logContent" class="empty-content">
            暂无任务日志信息，请点击刷新按钮获取
          </div>
          <pre v-else class="log-content">{{ logContent }}</pre>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';

interface Props {
  modelValue: boolean;
  taskId: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const loading = ref(false);
const logContent = ref('');

// 获取任务日志
const fetchTaskLog = async (taskId: string) => {
  try {
    loading.value = true;
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/analysis/logs/?task_id=${taskId}`;

    console.log('获取任务日志，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('日志API返回数据:', data);

    if (data.status === 'success' && data.data) {
      // 处理日志数据，logs是一个数组，需要合并成字符串
      if (data.data.logs && Array.isArray(data.data.logs)) {
        // 将日志数组合并成字符串，去掉每行末尾的\n（因为pre标签会自动换行）
        logContent.value = data.data.logs.map((log: string) => log.replace(/\n$/, '')).join('\n');
        console.log('日志内容处理完成，总行数:', data.data.logs.length);
      } else {
        logContent.value = '暂无日志信息';
      }
    } else {
      throw new Error(data.message || '获取日志失败');
    }
  } catch (error) {
    console.error('获取任务日志失败:', error);
    // 由于API不存在，使用模拟日志数据
    logContent.value = generateMockLogContent(taskId);
    console.log('使用模拟日志数据');
  } finally {
    loading.value = false;
  }
};

// 生成模拟日志内容
const generateMockLogContent = (taskId: string): string => {
  const now = new Date();
  const timestamp = now.toISOString().replace('T', ' ').substring(0, 19);
  
  return `[${timestamp}] 已创建分析任务: ${taskId}
[${timestamp}] 正在排队中，等待执行。`;
};

// 刷新日志
const refreshLog = () => {
  if (props.taskId) {
    fetchTaskLog(props.taskId);
  }
};

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.taskId) {
    fetchTaskLog(props.taskId);
  }
});
</script>

<style scoped lang="scss">
.log-dialog {
  .content-box {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    .content-header {
      padding: 10px 15px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
    
    .content-body {
      padding: 15px;
      min-height: 300px;
      max-height: 500px;
      overflow-y: auto;
      
      .loading-content, .empty-content {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
      }
      
      .log-content {
        margin: 0;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
        background-color: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
      }
    }
  }
}
</style>
