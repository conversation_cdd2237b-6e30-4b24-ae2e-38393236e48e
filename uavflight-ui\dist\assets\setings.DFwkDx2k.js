import{E as M,v as cl,u as ml,L as f,e as $,o as bl,m as vl,q as yl,__tla as gl}from"./index.BSP3cg_z.js";import{d as qa,s as fl,A as pl,c as xl,o as hl,y as Cl,i as Vl,B as x,a as wl,b as Bl,t as u,v,f as a,E as V,G as o,n as h,g as w}from"./vue.CnN__PXn.js";import{c as $l,__tla as Il}from"./commonFunction.BmnVIZty.js";let Wa,Tl=Promise.all([(()=>{try{return gl}catch{}})(),(()=>{try{return Il}catch{}})()]).then(async()=>{function I(){return{hexToRgb:c=>{let m="";if(!/^\#?[0-9A-Fa-f]{6}$/.test(c))return M.warning("\u8F93\u5165\u9519\u8BEF\u7684hex"),"";m=(c=c.replace("#","")).match(/../g);for(let r=0;r<3;r++)m[r]=parseInt(m[r],16);return m},rgbToHex:(c,m,r)=>{let n=/^\d{1,3}$/;if(!n.test(c)||!n.test(m)||!n.test(r))return M.warning("\u8F93\u5165\u9519\u8BEF\u7684rgb\u989C\u8272\u503C"),"";let b=[c.toString(16),m.toString(16),r.toString(16)];for(let B=0;B<3;B++)b[B].length==1&&(b[B]=`0${b[B]}`);return`#${b.join("")}`},getDarkColor:(c,m)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(c))return M.warning("\u8F93\u5165\u9519\u8BEF\u7684hex\u989C\u8272\u503C"),"";let r=I().hexToRgb(c);for(let n=0;n<3;n++)r[n]=Math.floor(r[n]*(1-m));return I().rgbToHex(r[0],r[1],r[2])},getLightColor:(c,m)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(c))return M.warning("\u8F93\u5165\u9519\u8BEF\u7684hex\u989C\u8272\u503C"),"";let r=I().hexToRgb(c);for(let n=0;n<3;n++)r[n]=Math.floor((255-r[n])*m+r[n]);return I().rgbToHex(r[0],r[1],r[2])}}}let _,H,P,R,q,W,O,j,J,N,K,Q,X,Y,Z,ee,ae,le,te,se,ue,oe,re,ie,ne,de,ce,me,be,ve,ye,ge,fe,pe,xe,he,Ce,Ve,we,Be,$e,Ie,Te,Me,Se,Ue,_e,ze,ke,Ae,De,Ee,Ge,Le,Fe,He,Pe,Re,qe,We,Oe,je,Je,Ne,Ke,Qe,Xe,Ye,Ze,ea,aa,la,ta,sa,ua,oa,ra,ia,na,da,ca,ma,ba,va,ya,ga,fa,pa,xa,ha,Ca,Va,wa,Ba,$a,Ia,Ta,Ma,Sa,Ua,_a,za,ka,Aa,Da,Ea,Ga,La;_=c=>{const m="1.23452384164.123412416";document.getElementById(m)!==null&&document.body.removeChild(document.getElementById(m));const r=document.createElement("canvas");r.width=200,r.height=130;const n=r.getContext("2d");n.rotate(-20*Math.PI/180),n.font="12px Vedana",n.fillStyle="rgba(200, 200, 200, 0.30)",n.textBaseline="middle",n.fillText(c,r.width/10,r.height/2);const b=document.createElement("div");return b.id=m,b.style.pointerEvents="none",b.style.top="0px",b.style.left="0px",b.style.position="fixed",b.style.zIndex="10000000",b.style.width=`${document.documentElement.clientWidth}px`,b.style.height=`${document.documentElement.clientHeight}px`,b.style.background=`url(${r.toDataURL("image/png")}) left top repeat`,document.body.appendChild(b),m},H=c=>{let m=_(c);document.getElementById(m)===null&&(m=_(c))},P=()=>{let c="1.23452384164.123412416";document.getElementById(c)!==null&&document.body.removeChild(document.getElementById(c))},R={class:"layout-breadcrumb-seting"},q={class:"layout-breadcrumb-seting-bar-flex"},W={class:"layout-breadcrumb-seting-bar-flex-value"},O={class:"layout-breadcrumb-seting-bar-flex mt15"},j={class:"layout-breadcrumb-seting-bar-flex-label"},J={class:"layout-breadcrumb-seting-bar-flex-value"},N={class:"layout-breadcrumb-seting-bar-flex mt15"},K={class:"layout-breadcrumb-seting-bar-flex-label"},Q={class:"layout-breadcrumb-seting-bar-flex-value"},X={class:"layout-breadcrumb-seting-bar-flex"},Y={class:"layout-breadcrumb-seting-bar-flex-label"},Z={class:"layout-breadcrumb-seting-bar-flex-value"},ee={class:"layout-breadcrumb-seting-bar-flex"},ae={class:"layout-breadcrumb-seting-bar-flex-label"},le={class:"layout-breadcrumb-seting-bar-flex-value"},te={class:"layout-breadcrumb-seting-bar-flex mt10"},se={class:"layout-breadcrumb-seting-bar-flex-label"},ue={class:"layout-breadcrumb-seting-bar-flex-value"},oe={class:"layout-breadcrumb-seting-bar-flex"},re={class:"layout-breadcrumb-seting-bar-flex-label"},ie={class:"layout-breadcrumb-seting-bar-flex-value"},ne={class:"layout-breadcrumb-seting-bar-flex"},de={class:"layout-breadcrumb-seting-bar-flex-label"},ce={class:"layout-breadcrumb-seting-bar-flex-value"},me={class:"layout-breadcrumb-seting-bar-flex"},be={class:"layout-breadcrumb-seting-bar-flex-label"},ve={class:"layout-breadcrumb-seting-bar-flex-value"},ye={class:"layout-breadcrumb-seting-bar-flex mt14"},ge={class:"layout-breadcrumb-seting-bar-flex-label"},fe={class:"layout-breadcrumb-seting-bar-flex-value"},pe={class:"layout-breadcrumb-seting-bar-flex-label"},xe={class:"layout-breadcrumb-seting-bar-flex-value"},he={class:"layout-breadcrumb-seting-bar-flex-label"},Ce={class:"layout-breadcrumb-seting-bar-flex-value"},Ve={class:"layout-breadcrumb-seting-bar-flex-label"},we={class:"layout-breadcrumb-seting-bar-flex-value"},Be={class:"layout-breadcrumb-seting-bar-flex-label"},$e={class:"layout-breadcrumb-seting-bar-flex-value"},Ie={class:"layout-breadcrumb-seting-bar-flex-label"},Te={class:"layout-breadcrumb-seting-bar-flex-value"},Me={class:"layout-breadcrumb-seting-bar-flex-label"},Se={class:"layout-breadcrumb-seting-bar-flex-value"},Ue={class:"layout-breadcrumb-seting-bar-flex mt15"},_e={class:"layout-breadcrumb-seting-bar-flex-label"},ze={class:"layout-breadcrumb-seting-bar-flex-value"},ke={class:"layout-breadcrumb-seting-bar-flex-label"},Ae={class:"layout-breadcrumb-seting-bar-flex-value"},De={class:"layout-breadcrumb-seting-bar-flex mt15"},Ee={class:"layout-breadcrumb-seting-bar-flex-label"},Ge={class:"layout-breadcrumb-seting-bar-flex-value"},Le={class:"layout-breadcrumb-seting-bar-flex mt15"},Fe={class:"layout-breadcrumb-seting-bar-flex-label"},He={class:"layout-breadcrumb-seting-bar-flex-value"},Pe={class:"layout-breadcrumb-seting-bar-flex-label"},Re={class:"layout-breadcrumb-seting-bar-flex-value"},qe={class:"layout-breadcrumb-seting-bar-flex mt15"},We={class:"layout-breadcrumb-seting-bar-flex-label"},Oe={class:"layout-breadcrumb-seting-bar-flex-value"},je={class:"layout-breadcrumb-seting-bar-flex mt15"},Je={class:"layout-breadcrumb-seting-bar-flex-label"},Ne={class:"layout-breadcrumb-seting-bar-flex-value"},Ke={class:"layout-breadcrumb-seting-bar-flex mt15"},Qe={class:"layout-breadcrumb-seting-bar-flex-label"},Xe={class:"layout-breadcrumb-seting-bar-flex-value"},Ye={class:"layout-breadcrumb-seting-bar-flex mt15"},Ze={class:"layout-breadcrumb-seting-bar-flex-label"},ea={class:"layout-breadcrumb-seting-bar-flex-value"},aa={class:"layout-breadcrumb-seting-bar-flex-label"},la={class:"layout-breadcrumb-seting-bar-flex-value"},ta={class:"layout-breadcrumb-seting-bar-flex mt15"},sa={class:"layout-breadcrumb-seting-bar-flex-label"},ua={class:"layout-breadcrumb-seting-bar-flex-value"},oa={class:"layout-breadcrumb-seting-bar-flex mt15"},ra={class:"layout-breadcrumb-seting-bar-flex-label"},ia={class:"layout-breadcrumb-seting-bar-flex-value"},na={class:"layout-breadcrumb-seting-bar-flex mt15"},da={class:"layout-breadcrumb-seting-bar-flex-label"},ca={class:"layout-breadcrumb-seting-bar-flex-value"},ma={class:"layout-breadcrumb-seting-bar-flex mt15"},ba={class:"layout-breadcrumb-seting-bar-flex-label"},va={class:"layout-breadcrumb-seting-bar-flex-value"},ya={class:"layout-breadcrumb-seting-bar-flex mt15"},ga={class:"layout-breadcrumb-seting-bar-flex-label"},fa={class:"layout-breadcrumb-seting-bar-flex-value"},pa={class:"layout-breadcrumb-seting-bar-flex mt15"},xa={class:"layout-breadcrumb-seting-bar-flex-label"},ha={class:"layout-breadcrumb-seting-bar-flex-value"},Ca={class:"layout-breadcrumb-seting-bar-flex mt15"},Va={class:"layout-breadcrumb-seting-bar-flex-label"},wa={class:"layout-breadcrumb-seting-bar-flex-value"},Ba={class:"layout-breadcrumb-seting-bar-flex-label"},$a={class:"layout-breadcrumb-seting-bar-flex-value"},Ia={class:"layout-breadcrumb-seting-bar-flex-label"},Ta={class:"layout-breadcrumb-seting-bar-flex-value"},Ma={class:"layout-drawer-content-flex"},Sa={class:"layout-tips-box"},Ua={class:"layout-tips-txt"},_a={class:"layout-tips-box"},za={class:"layout-tips-txt"},ka={class:"layout-tips-box"},Aa={class:"layout-tips-txt"},Da={class:"layout-tips-box"},Ea={class:"layout-tips-txt"},Ga={class:"copy-config"},La=qa({name:"layoutBreadcrumbSeting"}),Wa=yl(qa({...La,setup(c,{expose:m}){const{locale:r}=cl.useI18n(),n=ml(),{themeConfig:b}=fl(n),{copyText:B}=$l(),{getLightColor:z,getDarkColor:Oa}=I(),k=pl({isMobile:!1}),e=xl(()=>b.value),Fa=()=>{if(!e.value.primary)return M.warning("\u5168\u5C40\u4E3B\u9898 primary \u989C\u8272\u503C\u4E0D\u80FD\u4E3A\u7A7A");document.documentElement.style.setProperty("--el-color-primary-dark-2",`${Oa(e.value.primary,.1)}`),document.documentElement.style.setProperty("--el-color-primary",e.value.primary);for(let t=1;t<=9;t++)document.documentElement.style.setProperty(`--el-color-primary-light-${t}`,`${z(e.value.primary,t/10)}`);F()},y=t=>{document.documentElement.style.setProperty(`--next-bg-${t}`,b.value[t]),t==="menuBar"&&document.documentElement.style.setProperty("--next-bg-menuBar-light-1",z(e.value.menuBar,.05)),A(),D(),E(),F()},A=()=>{G(".layout-navbars-breadcrumb-index",e.value.isTopBarColorGradual,e.value.topBar)},D=()=>{G(".layout-container .el-aside",e.value.isMenuBarColorGradual,e.value.menuBar)},E=()=>{G(".layout-container .layout-columns-aside",e.value.isColumnsMenuBarColorGradual,e.value.columnsMenuBar)},G=(t,l,C)=>{setTimeout(()=>{let p=document.querySelector(t);if(!p)return!1;document.documentElement.style.setProperty("--el-menu-bg-color",document.documentElement.style.getPropertyValue("--next-bg-menuBar")),l?p.setAttribute("style",`background:linear-gradient(to bottom left , ${C}, ${z(C,.6)}) !important;`):p.setAttribute("style",""),i()},200)},ja=()=>{i()},Ja=()=>{F()},Na=()=>{e.value.isFixedHeaderChange=!e.value.isFixedHeader,i()},Ka=()=>{e.value.isBreadcrumb=!1,i(),$.emit("getBreadcrumbIndexSetFilterRoutes")},Qa=()=>{e.value.isShowLogoChange=!e.value.isShowLogo,i()},Xa=()=>{e.value.layout==="classic"&&(e.value.isClassicSplitMenu=!1),i()},Ya=()=>{$.emit("openOrCloseSortable"),i()},Za=()=>{$.emit("openShareTagsView"),i()},S=t=>{t==="grayscale"?e.value.isGrayscale&&(e.value.isInvert=!1):e.value.isInvert&&(e.value.isGrayscale=!1);const l=t==="grayscale"?`grayscale(${e.value.isGrayscale?1:0})`:`invert(${e.value.isInvert?"80%":"0%"})`;document.body.setAttribute("style",`filter: ${l}`),i()},Ha=()=>{const t=document.documentElement;e.value.isIsDark?(t.setAttribute("data-theme","dark"),t.classList.add("dark")):(t.classList.remove("dark"),t.setAttribute("data-theme",""))},el=()=>{var l;const t=((l=vl().userInfos.user)==null?void 0:l.username)||e.value.globalTitle;e.value.isWartermark?H(t):P(),i()},U=t=>{if(f.set("oldLayout",t),e.value.layout===t)return!1;t==="transverse"&&(e.value.isCollapse=!1),e.value.layout=t,e.value.isDrawer=!1,L()},L=()=>{y("menuBar"),y("menuBarColor"),y("menuBarActiveColor"),y("topBar"),y("topBarColor"),y("columnsMenuBar"),y("columnsMenuBarColor")},al=()=>{e.value.isFixedHeaderChange=!1,e.value.isShowLogoChange=!1,e.value.isDrawer=!1,i()},F=()=>{i(),ll()},i=()=>{f.remove("themeConfig"),f.set("themeConfig",e.value)},ll=()=>{f.set("themeConfigStyle",document.documentElement.style.cssText)},tl=()=>{let t=f.get("themeConfig");t.isDrawer=!1,B(JSON.stringify(t)).then(()=>{e.value.isDrawer=!1})},sl=()=>{f.clear(),window.location.reload(),f.set("version","3.8.2")},ul=t=>{f.remove("themeConfig"),b.value.globalComponentSize=t,f.set("themeConfig",b.value),e.value.isDrawer=!1,window.location.reload()};return hl(()=>{Cl(()=>{f.get("frequency")||L(),f.set("frequency",1),$.on("layoutMobileResize",t=>{e.value.layout=t.layout,e.value.isDrawer=!1,L(),k.isMobile=bl.isMobile()}),setTimeout(()=>{Fa(),e.value.isGrayscale&&S("grayscale"),e.value.isInvert&&S("invert"),e.value.isIsDark&&Ha(),f.get("themeConfig")&&(r.value=f.get("themeConfig").globalI18n),A(),D(),E()},100)})}),Vl(()=>{$.off("layoutMobileResize",()=>{}),$.off("updateWartermark",()=>{})}),m({openDrawer:()=>{e.value.isDrawer=!0}}),(t,l)=>{const C=x("el-divider"),p=x("el-color-picker"),d=x("el-switch"),g=x("el-option"),T=x("el-select"),ol=x("el-alert"),rl=x("ele-CopyDocument"),Pa=x("el-icon"),Ra=x("el-button"),il=x("ele-RefreshRight"),nl=x("el-scrollbar"),dl=x("el-drawer");return Bl(),wl("div",R,[u(dl,{title:t.$t("layout.configTitle"),modelValue:e.value.isDrawer,"onUpdate:modelValue":l[48]||(l[48]=s=>e.value.isDrawer=s),direction:"rtl","destroy-on-close":"",size:"260px",onClose:al},{default:v(()=>[u(nl,{class:"layout-breadcrumb-seting-bar"},{default:v(()=>[u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.oneTitle")),1)]),_:1}),a("div",q,[l[49]||(l[49]=a("div",{class:"layout-breadcrumb-seting-bar-flex-label"},"primary",-1)),a("div",W,[u(p,{modelValue:e.value.primary,"onUpdate:modelValue":l[0]||(l[0]=s=>e.value.primary=s),onChange:Fa},null,8,["modelValue"])])]),a("div",O,[a("div",j,o(t.$t("layout.fourIsDark")),1),a("div",J,[u(d,{modelValue:e.value.isIsDark,"onUpdate:modelValue":l[1]||(l[1]=s=>e.value.isIsDark=s),size:"small",onChange:Ha},null,8,["modelValue"])])]),a("div",N,[a("div",K,o(t.$t("user.title0")),1),a("div",Q,[u(T,{modelValue:e.value.globalComponentSize,"onUpdate:modelValue":l[2]||(l[2]=s=>e.value.globalComponentSize=s),placeholder:"\u8BF7\u9009\u62E9",style:{width:"90px"},onChange:ul},{default:v(()=>[u(g,{label:t.$t("user.dropdownLarge"),value:"large"},null,8,["label"]),u(g,{label:t.$t("user.dropdownDefault"),value:"default"},null,8,["label"]),u(g,{label:t.$t("user.dropdownSmall"),value:"small"},null,8,["label"])]),_:1},8,["modelValue"])])]),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.twoTopTitle")),1)]),_:1}),a("div",X,[a("div",Y,o(t.$t("layout.twoTopBar")),1),a("div",Z,[u(p,{modelValue:e.value.topBar,"onUpdate:modelValue":l[3]||(l[3]=s=>e.value.topBar=s),onChange:l[4]||(l[4]=s=>y("topBar"))},null,8,["modelValue"])])]),a("div",ee,[a("div",ae,o(t.$t("layout.twoTopBarColor")),1),a("div",le,[u(p,{modelValue:e.value.topBarColor,"onUpdate:modelValue":l[5]||(l[5]=s=>e.value.topBarColor=s),onChange:l[6]||(l[6]=s=>y("topBarColor"))},null,8,["modelValue"])])]),a("div",te,[a("div",se,o(t.$t("layout.twoIsTopBarColorGradual")),1),a("div",ue,[u(d,{modelValue:e.value.isTopBarColorGradual,"onUpdate:modelValue":l[7]||(l[7]=s=>e.value.isTopBarColorGradual=s),size:"small",onChange:A},null,8,["modelValue"])])]),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.twoMenuTitle")),1)]),_:1}),a("div",oe,[a("div",re,o(t.$t("layout.twoMenuBar")),1),a("div",ie,[u(p,{modelValue:e.value.menuBar,"onUpdate:modelValue":l[8]||(l[8]=s=>e.value.menuBar=s),onChange:l[9]||(l[9]=s=>y("menuBar"))},null,8,["modelValue"])])]),a("div",ne,[a("div",de,o(t.$t("layout.twoMenuBarColor")),1),a("div",ce,[u(p,{modelValue:e.value.menuBarColor,"onUpdate:modelValue":l[10]||(l[10]=s=>e.value.menuBarColor=s),onChange:l[11]||(l[11]=s=>y("menuBarColor"))},null,8,["modelValue"])])]),a("div",me,[a("div",be,o(t.$t("layout.twoMenuBarActiveColor")),1),a("div",ve,[u(p,{modelValue:e.value.menuBarActiveColor,"onUpdate:modelValue":l[12]||(l[12]=s=>e.value.menuBarActiveColor=s),"show-alpha":"",onChange:l[13]||(l[13]=s=>y("menuBarActiveColor"))},null,8,["modelValue"])])]),a("div",ye,[a("div",ge,o(t.$t("layout.twoIsMenuBarColorGradual")),1),a("div",fe,[u(d,{modelValue:e.value.isMenuBarColorGradual,"onUpdate:modelValue":l[14]||(l[14]=s=>e.value.isMenuBarColorGradual=s),size:"small",onChange:D},null,8,["modelValue"])])]),u(C,{"content-position":"left",style:h({opacity:e.value.layout!=="columns"?.5:1})},{default:v(()=>[V(o(t.$t("layout.twoColumnsTitle")),1)]),_:1},8,["style"]),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",pe,o(t.$t("layout.twoColumnsMenuBar")),1),a("div",xe,[u(p,{modelValue:e.value.columnsMenuBar,"onUpdate:modelValue":l[15]||(l[15]=s=>e.value.columnsMenuBar=s),onChange:l[16]||(l[16]=s=>y("columnsMenuBar")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",he,o(t.$t("layout.twoColumnsMenuBarColor")),1),a("div",Ce,[u(p,{modelValue:e.value.columnsMenuBarColor,"onUpdate:modelValue":l[17]||(l[17]=s=>e.value.columnsMenuBarColor=s),onChange:l[18]||(l[18]=s=>y("columnsMenuBarColor")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Ve,o(t.$t("layout.twoIsColumnsMenuBarColorGradual")),1),a("div",we,[u(d,{modelValue:e.value.isColumnsMenuBarColorGradual,"onUpdate:modelValue":l[19]||(l[19]=s=>e.value.isColumnsMenuBarColorGradual=s),size:"small",onChange:E,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Be,o(t.$t("layout.twoIsColumnsMenuHoverPreload")),1),a("div",$e,[u(d,{modelValue:e.value.isColumnsMenuHoverPreload,"onUpdate:modelValue":l[20]||(l[20]=s=>e.value.isColumnsMenuHoverPreload=s),size:"small",onChange:ja,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.threeTitle")),1)]),_:1}),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:h({opacity:e.value.layout==="transverse"?.5:1})},[a("div",Ie,o(t.$t("layout.threeIsCollapse")),1),a("div",Te,[u(d,{modelValue:e.value.isCollapse,"onUpdate:modelValue":l[21]||(l[21]=s=>e.value.isCollapse=s),disabled:e.value.layout==="transverse",size:"small",onChange:Ja},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:h({opacity:e.value.layout==="transverse"?.5:1})},[a("div",Me,o(t.$t("layout.threeIsUniqueOpened")),1),a("div",Se,[u(d,{modelValue:e.value.isUniqueOpened,"onUpdate:modelValue":l[22]||(l[22]=s=>e.value.isUniqueOpened=s),disabled:e.value.layout==="transverse",size:"small",onChange:i},null,8,["modelValue","disabled"])])],4),a("div",Ue,[a("div",_e,o(t.$t("layout.threeIsFixedHeader")),1),a("div",ze,[u(d,{modelValue:e.value.isFixedHeader,"onUpdate:modelValue":l[23]||(l[23]=s=>e.value.isFixedHeader=s),size:"small",onChange:Na},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:h({opacity:e.value.layout!=="classic"?.5:1})},[a("div",ke,o(t.$t("layout.threeIsClassicSplitMenu")),1),a("div",Ae,[u(d,{modelValue:e.value.isClassicSplitMenu,"onUpdate:modelValue":l[24]||(l[24]=s=>e.value.isClassicSplitMenu=s),disabled:e.value.layout!=="classic",size:"small",onChange:Ka},null,8,["modelValue","disabled"])])],4),a("div",De,[a("div",Ee,o(t.$t("layout.threeIsLockScreen")),1),a("div",Ge,[u(d,{modelValue:e.value.isLockScreen,"onUpdate:modelValue":l[25]||(l[25]=s=>e.value.isLockScreen=s),size:"small",onChange:i},null,8,["modelValue"])])]),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.fourTitle")),1)]),_:1}),a("div",Le,[a("div",Fe,o(t.$t("layout.fourIsShowLogo")),1),a("div",He,[u(d,{modelValue:e.value.isShowLogo,"onUpdate:modelValue":l[26]||(l[26]=s=>e.value.isShowLogo=s),size:"small",onChange:Qa},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:h({opacity:e.value.layout==="classic"||e.value.layout==="transverse"?.5:1})},[a("div",Pe,o(t.$t("layout.fourIsBreadcrumb")),1),a("div",Re,[u(d,{modelValue:e.value.isBreadcrumb,"onUpdate:modelValue":l[27]||(l[27]=s=>e.value.isBreadcrumb=s),disabled:e.value.layout==="classic"||e.value.layout==="transverse",size:"small",onChange:Xa},null,8,["modelValue","disabled"])])],4),a("div",qe,[a("div",We,o(t.$t("layout.fourIsBreadcrumbIcon")),1),a("div",Oe,[u(d,{modelValue:e.value.isBreadcrumbIcon,"onUpdate:modelValue":l[28]||(l[28]=s=>e.value.isBreadcrumbIcon=s),size:"small",onChange:i},null,8,["modelValue"])])]),a("div",je,[a("div",Je,o(t.$t("layout.fourIsTagsview")),1),a("div",Ne,[u(d,{modelValue:e.value.isTagsview,"onUpdate:modelValue":l[29]||(l[29]=s=>e.value.isTagsview=s),size:"small",onChange:i},null,8,["modelValue"])])]),a("div",Ke,[a("div",Qe,o(t.$t("layout.fourIsTagsviewIcon")),1),a("div",Xe,[u(d,{modelValue:e.value.isTagsviewIcon,"onUpdate:modelValue":l[30]||(l[30]=s=>e.value.isTagsviewIcon=s),size:"small",onChange:i},null,8,["modelValue"])])]),a("div",Ye,[a("div",Ze,o(t.$t("layout.fourIsCacheTagsView")),1),a("div",ea,[u(d,{modelValue:e.value.isCacheTagsView,"onUpdate:modelValue":l[31]||(l[31]=s=>e.value.isCacheTagsView=s),size:"small",onChange:i},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:h({opacity:k.isMobile?.5:1})},[a("div",aa,o(t.$t("layout.fourIsSortableTagsView")),1),a("div",la,[u(d,{modelValue:e.value.isSortableTagsView,"onUpdate:modelValue":l[32]||(l[32]=s=>e.value.isSortableTagsView=s),disabled:!!k.isMobile,size:"small",onChange:Ya},null,8,["modelValue","disabled"])])],4),a("div",ta,[a("div",sa,o(t.$t("layout.fourIsShareTagsView")),1),a("div",ua,[u(d,{modelValue:e.value.isShareTagsView,"onUpdate:modelValue":l[33]||(l[33]=s=>e.value.isShareTagsView=s),size:"small",onChange:Za},null,8,["modelValue"])])]),a("div",oa,[a("div",ra,o(t.$t("layout.fourIsFooter")),1),a("div",ia,[u(d,{modelValue:e.value.isFooter,"onUpdate:modelValue":l[34]||(l[34]=s=>e.value.isFooter=s),size:"small",onChange:i},null,8,["modelValue"])])]),a("div",na,[a("div",da,o(t.$t("layout.fourIsGrayscale")),1),a("div",ca,[u(d,{modelValue:e.value.isGrayscale,"onUpdate:modelValue":l[35]||(l[35]=s=>e.value.isGrayscale=s),size:"small",onChange:l[36]||(l[36]=s=>S("grayscale"))},null,8,["modelValue"])])]),a("div",ma,[a("div",ba,o(t.$t("layout.fourIsInvert")),1),a("div",va,[u(d,{modelValue:e.value.isInvert,"onUpdate:modelValue":l[37]||(l[37]=s=>e.value.isInvert=s),size:"small",onChange:l[38]||(l[38]=s=>S("invert"))},null,8,["modelValue"])])]),a("div",ya,[a("div",ga,o(t.$t("layout.fourIsWartermark")),1),a("div",fa,[u(d,{modelValue:e.value.isWartermark,"onUpdate:modelValue":l[39]||(l[39]=s=>e.value.isWartermark=s),size:"small",onChange:el},null,8,["modelValue"])])]),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.fiveTitle")),1)]),_:1}),a("div",pa,[a("div",xa,o(t.$t("layout.fiveTagsStyle")),1),a("div",ha,[u(T,{modelValue:e.value.tagsStyle,"onUpdate:modelValue":l[40]||(l[40]=s=>e.value.tagsStyle=s),placeholder:"\u8BF7\u9009\u62E9",style:{width:"90px"},onChange:i},{default:v(()=>[u(g,{label:"\u98CE\u683C1",value:"tags-style-one"}),u(g,{label:"\u98CE\u683C4",value:"tags-style-four"}),u(g,{label:"\u98CE\u683C5",value:"tags-style-five"})]),_:1},8,["modelValue"])])]),a("div",Ca,[a("div",Va,o(t.$t("layout.fiveAnimation")),1),a("div",wa,[u(T,{modelValue:e.value.animation,"onUpdate:modelValue":l[41]||(l[41]=s=>e.value.animation=s),placeholder:"\u8BF7\u9009\u62E9",style:{width:"90px"},onChange:i},{default:v(()=>[u(g,{label:"slide-right",value:"slide-right"}),u(g,{label:"slide-left",value:"slide-left"}),u(g,{label:"opacitys",value:"opacitys"})]),_:1},8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Ba,o(t.$t("layout.fiveColumnsAsideStyle")),1),a("div",$a,[u(T,{modelValue:e.value.columnsAsideStyle,"onUpdate:modelValue":l[42]||(l[42]=s=>e.value.columnsAsideStyle=s),placeholder:"\u8BF7\u9009\u62E9",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:i},{default:v(()=>[u(g,{label:"\u5706\u89D2",value:"columns-round"}),u(g,{label:"\u5361\u7247",value:"columns-card"})]),_:1},8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15 mb27",style:h({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Ia,o(t.$t("layout.fiveColumnsAsideLayout")),1),a("div",Ta,[u(T,{modelValue:e.value.columnsAsideLayout,"onUpdate:modelValue":l[43]||(l[43]=s=>e.value.columnsAsideLayout=s),placeholder:"\u8BF7\u9009\u62E9",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:i},{default:v(()=>[u(g,{label:"\u6C34\u5E73",value:"columns-horizontal"}),u(g,{label:"\u5782\u76F4",value:"columns-vertical"})]),_:1},8,["modelValue","disabled"])])],4),u(C,{"content-position":"left"},{default:v(()=>[V(o(t.$t("layout.sixTitle")),1)]),_:1}),a("div",Ma,[a("div",{class:"layout-drawer-content-item",onClick:l[44]||(l[44]=s=>U("defaults"))},[a("section",{class:w(["el-container el-circular",{"drawer-layout-active":e.value.layout==="defaults"}])},l[50]||(l[50]=[a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:w(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="defaults"}])},[a("div",Sa,[a("p",Ua,o(t.$t("layout.sixDefaults")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:l[45]||(l[45]=s=>U("classic"))},[a("section",{class:w(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="classic"}])},l[51]||(l[51]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("aside",{class:"el-aside",style:{width:"20px"}}),a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:w(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="classic"}])},[a("div",_a,[a("p",za,o(t.$t("layout.sixClassic")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:l[46]||(l[46]=s=>U("transverse"))},[a("section",{class:w(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="transverse"}])},l[52]||(l[52]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:w(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="transverse"}])},[a("div",ka,[a("p",Aa,o(t.$t("layout.sixTransverse")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:l[47]||(l[47]=s=>U("columns"))},[a("section",{class:w(["el-container el-circular",{"drawer-layout-active":e.value.layout==="columns"}])},l[53]||(l[53]=[a("aside",{class:"el-aside-dark",style:{width:"10px"}},null,-1),a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:w(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="columns"}])},[a("div",Da,[a("p",Ea,o(t.$t("layout.sixColumns")),1)])],2)])]),a("div",Ga,[u(ol,{title:t.$t("layout.tipText"),type:"warning",closable:!1},null,8,["title"]),u(Ra,{class:"copy-config-btn",type:"primary",ref:"copyConfigBtnRef",onClick:tl},{default:v(()=>[u(Pa,{class:"mr5"},{default:v(()=>[u(rl)]),_:1}),V(" "+o(t.$t("layout.copyText")),1)]),_:1},512),u(Ra,{class:"copy-config-btn-reset",type:"info",onClick:sl},{default:v(()=>[u(Pa,{class:"mr5"},{default:v(()=>[u(il)]),_:1}),V(" "+o(t.$t("layout.resetText")),1)]),_:1})])]),_:1})]),_:1},8,["title","modelValue"])])}}}),[["__scopeId","data-v-2228483f"]])});export{Tl as __tla,Wa as default};
