import{S as u,q as b,__tla as F}from"./index.BSP3cg_z.js";import{d as _,B as y,a as w,b as h,f as t,G as e,t as x,v as A,E as B}from"./vue.CnN__PXn.js";let p,$=Promise.all([(()=>{try{return F}catch{}})()]).then(async()=>{let i,l,c,o,n,r,m,d,f;i={class:"error layout-padding"},l={class:"layout-padding-auto layout-padding-view"},c={class:"error-flex"},o={class:"left"},n={class:"left-item"},r={class:"left-item-animation left-item-title"},m={class:"left-item-animation left-item-msg"},d={class:"left-item-animation left-item-btn"},f=_({name:"noPower"}),p=b(_({...f,setup(k){const v=()=>{u.clear(),window.location.reload()};return(s,a)=>{const g=y("el-button");return h(),w("div",i,[t("div",l,[t("div",c,[t("div",o,[t("div",n,[a[0]||(a[0]=t("div",{class:"left-item-animation left-item-num"},"401",-1)),t("div",r,e(s.$t("noAccess.accessTitle")),1),t("div",m,e(s.$t("noAccess.accessMsg")),1),t("div",d,[x(g,{type:"primary",round:"",onClick:v},{default:A(()=>[B(e(s.$t("noAccess.accessBtn")),1)]),_:1})])])]),a[1]||(a[1]=t("div",{class:"right"},[t("img",{src:"https://img-blog.csdnimg.cn/3333f265772a4fa89287993500ecbf96.png?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,shadow_50,text_Q1NETiBAbHl0LXRvcA==,size_16,color_FFFFFF,t_70,g_se,x_16"})],-1))])])])}}}),[["__scopeId","data-v-ce7b9f94"]])});export{$ as __tla,p as default};
