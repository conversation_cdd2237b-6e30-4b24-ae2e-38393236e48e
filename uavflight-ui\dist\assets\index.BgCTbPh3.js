const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/generator.BIg3WM4U.js","assets/table.BExdFBu3.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/group.ZBPXjmD0.js","assets/template.BlMVjGfJ.js","assets/generator.D1-SGm6q.css","assets/edit.lWmXSGDk.js","assets/fieldtype.CAArssoY.js","assets/sortable.esm.BGML4dzN.js","assets/edit.CXY9CdCt.css","assets/preview.CuRzhMr-.js","assets/commonFunction.BmnVIZty.js","assets/preview.CVmkohOd.css"])))=>i.map(i=>d[i]);
import{v as O,a as g,f as V,c as $,q as z,__tla as B}from"./index.BSP3cg_z.js";import{i as S,__tla as F}from"./table.BExdFBu3.js";import{d as G,k as s,l as J,o as K,B as p,a as M,b as n,t as i,v as u,u as t,e as r,D as o,f as Q,j as h,E as f}from"./vue.CnN__PXn.js";let R,U=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return F}catch{}})()]).then(async()=>{let w,N;w={class:"layout-padding"},N={style:{"text-align":"center"}},R=z(G({__name:"index",setup(W){const{t:T}=O.useI18n(),H=h(()=>g(()=>import("./generator.BIg3WM4U.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4,5,6,7]))),P=h(()=>g(()=>import("./edit.lWmXSGDk.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([8,2,3,4,1,9,10,11]))),j=h(()=>g(()=>import("./preview.CuRzhMr-.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([12,2,3,4,1,13,14]))),x=s(),E=s(),C=J(),l=s(0),_=s(),d=s(),v=s(),c=s(),k=s(),m=async a=>{try{if(a===0)await c.value.submitHandle();else if(a===1){const e=await E.value.submitHandle();_.value=e.id,k.value=e.generatorType}if(l.value===a)return;l.value=a}catch{}},q=async()=>{await c.value.submitHandle(),x.value.openDialog(_.value)},A=async()=>{await c.value.submitHandle(),k.value==="0"&&V(`/gen/generator/download?tableIds=${[_.value].join(",")}`,{},`${d.value}.zip`),k.value==="1"&&S([_.value].join(",")).then(()=>{$().success(T("common.optSuccessText"))})};return K(()=>{d.value=C.query.tableName,v.value=C.query.dsName}),(a,e)=>{const D=p("el-step"),L=p("el-steps"),I=p("el-card"),y=p("el-button");return n(),M("div",w,[i(I,{class:"layout-padding-auto",shadow:"hover"},{default:u(()=>[i(L,{active:t(l),"finish-status":"success",simple:""},{default:u(()=>[i(D,{title:"\u57FA\u7840\u4FE1\u606F",onClick:e[0]||(e[0]=b=>m(0))}),i(D,{title:"\u6570\u636E\u4FEE\u6539",onClick:e[1]||(e[1]=b=>m(1))})]),_:1},8,["active"])]),_:1}),i(I,{class:"layout-padding-auto mt5",shadow:"hover"},{default:u(()=>[t(l)===0?(n(),r(t(H),{key:0,ref_key:"generatorRef",ref:E,tableName:t(d),dsName:t(v)},null,8,["tableName","dsName"])):o("",!0),t(l)===1?(n(),r(t(P),{key:1,ref_key:"editTableRef",ref:c,tableName:t(d),dsName:t(v)},null,8,["tableName","dsName"])):o("",!0),Q("div",N,[t(l)===0?(n(),r(y,{key:0,style:{"margin-top":"12px"},onClick:e[2]||(e[2]=b=>m(1))},{default:u(()=>e[4]||(e[4]=[f("\u4E0B\u4E00\u6B65")])),_:1})):o("",!0),t(l)===1?(n(),r(y,{key:1,style:{"margin-top":"12px"},onClick:e[3]||(e[3]=b=>m(0))},{default:u(()=>e[5]||(e[5]=[f("\u4E0A\u4E00\u6B65")])),_:1})):o("",!0),t(l)===1?(n(),r(y,{key:2,style:{"margin-top":"12px"},onClick:q},{default:u(()=>e[6]||(e[6]=[f("\u4FDD\u5B58\u5E76\u9884\u89C8")])),_:1})):o("",!0),t(l)===1?(n(),r(y,{key:3,style:{"margin-top":"12px"},onClick:A},{default:u(()=>e[7]||(e[7]=[f("\u4FDD\u5B58\u5E76\u751F\u6210")])),_:1})):o("",!0)])]),_:1}),i(t(j),{ref_key:"previewDialogRef",ref:x},null,512)])}}}),[["__scopeId","data-v-0d6b4db6"]])});export{U as __tla,R as default};
