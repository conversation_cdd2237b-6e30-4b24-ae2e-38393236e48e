import{S as D}from"./sortable.esm.BGML4dzN.js";import{q as A,__tla as C}from"./index.BSP3cg_z.js";import{B as d,a as F,b as r,t as o,v as t,e as i,D as b,N,f as p,g as O,G as _,E as g}from"./vue.CnN__PXn.js";let v,V=Promise.all([(()=>{try{return C}catch{}})()]).then(async()=>{const w={props:{modelValue:{type:Array,default:()=>[]},addTemplate:{type:Object,default:()=>{}},placeholder:{type:String,default:"\u6682\u65E0\u6570\u636E"},dragSort:{type:Boolean,default:!1},hideAdd:{type:Boolean,default:!1},hideDelete:{type:Boolean,default:!1}},data:()=>({data:[]}),mounted(){this.data=this.modelValue,this.dragSort&&this.rowDrop()},watch:{modelValue(){this.data=this.modelValue},data:{handler(){this.$emit("update:modelValue",this.data)},deep:!0}},methods:{rowDrop(){const e=this,a=this.$refs.table.$el.querySelector(".el-table__body-wrapper tbody");D.create(a,{handle:".move",animation:300,ghostClass:"ghost",onEnd({newIndex:l,oldIndex:h}){e.data.splice(l,0,e.data.splice(h,1)[0]);const n=e.data.slice(0),s=e.$refs.scFormTable.offsetHeight;e.$refs.scFormTable.style.setProperty("height",s+"px"),e.data=[],e.$nextTick(()=>{e.data=n,e.$nextTick(()=>{e.$refs.scFormTable.style.removeProperty("height")})})}})},rowAdd(){const e=JSON.parse(JSON.stringify(this.addTemplate));this.data.push(e)},rowDel(e,a){this.data.splice(a,1),this.$emit("delete",e)},pushRow(e){const a=e||JSON.parse(JSON.stringify(this.addTemplate));this.data.push(a)},deleteRow(e){this.data.splice(e,1)}}},x={class:"form-table",ref:"scFormTable"},$={class:"move",style:{cursor:"move"}};v=A(w,[["render",function(e,a,l,h,n,s){const m=d("el-button"),f=d("el-tooltip"),u=d("el-table-column"),k=d("WarningFilled"),y=d("el-icon"),S=d("Sort"),T=d("el-table");return r(),F("div",x,[o(T,{data:n.data,ref:"table",border:"",stripe:"","cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center",background:"var(--el-table-row-hover-bg-color)",color:"var(--el-text-color-primary)"}},{empty:t(()=>[g(_(l.placeholder),1)]),default:t(()=>[o(u,{type:"index",width:"50",fixed:"left"},{header:t(()=>[l.hideAdd?(r(),i(f,{key:1,content:"\u5E8F\u53F7",placement:"top"},{default:t(()=>a[0]||(a[0]=[g(" # ")])),_:1})):(r(),i(m,{key:0,type:"primary",icon:"el-icon-plus",size:"small",circle:"",onClick:s.rowAdd},null,8,["onClick"]))]),default:t(c=>[p("div",{class:O(["form-table-handle",{"form-table-handle-delete":!l.hideDelete}])},[p("span",null,_(c.$index+1),1),l.hideDelete?b("",!0):(r(),i(m,{key:0,type:"danger",icon:"el-icon-delete",size:"small",plain:"",circle:"",onClick:B=>s.rowDel(c.row,c.$index)},null,8,["onClick"]))],2)]),_:1}),l.dragSort?(r(),i(u,{key:0,label:"",width:"50"},{header:t(()=>[o(y,null,{default:t(()=>[o(f,{content:"\u62D6\u52A8\u6392\u5E8F",placement:"top"},{default:t(()=>[o(k)]),_:1})]),_:1})]),default:t(()=>[p("div",$,[o(y,null,{default:t(()=>[o(S)]),_:1})])]),_:1})):b("",!0),N(e.$slots,"default",{},void 0,!0)]),_:3},8,["data"])],512)}],["__scopeId","data-v-8a3cf01a"]])});export{V as __tla,v as default};
