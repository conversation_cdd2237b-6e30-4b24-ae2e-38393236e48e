const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/password.-GCC3Sdd.js","assets/vue.CnN__PXn.js","assets/index.BSP3cg_z.js","assets/index.DjeikzAe.css"])))=>i.map(i=>d[i]);
import{u as S,v as k,N as u,a as w,ae as O,c as p,S as b,af as A,q as B,__tla as D}from"./index.BSP3cg_z.js";import{d,s as E,l as j,z as N,k as _,c as P,o as T,B as $,a as z,b as C,f as a,t as y,v as J,u as L,j as R}from"./vue.CnN__PXn.js";let g,V=Promise.all([(()=>{try{return D}catch{}})()]).then(async()=>{let r,c,l,i;r={class:"outer-container"},c={class:"parent"},l={class:"child"},i=d({name:"loginIndex"}),g=B(d({...i,setup(W){const m=R(()=>w(()=>import("./password.-GCC3Sdd.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3]))),f=S(),{themeConfig:h}=E(f),{t:v}=k.useI18n(),t=j(),o=N();_(!1),_("account"),P(()=>h.value);const I=async()=>{var e,s,n;if(await O())p().wraning("\u62B1\u6B49\uFF0C\u60A8\u6CA1\u6709\u767B\u5F55\u6743\u9650"),b.clear();else{let q=A(new Date);(e=t.query)!=null&&e.redirect?o.push({path:t.query.redirect,query:Object.keys(((s=t.query)==null?void 0:s.params)||{}).length>0?JSON.parse((n=t.query)==null?void 0:n.params):""}):o.push("/");const x=v("signInText");p().success(`${q},${x}`),u.start()}};return T(()=>{u.done()}),(e,s)=>{const n=$("el-card");return C(),z("div",r,[s[1]||(s[1]=a("div",{class:"container1"},[a("img",{src:"/assets/pigx-app.BS8s1kyW.png",class:"logo-sty"}),a("p",{class:"index-top-font"},'\u6276\u7EE5\u53BF"AI+\u65E0\u4EBA\u673A\u573A"\u76D1\u6D4B\u76D1\u7BA1\u5E73\u53F0\uFF08\u6E20\u65E7\u8BD5\u70B9\uFF09')],-1)),a("div",c,[a("div",l,[y(n,null,{default:J(()=>[s[0]||(s[0]=a("div",{class:"login-name"},[a("span",null,"\u767B\u5F55")],-1)),y(L(m),{onSignInSuccess:I})]),_:1})])])])}}}),[["__scopeId","data-v-e9d9adbf"]])});export{V as __tla,g as default};
