import{s as t,__tla as c}from"./index.BSP3cg_z.js";let n,a,r,i,m,u,g,o,s,d,l=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{u=function(e){return t({url:"/admin/bigScreen/getRealTimeTaskList",method:"get",params:e})},m=e=>t({url:"/admin/bigScreen/getVideoInfoByTaskId",method:"post",data:e}),o=function(e){return t({url:"/admin/bigScreen/getRealEventList",method:"get",params:e})},s=function(){return t({url:"/admin/bigScreen/getUavAndEventInfo",method:"get",params:{}})},r=function(e){return t({url:"/admin/bigScreen/getEventDataHx",method:"get",params:e})},i=function(e){return t({url:"/admin/bigScreen/getEventTop10",method:"get",params:e})},g=function(){return t({url:"/admin/bigScreen/getIndexParams",method:"get",params:{}})},n=function(){return t({url:"/admin/bigScreen/getEventByDay",method:"get",params:{}})},d=function(e){return t({url:"/admin/bigScreen/getServiceCount",method:"get",params:e})},a=function(){return t({url:"/admin/bigScreen/getServiceLineChart",method:"get",params:{}})}});export{l as __tla,n as a,a as b,r as c,i as d,m as e,u as f,g,o as h,s as i,d as j};
