/*
 * @Description: 地图服务URL替换工具
 * 用于替换地图配置中的IP地址，以适应不同环境的部署
 */

/**
 * 替换URL中的IP地址
 * 只替换IP地址部分，保留端口号和其他路径参数
 * @param url 原始URL
 * @param newIp 新的IP地址
 * @returns 替换后的URL
 */
export function replaceIpInUrl(url: string, newIp: string): string {
  if (!url) return url;
  
  // 使用正则表达式匹配URL中的IP地址部分
  // 这个正则表达式匹配http://或https://后面的IP地址或域名，直到端口号(:)或路径(/)
  const ipRegex = /(https?:\/\/)([^:/]+)(:\d+|\/|$)/;
  
  return url.replace(ipRegex, (match, protocol, oldIp, portOrPath) => {
    // 如果是localhost或127.0.0.1，则替换为新IP
    if (oldIp === 'localhost' || oldIp.startsWith('127.0.0.1')) {
      return `${protocol}${newIp}${portOrPath}`;
    }
    // 否则保持不变
    return match;
  });
}

/**
 * 替换配置对象中所有URL的IP地址
 * @param config 配置对象
 * @param newIp 新的IP地址
 * @returns 替换后的配置对象
 */
export function replaceIpInConfig(config: any, newIp: string): any {
  if (!config) return config;
  
  // 创建配置的深拷贝，避免修改原对象
  const newConfig = JSON.parse(JSON.stringify(config));
  
  // 递归处理所有层级的URL
  function processObject(obj: any) {
    if (!obj || typeof obj !== 'object') return;
    
    // 遍历对象的所有属性
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      
      // 如果是URL属性，进行替换
      if (key === 'url' && typeof value === 'string') {
        obj[key] = replaceIpInUrl(value, newIp);
      } 
      // 如果是对象或数组，递归处理
      else if (typeof value === 'object') {
        processObject(value);
      }
    });
  }
  
  processObject(newConfig);
  return newConfig;
}

/**
 * 从环境变量获取地图服务IP地址
 * @returns 地图服务IP地址
 */
export function getMapServerIp(): string {
  return import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
  
}

/**
 * 从环境变量获取指定端口的地图服务地址
 * @param port 端口号
 * @returns 完整的地图服务地址
 */
export function getMapServerUrl(port: number): string {
  const ip = getMapServerIp();
  return `http://${ip}:${port}`;
}

/**
 * 获取Nginx服务器URL
 * @returns Nginx服务器URL
 */
export function getMapServerNginxUrl(): string {
  return getMapServerUrl(import.meta.env.VITE_MAP_SERVER_PORT_NGINX);
}

/**
 * 获取GeoServer服务器URL
 * @returns GeoServer服务器URL
 */
export function getMapServerGeoserverUrl(): string {
  return getMapServerUrl(import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER);
}

/**
 * 替换URL中的特定端口的IP地址
 * @param url 原始URL
 * @param port 端口号
 * @returns 替换后的URL
 */
export function replaceIpInUrlByPort(url: string, port: number): string {
  if (!url) return url;
  
  const portRegex = new RegExp(`(https?://)[^:/]+(:\\s*${port})`, 'i');
  const ip = getMapServerIp();
  
  return url.replace(portRegex, `$1${ip}$2`);
}

/**
 * 测试URL替换功能
 * 这个函数用于开发测试，可以在控制台调用
 */
export function testUrlReplacement(): void {
  const testUrls = [
    'http://127.0.0.1:81/baseMap.json',
    'http://localhost:8085/geoserver/wcs?service=WCS&version=2.0.1&request=GetCoverage',
    'http://127.0.0.1:8085/geoserver/gwc/demo/drone:geotiff_coverage?gridSet=EPSG:4326&format=image/png',
    'https://example.com/api/data',
    'http://127.0.0.1/api/no-port'
  ];
  
  const newIp = getMapServerIp();
  console.group('URL替换测试');
  console.log('使用的IP地址:', newIp);
  
  testUrls.forEach(url => {
    const replaced = replaceIpInUrl(url, newIp);
    console.log('原始URL:', url);
    console.log('替换后:', replaced);
    console.log('---');
  });
  
  // 测试配置对象替换
  const testConfig = {
    layers: [
      {
        id: 'layer1',
        url: 'http://127.0.0.1:81/data.json'
      },
      {
        id: 'layer2',
        url: 'http://localhost:8085/api'
      }
    ]
  };
  
  console.log('原始配置:', testConfig);
  const replacedConfig = replaceIpInConfig(testConfig, newIp);
  console.log('替换后配置:', replacedConfig);
  
  console.groupEnd();
} 