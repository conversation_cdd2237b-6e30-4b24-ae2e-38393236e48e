function y(o,f){if(!o)return o;const e=JSON.parse(JSON.stringify(o));return function u(t){t&&typeof t=="object"&&Object.keys(t).forEach(r=>{const n=t[r];r==="url"&&typeof n=="string"?t[r]=function(c,i){return c&&c.replace(/(https?:\/\/)([^:/]+)(:\d+|\/|$)/,(a,p,s,h)=>s==="localhost"||s.startsWith("127.0.0.1")?`${p}${i}${h}`:a)}(n,f):typeof n=="object"&&u(n)})}(e),e}function $(){return"**************"}function l(){return"http://**************:81"}export{$ as a,l as g,y as r};
