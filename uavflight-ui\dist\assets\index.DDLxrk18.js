const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/defaults.Cf_7ilb9.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/defaults.03ykorUT.css","assets/classic.DvjspQby.js","assets/transverse.D5y2O9jH.js","assets/columns.I5A59mO8.js"])))=>i.map(i=>d[i]);
import{u as c,a,L as s,e as i,__tla as d}from"./index.BSP3cg_z.js";import{d as n,s as m,h as L,i as h,e as E,b as p,r as v,j as l,u as w}from"./vue.CnN__PXn.js";let r,f=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{let _;_=n({name:"layout"}),r=n({..._,setup(R){const u={defaults:l(()=>a(()=>import("./defaults.Cf_7ilb9.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([0,1,2,3,4]))),classic:l(()=>a(()=>import("./classic.DvjspQby.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([5,1,2,3]))),transverse:l(()=>a(()=>import("./transverse.D5y2O9jH.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([6,1,2,3]))),columns:l(()=>a(()=>import("./columns.I5A59mO8.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([7,1,2,3])))},y=c(),{themeConfig:e}=m(y),o=()=>{s.get("oldLayout")||s.set("oldLayout",e.value.layout);const t=document.body.clientWidth;t<1e3?(e.value.isCollapse=!1,i.emit("layoutMobileResize",{layout:"defaults",clientWidth:t})):i.emit("layoutMobileResize",{layout:s.get("oldLayout")?s.get("oldLayout"):e.value.layout,clientWidth:t})};return L(()=>{o(),window.addEventListener("resize",o)}),h(()=>{window.removeEventListener("resize",o)}),(t,P)=>(p(),E(v(u[w(e).layout])))}})});export{f as __tla,r as default};
