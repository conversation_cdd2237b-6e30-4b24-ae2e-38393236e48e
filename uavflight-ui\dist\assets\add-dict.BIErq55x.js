import{v as R,r as v,c as k,__tla as S}from"./index.BSP3cg_z.js";import{b as J,h as K,a as Q,__tla as W}from"./dict.D9OX-VAS.js";import{u as X,__tla as Y}from"./dict.DrX0Qdnc.js";import{d as I,k as y,A as U,B as i,m as Z,e as V,b as g,v as o,q as ee,u as a,t,a as le,F as ae,p as te,E as T,G as w,f as re,H as oe,y as de}from"./vue.CnN__PXn.js";let C,ie=Promise.all([(()=>{try{return S}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})()]).then(async()=>{let $,x;$={class:"dialog-footer"},x=I({name:"systemDicDialog"}),C=I({...x,emits:["refresh"],setup(se,{expose:D,emit:q}){const B=q,{dict_type:L}=X("dict_type"),{t:c}=R.useI18n(),f=y(),s=y(!1),n=y(!1);y({});const r=U({id:"",dictType:"",description:"",systemFlag:"0",remarks:"",columns:[]}),z=U({dictType:[{validator:v.overLength,trigger:"blur"},{required:!0,message:"\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:v.validatorNameCn,trigger:"blur"},{validator:(l,d,_)=>{J(l,d,_,r.id!=="")},trigger:"blur"}],systemFlag:[{required:!0,message:"\u5B57\u5178\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{validator:v.overLength,trigger:"blur"},{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],columns:[{required:!0,message:"\u5B57\u5178\u9879\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),E=async()=>{if(!await f.value.validate().catch(()=>{}))return!1;try{n.value=!0;const{data:l}=await K(r);r.columns.forEach(async d=>{d.dictId=l.id,d.dictType=r.dictType,await Q(d)}),k().success(c("common.addSuccessText")),s.value=!1,B("refresh",r.dictType)}catch(l){k().error(l.msg)}finally{n.value=!1}};let A=1;const G=()=>{r.columns.push({sortOrder:""+A++})};return D({openDialog:()=>{s.value=!0,r.id="",de(()=>{var l;(l=f.value)==null||l.resetFields()})}}),(l,d)=>{const _=i("el-radio"),P=i("el-radio-group"),m=i("el-form-item"),p=i("el-input"),b=i("el-button"),h=i("el-table-column"),j=i("el-table"),H=i("el-col"),M=i("el-form"),N=i("el-dialog"),O=Z("loading");return g(),V(N,{title:"\u65B0\u589E\u5B57\u5178",modelValue:a(s),"onUpdate:modelValue":d[4]||(d[4]=e=>oe(s)?s.value=e:null),width:"600"},{footer:o(()=>[re("span",$,[t(b,{onClick:d[3]||(d[3]=e=>s.value=!1)},{default:o(()=>[T(w(l.$t("common.cancelButtonText")),1)]),_:1}),t(b,{onClick:E,type:"primary",disabled:a(n)},{default:o(()=>[T(w(l.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:o(()=>[ee((g(),V(M,{model:a(r),rules:a(z),"label-width":"100px",ref_key:"dicDialogFormRef",ref:f},{default:o(()=>[t(m,{label:l.$t("sysdict.systemFlag"),prop:"systemFlag"},{default:o(()=>[t(P,{modelValue:a(r).systemFlag,"onUpdate:modelValue":d[0]||(d[0]=e=>a(r).systemFlag=e)},{default:o(()=>[(g(!0),le(ae,null,te(a(L),(e,u)=>(g(),V(_,{border:"",key:u,label:e.value},{default:o(()=>[T(w(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(m,{label:l.$t("sysdict.dictType"),prop:"dictType"},{default:o(()=>[t(p,{placeholder:l.$t("sysdict.inputDictTypeTip"),disabled:a(r).id!=="",clearable:"",modelValue:a(r).dictType,"onUpdate:modelValue":d[1]||(d[1]=e=>a(r).dictType=e)},null,8,["placeholder","disabled","modelValue"])]),_:1},8,["label"]),t(m,{label:l.$t("sysdict.description"),prop:"description"},{default:o(()=>[t(p,{placeholder:l.$t("sysdict.inputDescriptionTip"),clearable:"",modelValue:a(r).description,"onUpdate:modelValue":d[2]||(d[2]=e=>a(r).description=e)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),t(H,{span:24,class:"mb20"},{default:o(()=>[t(m,{label:a(c)("dictItem.name"),prop:"columns"},{default:o(()=>[t(j,{data:a(r).columns,border:"",style:{width:"100%"},"max-height":"500"},{default:o(()=>[t(h,{type:"index",label:a(c)("createTable.index"),width:"50"},{header:o(()=>[t(b,{icon:"Plus",size:"small",type:"primary",circle:"",onClick:G})]),default:o(e=>[t(b,{icon:"Minus",size:"small",type:"danger",circle:"",onClick:u=>{return F=e.$index,e.row,void r.columns.splice(F,1);var F}},null,8,["onClick"])]),_:1},8,["label"]),t(h,{prop:"label",label:l.$t("dictItem.label"),"show-overflow-tooltip":""},{default:o(e=>[t(p,{modelValue:e.row.label,"onUpdate:modelValue":u=>e.row.label=u,placeholder:a(c)("dictItem.inputLabelTip")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:1},8,["label"]),t(h,{prop:"value",label:l.$t("dictItem.itemValue"),"show-overflow-tooltip":""},{default:o(e=>[t(p,{modelValue:e.row.value,"onUpdate:modelValue":u=>e.row.value=u,placeholder:a(c)("dictItem.inputItemValueTip")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["data"])]),_:1},8,["label"])]),_:1})]),_:1},8,["model","rules"])),[[O,a(n)]])]),_:1},8,["modelValue"])}}})});export{ie as __tla,C as default};
