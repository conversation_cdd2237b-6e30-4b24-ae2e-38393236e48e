
<template>
  <div class="spatial-analysis-container">
    <!-- 页面头部 -->
    <div class="header">
      <h2>空间分析任务管理</h2>
      <div class="actions">
        <span class="refresh-info" v-if="lastRefreshTime">
          上次刷新: {{ formatRefreshTime(lastRefreshTime) }}
          <template v-if="autoRefresh && nextRefreshCountdown > 0">
            | 下次刷新: {{ nextRefreshCountdown }}秒
          </template>
        </span>
        <el-button type="primary" :icon="Refresh" @click="handleManualRefresh" :loading="loading">刷新数据</el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="page-header">
      <div class="filter-bar">
        <div class="search-area">
          <el-input
            v-model="searchQuery"
            placeholder="输入任务ID搜索"
            clearable
            prefix-icon="Search"
            style="width: 300px"
            @input="handleSearch"
          />
          <el-button
            v-if="!hasActiveFilters"
            type="primary"
            :icon="Filter"
            @click="showFilterDialog"
          >高级筛选</el-button>
          <el-button
            v-else
            type="warning"
            :icon="Delete"
            @click="clearAllFilters"
          >取消筛选</el-button>
          <el-button type="success" :icon="PictureFilled" @click="openBatchAnalysis">批量分析与删除</el-button>
        </div>
      </div>

      <div class="right-section" v-if="!loading && !error">
        <el-button type="warning" :icon="RefreshLeft" @click="openBatchExport">批量下载</el-button>
        <div class="task-count">
          共 <strong>{{ totalTasks }}</strong> 个任务，当前显示 <strong>{{ filteredTasks.length }}</strong> 个
          <el-tag v-if="hasActiveFilters" size="small" type="info">已应用筛选</el-tag>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <el-table
        v-loading="loading"
        :data="paginatedTasks"
        :key="`table-${refreshTrigger}`"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="任务ID" width="180">
          <template #default="{ row }">
            <el-tag type="primary">{{ row.id }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="endTime" label="发布时间" width="200">
          <template #default="{ row }">
            <el-icon><Clock /></el-icon>
            {{ formatDateTime(row.endTime) }}
          </template>
        </el-table-column>

        <el-table-column label="空间范围" min-width="300">
          <template #default="{ row }">
            <div class="bbox-info">
              <div v-if="row.bbox && isValidBbox(row.bbox)" class="bbox-row">
                <span class="bbox-label">经纬度：</span>
                <span class="bbox-value">{{ formatCoordinate(row.bbox.minx) }} ~ {{ formatCoordinate(row.bbox.maxx) }}, {{ formatCoordinate(row.bbox.miny) }} ~ {{ formatCoordinate(row.bbox.maxy) }}</span>
              </div>
              <div v-else class="no-data">
                <span class="no-data-text">暂无数据</span>
              </div>
            </div>
          </template>
        </el-table-column>



        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewAnalysisDetails(row)"
              :icon="View"
            >
              操作
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredTasks.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 排序筛选弹窗 -->
    <FilterDialog
      v-model="filterDialogVisible"
      :search-query="searchQuery"
      :date-range="dateRange"
      :sort-field="sortField"
      :sort-order="sortOrder"
      @confirm="handleFilterConfirm"
    />

    <!-- 分析详情弹窗 -->
    <AnalysisDetailDialog
      v-model="detailDialogVisible"
      :task="selectedTask"
    />

    <!-- 批量下载弹窗 -->
    <BatchDownloadDialog
      v-model="batchDownloadDialogVisible"
    />

    <!-- 批量分析弹窗 -->
    <BatchAnalysisDialog
      v-model="batchAnalysisDialogVisible"
    />
  </div>
</template>

<script setup lang="ts">
// Vue相关导入
import { onMounted, onBeforeUnmount, ref, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh, Clock, View, Filter, Delete, PictureFilled, RefreshLeft } from '@element-plus/icons-vue';

// 组件导入
import FilterDialog from './components/FilterDialog.vue';
import AnalysisDetailDialog from './components/AnalysisDetailDialog.vue';
import BatchDownloadDialog from './components/BatchDownloadDialog.vue';
import BatchAnalysisDialog from './components/BatchAnalysisDialog.vue';

// 接口类型定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface TaskDetails {
  id: string;
  startTime: string;
  endTime: string;
  status: string;
  map_config: {
    status: string;
    start_time: string;
    end_time: string;
    bbox: BBox;
    layer_id: string;
    theme: string;
  };
}

interface Task {
  task_id: string;
  start_time: string;
  end_time: string;
  status: string;
  has_task_info: boolean;
  details: TaskDetails;
}

interface ApiResponse {
  status: string;
  count: number;
  tasks: Task[];
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

// 响应式数据
const loading = ref(false);
const error = ref<string | null>(null);
const tasks = ref<Task[]>([]);
const searchQuery = ref('');
const dateRange = ref<[string, string] | null>(null);
const sortField = ref('endTime');
const sortOrder = ref('desc');
const refreshTrigger = ref(0); // 强制刷新触发器
const currentPage = ref(1);
const pageSize = ref(20);
const detailDialogVisible = ref(false);
const filterDialogVisible = ref(false);
const batchDownloadDialogVisible = ref(false);
const batchAnalysisDialogVisible = ref(false);
const selectedTask = ref<ProcessedTask | null>(null);

// 自动刷新相关
const autoRefresh = ref(true); // 默认开启自动刷新
const lastRefreshTime = ref<Date | null>(null);
const nextRefreshCountdown = ref(60); // 初始化为60秒
const refreshInterval = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const REFRESH_INTERVAL = 60000; // 60秒，与onedragon页面保持一致

// 计算属性
const totalTasks = computed(() => tasks.value.length);

const hasActiveFilters = computed(() => {
  return searchQuery.value.trim() !== '' ||
         (dateRange.value && dateRange.value[0] && dateRange.value[1]);
});

const filteredTasks = computed(() => {
  // 使用刷新触发器确保计算属性重新执行
  refreshTrigger.value;

  console.log('计算属性重新执行，当前排序参数:', {
    sortField: sortField.value,
    sortOrder: sortOrder.value,
    searchQuery: searchQuery.value,
    dateRange: dateRange.value
  });

  console.log('原始任务数据:', tasks.value.length);
  console.log('完成状态的任务:', tasks.value.filter(task => task.status === '完成').length);
  console.log('有details的任务:', tasks.value.filter(task => task.status === '完成' && task.details).length);
  console.log('有map_config的任务:', tasks.value.filter(task => task.status === '完成' && task.details?.map_config).length);

  let filtered: ProcessedTask[] = [];
  try {
    filtered = tasks.value
      .filter(task => {
        const isCompleted = task.status === '完成';
        const hasDetails = task.details;
        const hasMapConfig = task.details?.map_config;

        if (!isCompleted) return false;
        if (!hasDetails) {
          console.warn('任务缺少details:', task.id);
          return false;
        }
        if (!hasMapConfig) {
          console.warn('任务缺少map_config:', task.id);
          return false;
        }

        return true;
      })
      .map(task => {
        try {
          return {
            id: task.details.id,
            endTime: task.details.endTime,
            bbox: task.details.map_config.bbox || null, // 允许bbox为空
            analysisCount: 0, // 暂时设为0，后续实现
            layer_name: task.details.map_config.layer_id,
            theme: task.details.map_config.theme
          };
        } catch (mapError) {
          console.error('处理任务数据时出错:', task.id, mapError);
          return null;
        }
      })
      .filter(task => task !== null); // 过滤掉处理失败的任务

    console.log('筛选后的任务数量:', filtered.length);
  } catch (filterError) {
    console.error('筛选任务时出错:', filterError);
    filtered = [];
  }

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.trim().toLowerCase();
    filtered = filtered.filter(task =>
      task.id.toLowerCase().includes(query)
    );
  }

  // 时间范围过滤
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    const [startDate, endDate] = dateRange.value;
    filtered = filtered.filter(task => {
      const taskDate = task.endTime.split(' ')[0]; // 提取日期部分 YYYY-MM-DD
      return taskDate >= startDate && taskDate <= endDate;
    });
  }

  // 排序
  console.log('排序参数:', { sortField: sortField.value, sortOrder: sortOrder.value });
  console.log('排序前全部数据:', filtered.map(item => ({ id: item.id, endTime: item.endTime })));

  // 创建新数组进行排序，确保Vue响应式系统能检测到变化
  filtered = [...filtered].sort((a, b) => {
    let aValue: any, bValue: any;

    if (sortField.value === 'endTime') {
      aValue = new Date(a.endTime).getTime();
      bValue = new Date(b.endTime).getTime();
    } else if (sortField.value === 'id') {
      aValue = a.id;
      bValue = b.id;
    } else if (sortField.value === 'layer_name') {
      aValue = a.layer_name || '';
      bValue = b.layer_name || '';
    } else if (sortField.value === 'theme') {
      aValue = a.theme || '';
      bValue = b.theme || '';
    } else {
      aValue = a[sortField.value as keyof ProcessedTask];
      bValue = b[sortField.value as keyof ProcessedTask];
    }

    // 处理undefined或null值
    if (aValue === undefined || aValue === null) aValue = '';
    if (bValue === undefined || bValue === null) bValue = '';

    const result = sortOrder.value === 'asc' ?
      (aValue > bValue ? 1 : -1) :
      (aValue < bValue ? 1 : -1);

    return result;
  });

  console.log('排序后全部数据:', filtered.map(item => ({ id: item.id, endTime: item.endTime })));

  return filtered;
});

const paginatedTasks = computed(() => {
  // 使用刷新触发器确保重新计算
  refreshTrigger.value;

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  const result = filteredTasks.value.slice(start, end);

  console.log('分页数据计算:', {
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    start,
    end,
    totalFiltered: filteredTasks.value.length,
    paginatedResult: result.map(t => ({ id: t.id, endTime: t.endTime }))
  });

  return result;
});

// 方法
const fetchTasks = async () => {
  loading.value = true;
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/map/odm/tasks`;

    console.log('环境变量调试信息:', {
      VITE_MAP_SERVER_IP: import.meta.env.VITE_MAP_SERVER_IP,
      VITE_GEOSERVER_HD_PORT: import.meta.env.VITE_GEOSERVER_HD_PORT,
      计算出的IP: geoserverIp,
      计算出的端口: geoserverPort,
      最终URL: apiUrl
    });

    console.log('获取ODM任务列表，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();

    if (data.status === 'success') {
      tasks.value = data.tasks;
      const completedCount = data.tasks.filter(task => task.status === '完成').length;
      lastRefreshTime.value = new Date();

      // 重新开始倒计时（如果自动刷新开启）
      if (autoRefresh.value) {
        startCountdown();
      }

      console.log(`成功获取 ${data.count} 个任务，其中 ${completedCount} 个已完成`);

      // 详细调试信息
      console.log('API返回的原始数据结构:', {
        status: data.status,
        count: data.count,
        tasksLength: data.tasks?.length,
        firstTaskSample: data.tasks?.[0]
      });

      // 检查任务数据结构
      const completedTasks = data.tasks.filter(task => task.status === '完成');
      console.log('完成任务的数据结构样本:', completedTasks.slice(0, 2));

      // 检查details和map_config结构
      const tasksWithDetails = completedTasks.filter(task => task.details);
      const tasksWithMapConfig = completedTasks.filter(task => task.details?.map_config);
      const tasksWithBbox = completedTasks.filter(task => task.details?.map_config?.bbox);

      console.log('数据结构统计:', {
        总任务数: data.tasks.length,
        完成任务数: completedTasks.length,
        有details的任务: tasksWithDetails.length,
        有map_config的任务: tasksWithMapConfig.length,
        有bbox的任务: tasksWithBbox.length
      });

    } else {
      throw new Error('API返回状态不正确');
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
    ElMessage.error('获取任务列表失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};



const handleManualRefresh = () => {
  fetchTasks();
  if (autoRefresh.value) {
    startCountdown(); // 手动刷新时重置倒计时
  }
};

const toggleAutoRefresh = (val: boolean) => {
  if (val) {
    startAutoRefresh();
    startCountdown();
  } else {
    stopAutoRefresh();
    stopCountdown();
  }
};

const startAutoRefresh = () => {
  stopAutoRefresh(); // 先清除可能存在的定时器

  refreshInterval.value = window.setInterval(() => {
    fetchTasks();
  }, REFRESH_INTERVAL);

  console.log('启动自动刷新');
};

const stopAutoRefresh = () => {
  if (refreshInterval.value !== null) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
    console.log('停止自动刷新');
  }
};

// 倒计时功能
const startCountdown = () => {
  stopCountdown(); // 先清除可能存在的定时器
  nextRefreshCountdown.value = 60; // 初始化倒计时为60秒

  countdownInterval.value = window.setInterval(() => {
    if (nextRefreshCountdown.value > 0) {
      nextRefreshCountdown.value--;
    } else {
      // 倒计时结束，触发刷新
      fetchTasks();
      // 不需要手动重置倒计时，因为fetchTasks会调用startCountdown
    }
  }, 1000);

  console.log('启动倒计时');
};

const stopCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
    console.log('停止倒计时');
  }
};



const handleSearch = () => {
  currentPage.value = 1; // 搜索时重置到第一页
};

const clearAllFilters = () => {
  searchQuery.value = '';
  dateRange.value = null;
  currentPage.value = 1;
};

const showFilterDialog = () => {
  filterDialogVisible.value = true;
};

const handleFilterConfirm = (filters: any) => {
  console.log('筛选确认，接收到的参数:', filters);

  // 先保存旧值用于比较
  const oldSortField = sortField.value;
  const oldSortOrder = sortOrder.value;

  searchQuery.value = filters.searchQuery;
  dateRange.value = filters.dateRange;
  sortField.value = filters.sortField;
  sortOrder.value = filters.sortOrder;
  currentPage.value = 1; // 重置到第一页

  // 强制触发重新计算
  refreshTrigger.value++;

  nextTick(() => {
    console.log('筛选后的排序参数:', {
      sortField: sortField.value,
      sortOrder: sortOrder.value
    });
    console.log('排序是否改变:', {
      sortFieldChanged: oldSortField !== sortField.value,
      sortOrderChanged: oldSortOrder !== sortOrder.value
    });
    console.log('强制刷新触发器:', refreshTrigger.value);
    console.log('当前分页数据:', paginatedTasks.value.map(t => ({ id: t.id, endTime: t.endTime })));
  });
};

const openBatchAnalysis = () => {
  batchAnalysisDialogVisible.value = true;
};

const openBatchExport = () => {
  batchDownloadDialogVisible.value = true;
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
};

const viewAnalysisDetails = (task: ProcessedTask) => {
  selectedTask.value = task;
  detailDialogVisible.value = true;
};

// 格式化刷新时间 - 与onedragon页面保持一致
const formatRefreshTime = (date: Date) => {
  if (!date) return '';

  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) { // 小于1分钟
    return `${Math.floor(diff / 1000)}秒前`;
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`;
  } else {
    return date.toLocaleTimeString('zh-CN');
  }
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatCoordinate = (coord: number) => {
  return coord.toFixed(6);
};

// 验证bbox数据是否有效
const isValidBbox = (bbox: any) => {
  if (!bbox) return false;

  const { minx, miny, maxx, maxy } = bbox;

  // 检查所有坐标是否存在且为有效数字
  if (typeof minx !== 'number' || typeof miny !== 'number' ||
      typeof maxx !== 'number' || typeof maxy !== 'number') {
    return false;
  }

  // 检查坐标是否为NaN或无穷大
  if (isNaN(minx) || isNaN(miny) || isNaN(maxx) || isNaN(maxy) ||
      !isFinite(minx) || !isFinite(miny) || !isFinite(maxx) || !isFinite(maxy)) {
    return false;
  }

  // 检查坐标范围是否合理（经度-180到180，纬度-90到90）
  if (minx < -180 || minx > 180 || maxx < -180 || maxx > 180 ||
      miny < -90 || miny > 90 || maxy < -90 || maxy > 90) {
    return false;
  }

  // 检查最小值是否小于最大值
  if (minx >= maxx || miny >= maxy) {
    return false;
  }

  return true;
};

// 生命周期
onMounted(() => {
  fetchTasks();
  if (autoRefresh.value) {
    startAutoRefresh();
    startCountdown(); // 启动倒计时
  }
});

onBeforeUnmount(() => {
  stopAutoRefresh();
  stopCountdown();
});
</script>

<style lang="scss" scoped>
.spatial-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 20px;
      font-weight: 500;
    }

    .actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .refresh-info {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .filter-bar {
      .search-area {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .right-section {
      display: flex;
      align-items: center;
      gap: 16px;

      .task-count {
        color: #606266;
        font-size: 14px;

        strong {
          color: #409EFF;
        }

        .el-tag {
          margin-left: 10px;
        }
      }
    }
  }

  .task-list-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .bbox-info {
      .bbox-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .bbox-label {
          color: #909399;
          font-size: 12px;
          width: 50px;
          flex-shrink: 0;
        }

        .bbox-value {
          color: #606266;
          font-size: 12px;
          font-family: 'Courier New', monospace;
          line-height: 1.4;
        }
      }

      .no-data {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 0;

        .no-data-text {
          color: #C0C4CC;
          font-size: 12px;
          font-style: italic;
        }
      }
    }

    .pagination-section {
      padding: 20px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
  }


}

// 全局样式覆盖
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

:deep(.el-pagination) {
  .el-pagination__total {
    color: #606266;
  }
}
</style>