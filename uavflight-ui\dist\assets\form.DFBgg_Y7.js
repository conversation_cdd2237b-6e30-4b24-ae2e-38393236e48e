import{u as J,__tla as K}from"./dict.DrX0Qdnc.js";import{v as M,c as w,r as s,__tla as N}from"./index.BSP3cg_z.js";import{p as X,a as Y,g as Z,v as ee,__tla as ae}from"./client.CTWjgIhK.js";import{d as z,k as h,A as le,B as u,m as te,e as m,b as c,v as i,q as re,u as e,t as o,D as _,a as R,F as G,p as q,E as f,G as V,f as ie,H as oe,y as de}from"./vue.CnN__PXn.js";import{__tla as ue}from"./dict.D9OX-VAS.js";let I,ne=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{let T,k;T={class:"dialog-footer"},k=z({name:"SysOauthClientDetailsDialog"}),I=z({...k,emits:["refresh"],setup(se,{expose:L,emit:x}){const B=x,{t:r}=M.useI18n(),v=h(),g=h(!1),b=h(!1),{grant_types:F,common_status:C}=J("grant_types","common_status"),a=le({id:"",clientId:"",clientSecret:"",scope:"server",authorizedGrantTypes:[],webServerRedirectUri:"",authorities:"",accessTokenValidity:43200,refreshTokenValidity:2592001,autoapprove:"true",delFlag:"",createBy:"",updateBy:"",createTime:"",updateTime:"",tenantId:"",onlineQuantity:"1",captchaFlag:"1",encFlag:"1"}),D=h({clientId:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:s.validatorLowercase,trigger:"blur"},{validator:(d,l,p)=>{ee(d,l,p,a.id!=="")},trigger:"blur"}],clientSecret:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{validator:s.validatorLower,trigger:"blur"}],scope:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u57DF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],authorizedGrantTypes:[{required:!0,message:"\u6388\u6743\u6A21\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],accessTokenValidity:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u4EE4\u724C\u65F6\u6548\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{type:"number",min:1,message:"\u4EE4\u724C\u65F6\u6548\u4E0D\u80FD\u5C0F\u4E8E\u4E00\u5C0F\u65F6",trigger:"blur"}],refreshTokenValidity:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u5237\u65B0\u65F6\u6548\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},{type:"number",min:1,message:"\u5237\u65B0\u65F6\u6548\u4E0D\u80FD\u5C0F\u4E8E\u4E24\u5C0F\u65F6",trigger:"blur"}],autoapprove:[{required:!0,message:"\u81EA\u52A8\u653E\u884C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],webServerRedirectUri:[{validator:s.overLength,trigger:"blur"},{required:!0,message:"\u56DE\u8C03\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],authorities:[{validator:s.overLength,trigger:"blur"}]}),$=async()=>{if(!await v.value.validate().catch(()=>{}))return!1;try{b.value=!0,a.id?await X(a):await Y(a),w().success(r(a.id?"common.editSuccessText":"common.addSuccessText")),g.value=!1,B("refresh")}catch(d){w().error(d.msg)}finally{b.value=!1}},A=d=>{Z(d).then(l=>{Object.assign(a,l.data)})};return L({openDialog:d=>{g.value=!0,a.id="",de(()=>{var l;(l=v.value)==null||l.resetFields()}),d&&(a.id=d,A(d))}}),(d,l)=>{const p=u("el-input"),n=u("el-form-item"),O=u("el-option"),j=u("el-select"),S=u("el-input-number"),E=u("el-radio"),H=u("el-radio-group"),P=u("el-form"),U=u("el-button"),Q=u("el-dialog"),W=te("loading");return c(),m(Q,{"close-on-click-modal":!1,title:e(a).id?d.$t("common.editBtn"):d.$t("common.addBtn"),width:"600",draggable:"",modelValue:e(g),"onUpdate:modelValue":l[10]||(l[10]=t=>oe(g)?g.value=t:null)},{footer:i(()=>[ie("span",T,[o(U,{onClick:l[9]||(l[9]=t=>g.value=!1)},{default:i(()=>[f(V(d.$t("common.cancelButtonText")),1)]),_:1}),o(U,{onClick:$,type:"primary",disabled:e(b)},{default:i(()=>[f(V(d.$t("common.confirmButtonText")),1)]),_:1},8,["disabled"])])]),default:i(()=>[re((c(),m(P,{model:e(a),rules:e(D),formDialogRef:"","label-width":"120px",ref_key:"dataFormRef",ref:v},{default:i(()=>[o(n,{label:e(r)("client.clientId"),prop:"clientId"},{default:i(()=>[o(p,{placeholder:e(r)("client.inputClientIdTip"),modelValue:e(a).clientId,"onUpdate:modelValue":l[0]||(l[0]=t=>e(a).clientId=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),o(n,{label:e(r)("client.clientSecret"),prop:"clientSecret"},{default:i(()=>[o(p,{placeholder:e(r)("client.inputClientSecretTip"),modelValue:e(a).clientSecret,"onUpdate:modelValue":l[1]||(l[1]=t=>e(a).clientSecret=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),o(n,{label:e(r)("client.scope"),prop:"scope"},{default:i(()=>[o(p,{placeholder:e(r)("client.inputScopeTip"),modelValue:e(a).scope,"onUpdate:modelValue":l[2]||(l[2]=t=>e(a).scope=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),o(n,{label:e(r)("client.authorizedGrantTypes"),prop:"authorizedGrantTypes"},{default:i(()=>[o(j,{"collapse-tags":"","collapse-tags-tooltip":"",multiple:"",modelValue:e(a).authorizedGrantTypes,"onUpdate:modelValue":l[3]||(l[3]=t=>e(a).authorizedGrantTypes=t)},{default:i(()=>[(c(!0),R(G,null,q(e(F),(t,y)=>(c(),m(O,{key:y,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),o(n,{label:e(r)("client.accessTokenValidity"),prop:"accessTokenValidity"},{default:i(()=>[o(S,{placeholder:e(r)("client.inputAccessTokenValidityTip"),modelValue:e(a).accessTokenValidity,"onUpdate:modelValue":l[4]||(l[4]=t=>e(a).accessTokenValidity=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),o(n,{label:e(r)("client.refreshTokenValidity"),prop:"refreshTokenValidity"},{default:i(()=>[o(S,{placeholder:e(r)("client.inputRefreshTokenValidityTip"),modelValue:e(a).refreshTokenValidity,"onUpdate:modelValue":l[5]||(l[5]=t=>e(a).refreshTokenValidity=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),e(a).authorizedGrantTypes.includes("authorization_code")?(c(),m(n,{key:0,label:e(r)("client.autoapprove"),prop:"autoapprove"},{default:i(()=>[o(H,{modelValue:e(a).autoapprove,"onUpdate:modelValue":l[6]||(l[6]=t=>e(a).autoapprove=t)},{default:i(()=>[(c(!0),R(G,null,q(e(C),(t,y)=>(c(),m(E,{key:y,label:t.value,border:""},{default:i(()=>[f(V(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):_("",!0),e(a).authorizedGrantTypes.includes("authorization_code")?(c(),m(n,{key:1,label:e(r)("client.authorities"),prop:"authorities"},{default:i(()=>[o(p,{placeholder:e(r)("client.inputAuthoritiesTip"),modelValue:e(a).authorities,"onUpdate:modelValue":l[7]||(l[7]=t=>e(a).authorities=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"])):_("",!0),e(a).authorizedGrantTypes.includes("authorization_code")?(c(),m(n,{key:2,label:e(r)("client.webServerRedirectUri"),prop:"webServerRedirectUri"},{default:i(()=>[o(p,{placeholder:e(r)("client.inputWebServerRedirectUriTip"),modelValue:e(a).webServerRedirectUri,"onUpdate:modelValue":l[8]||(l[8]=t=>e(a).webServerRedirectUri=t)},null,8,["placeholder","modelValue"])]),_:1},8,["label"])):_("",!0)]),_:1},8,["model","rules"])),[[W,e(b)]])]),_:1},8,["title","modelValue"])}}})});export{ne as __tla,I as default};
