import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { getProjection } from 'ol/proj';
import { ElMessage } from 'element-plus';
import { Ref } from 'vue';

// 地图工具类
export class MapUtils {
  private map: Ref<any>;
  private mapContainer: Ref<HTMLElement | null>;
  private mapLoading: Ref<boolean>;
  private mousePosition: Ref<string>;
  private mousePositionVisible: Ref<boolean>;

  constructor(
    map: Ref<any>,
    mapContainer: Ref<HTMLElement | null>,
    mapLoading: Ref<boolean>,
    mousePosition: Ref<string>,
    mousePositionVisible: Ref<boolean>
  ) {
    this.map = map;
    this.mapContainer = mapContainer;
    this.mapLoading = mapLoading;
    this.mousePosition = mousePosition;
    this.mousePositionVisible = mousePositionVisible;
  }

  // 初始化地图
  initMap(): void {
    console.log('initMap 被调用');
    console.log('map.value:', this.map.value);
    console.log('mapContainer.value:', this.mapContainer.value);

    if (this.map.value) {
      console.log('地图已存在，跳过初始化');
      return;
    }

    if (!this.mapContainer.value) {
      console.error('地图容器不存在');
      return;
    }

    try {
      console.log('开始创建地图实例');
      
      this.map.value = new Map({
        target: this.mapContainer.value,
        view: new View({
          center: [108.0, 22.5],
          zoom: 8,
          projection: 'EPSG:4326'
        }),
        controls: []
      });

      // 添加鼠标移动事件监听
      this.map.value.on('pointermove', (event) => {
        const coordinate = event.coordinate;
        if (coordinate) {
          const [lon, lat] = coordinate;
          this.mousePosition.value = `经度: ${lon.toFixed(6)}, 纬度: ${lat.toFixed(6)}`;
          this.mousePositionVisible.value = true;
        }
      });

      // 添加鼠标离开事件监听
      this.map.value.getViewport().addEventListener('mouseleave', () => {
        this.mousePositionVisible.value = false;
      });

      console.log('地图初始化完成');
    } catch (error) {
      console.error('地图初始化失败:', error);
      ElMessage.error('地图初始化失败');
    }
  }

  // 加载WMTS图层
  loadWMTSLayer(layerName: string): void {
    if (!this.map.value || !layerName) {
      console.log('地图或图层名称不存在，跳过图层加载');
      return;
    }

    try {
      this.mapLoading.value = true;
      console.log('开始加载WMTS图层:', layerName);

      // 解析工作空间和图层名
      const parts = layerName.split(':');
      if (parts.length !== 2) {
        throw new Error(`图层ID格式不正确: ${layerName}`);
      }

      const workspace = parts[0];
      const layerNameOnly = parts[1];

      console.log(`创建WMTS图层: ${layerName}, 工作区: ${workspace}, 图层名: ${layerNameOnly}`);

      // 获取Geoserver基础URL - 使用正确的环境变量
      const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
      const port = import.meta.env.VITE_GEOSERVER_PORT || import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
      const geoserverUrl = `http://${host}:${port}/geoserver`;

      console.log(`WMTS服务端点: ${geoserverUrl}/gwc/service/wmts`);

      // 设置默认值
      const tileMatrixSet = 'EPSG:4326';
      const format = 'image/png';
      const style = '';

      // 获取投影
      const projection = getProjection(tileMatrixSet);
      if (!projection) {
        throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
      }

      console.log(`WMTS投影系统:`, projection);

      // 创建WMTS源，使用KVP编码方式
      const source = new WMTS({
        url: `${geoserverUrl}/gwc/service/wmts`,
        layer: layerName,
        matrixSet: tileMatrixSet,
        format: format,
        projection: projection,
        style: style,
        // 关键参数：使用KVP编码而非REST
        requestEncoding: 'KVP',
        // 使用自定义TileGrid
        tileGrid: this.createWmtsTileGrid(projection, tileMatrixSet),
        wrapX: true,
        transition: 0,
        crossOrigin: 'anonymous'
      });

      // 添加事件处理
      source.on('tileloaderror', (event) => {
        console.warn(`WMTS图层 ${layerName} 加载失败:`, event);
      });

      source.on('tileloadend', () => {
        console.log(`WMTS图层 ${layerName} 部分加载成功`);
      });

      // 创建并添加图层
      const tileLayer = new TileLayer({
        preload: Infinity,  // 开启所有级别预加载
        source: source,
        visible: true,
        opacity: 1,
        zIndex: 0
      });

      this.map.value.addLayer(tileLayer);
      console.log('WMTS图层添加成功');

      // 调用API获取图层范围并缩放
      this.fetchLayerExtentAndZoom(workspace, layerNameOnly);

    } catch (error) {
      console.error('WMTS图层加载失败:', error);
      ElMessage.error('图层加载失败');
      this.mapLoading.value = false;
    }
  }

  // 创建WMTS瓦片网格
  private createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
    const projectionExtent = projection.getExtent();

    // 根据不同的坐标系创建合适的参数
    let origin, resolutions, matrixIds;

    if (gridSetId === 'EPSG:4326') {
      // EPSG:4326 特殊处理
      origin = [-180, 90]; // 正确的原点

      // 标准的EPSG:4326分辨率
      resolutions = [];
      matrixIds = [];
      
      // 计算分辨率，从0级到18级
      for (let z = 0; z < 19; z++) {
        resolutions[z] = 0.703125 / Math.pow(2, z);
        matrixIds[z] = `EPSG:4326:${z}`;
      }
    } else {
      // 其他坐标系的通用处理
      const size = Math.sqrt(projectionExtent[2] - projectionExtent[0]) / 256;
      resolutions = new Array(19);
      matrixIds = new Array(19);
      
      for (let z = 0; z < 19; z++) {
        resolutions[z] = size / Math.pow(2, z);
        matrixIds[z] = z.toString();
      }
      
      origin = [projectionExtent[0], projectionExtent[3]];
    }

    console.log(`创建TileGrid - 坐标系: ${gridSetId}, 原点: [${origin[0]}, ${origin[1]}]`);
    console.log(`分辨率数量: ${resolutions.length}, 第一个分辨率: ${resolutions[0]}`);

    return new WMTSTileGrid({
      origin: origin,
      resolutions: resolutions,
      matrixIds: matrixIds
    });
  }

  // 获取图层范围并缩放到合适的位置
  private async fetchLayerExtentAndZoom(workspace: string, layerName: string): Promise<void> {
    try {
      console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);
      const bboxData = await this.getLayerBbox(workspace, layerName);

      // 检查返回数据格式
      if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
        const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;

        // 检查获取的边界框是否有效
        if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
          console.warn('获取到的是全球范围边界框，使用默认范围');
          this.zoomToDefaultExtent();
          return;
        }

        console.log(`图层边界框: [${minx}, ${miny}, ${maxx}, ${maxy}]`);
        
        // 缩放到图层范围
        const extent = [minx, miny, maxx, maxy];
        this.map.value?.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          duration: 1000
        });
        console.log(`已缩放至图层 ${workspace}:${layerName} 的范围`);
      } else {
        throw new Error('API返回的边界框数据格式不正确');
      }
    } catch (error) {
      console.error(`获取图层边界框失败: ${error}`);
      // 使用默认范围
      this.zoomToDefaultExtent();
    } finally {
      this.mapLoading.value = false;
    }
  }

  // 获取图层边界框的API调用
  private async getLayerBbox(workspace: string, layerName: string): Promise<any> {
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${host}:${port}/api/map/layer/bbox`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        workspace: workspace,
        layer: layerName
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    return await response.json();
  }

  // 缩放到默认范围
  private zoomToDefaultExtent(): void {
    if (!this.map.value) return;
    
    try {
      // 使用中国南方的默认范围
      const defaultExtent = [106.0, 20.0, 110.0, 25.0]; // [minx, miny, maxx, maxy]
      
      this.map.value.getView().fit(defaultExtent, {
        padding: [50, 50, 50, 50],
        duration: 1000
      });
      console.log(`已缩放至默认范围`);
    } catch (error) {
      console.error(`缩放至默认范围失败: ${error}`);
    }
  }

  // 清理地图
  cleanupMap(): void {
    if (this.map.value) {
      this.map.value.setTarget(undefined);
      this.map.value = null;
    }
  }
}
