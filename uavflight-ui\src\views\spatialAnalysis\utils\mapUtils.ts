import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import TileWMS from 'ol/source/TileWMS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { ElMessage } from 'element-plus';
import { Ref } from 'vue';

// 地图工具类
export class MapUtils {
  private map: Ref<any>;
  private mapContainer: Ref<HTMLElement | null>;
  private mapLoading: Ref<boolean>;
  private mousePosition: Ref<string>;
  private mousePositionVisible: Ref<boolean>;

  constructor(
    map: Ref<any>,
    mapContainer: Ref<HTMLElement | null>,
    mapLoading: Ref<boolean>,
    mousePosition: Ref<string>,
    mousePositionVisible: Ref<boolean>
  ) {
    this.map = map;
    this.mapContainer = mapContainer;
    this.mapLoading = mapLoading;
    this.mousePosition = mousePosition;
    this.mousePositionVisible = mousePositionVisible;
  }

  // 初始化地图
  initMap(): void {
    console.log('initMap 被调用');
    console.log('map.value:', this.map.value);
    console.log('mapContainer.value:', this.mapContainer.value);

    if (this.map.value) {
      console.log('地图已存在，跳过初始化');
      return;
    }

    if (!this.mapContainer.value) {
      console.error('地图容器不存在');
      return;
    }

    try {
      console.log('开始创建地图实例');
      
      this.map.value = new Map({
        target: this.mapContainer.value,
        view: new View({
          center: [108.0, 22.5],
          zoom: 8,
          projection: 'EPSG:4326'
        }),
        controls: []
      });

      // 添加鼠标移动事件监听
      this.map.value.on('pointermove', (event: any) => {
        const coordinate = event.coordinate;
        if (coordinate) {
          const [lon, lat] = coordinate;
          this.mousePosition.value = `经度: ${lon.toFixed(6)}, 纬度: ${lat.toFixed(6)}`;
          this.mousePositionVisible.value = true;
        }
      });

      // 添加鼠标离开事件监听
      this.map.value.getViewport().addEventListener('mouseleave', () => {
        this.mousePositionVisible.value = false;
      });

      console.log('地图初始化完成');
    } catch (error) {
      console.error('地图初始化失败:', error);
      ElMessage.error('地图初始化失败');
    }
  }

  // 加载WMTS图层
  loadWMTSLayer(layerName: string): void {
    if (!this.map.value || !layerName) {
      console.log('地图或图层名称不存在，跳过图层加载');
      return;
    }

    try {
      this.mapLoading.value = true;
      console.log('开始加载WMTS图层:', layerName);

      // 解析工作空间和图层名
      const parts = layerName.split(':');
      if (parts.length !== 2) {
        throw new Error(`图层ID格式不正确: ${layerName}`);
      }

      const workspace = parts[0];
      const layerNameOnly = parts[1];

      console.log(`创建WMTS图层: ${layerName}, 工作区: ${workspace}, 图层名: ${layerNameOnly}`);

      // 获取Geoserver基础URL - 使用正确的环境变量
      const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
      const port = import.meta.env.VITE_GEOSERVER_PORT || import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
      const geoserverUrl = `http://${host}:${port}/geoserver`;

      console.log(`WMTS服务端点: ${geoserverUrl}/gwc/service/wmts`);

      // 设置默认值
      const tileMatrixSet = 'EPSG:4326';
      const format = 'image/png';
      const style = '';

      // 获取投影
      const projection = getProjection(tileMatrixSet);
      if (!projection) {
        throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
      }

      console.log(`WMTS投影系统:`, projection);

      // 创建WMTS源，使用KVP编码方式
      const source = new WMTS({
        url: `${geoserverUrl}/gwc/service/wmts`,
        layer: layerName,
        matrixSet: tileMatrixSet,
        format: format,
        projection: projection,
        style: style,
        // 关键参数：使用KVP编码而非REST
        requestEncoding: 'KVP',
        // 使用自定义TileGrid
        tileGrid: this.createWmtsTileGrid(projection, tileMatrixSet),
        wrapX: true,
        transition: 0,
        crossOrigin: 'anonymous'
      });

      // 添加事件处理
      source.on('tileloaderror', (event) => {
        console.warn(`WMTS图层 ${layerName} 加载失败:`, event);
      });

      source.on('tileloadend', () => {
        console.log(`WMTS图层 ${layerName} 部分加载成功`);
      });

      // 创建并添加图层
      const tileLayer = new TileLayer({
        preload: Infinity,  // 开启所有级别预加载
        source: source,
        visible: true,
        opacity: 1,
        zIndex: 0  // WMTS作为底图，设置为0（最底层）
      });

      // 保存图层信息到图层实例
      (tileLayer as any).layerId = `${workspace}:${layerNameOnly}`;
      (tileLayer as any).workspace = workspace;
      (tileLayer as any).layerName = layerNameOnly;  // 只保存图层名，不包含工作空间
      (tileLayer as any).displayName = layerNameOnly;  // 用于显示的名称，使用动态获取的名称
      (tileLayer as any).layerType = 'WMTS';

      this.map.value.addLayer(tileLayer);
      console.log('WMTS图层添加成功');

      // 触发图层变化事件
      this.triggerLayersChange();

      // 调用API获取图层范围并缩放
      this.fetchLayerExtentAndZoom(workspace, layerNameOnly);

    } catch (error) {
      console.error('WMTS图层加载失败:', error);
      ElMessage.error('图层加载失败');
      this.mapLoading.value = false;
    }
  }

  // 创建WMTS瓦片网格
  private createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
    const projectionExtent = projection.getExtent();

    // 根据不同的坐标系创建合适的参数
    let origin, resolutions, matrixIds;

    if (gridSetId === 'EPSG:4326') {
      // EPSG:4326 特殊处理
      origin = [-180, 90]; // 正确的原点

      // 标准的EPSG:4326分辨率
      resolutions = [];
      matrixIds = [];
      
      // 计算分辨率，从0级到18级
      for (let z = 0; z < 22; z++) {
        resolutions[z] = 0.703125 / Math.pow(2, z);
        matrixIds[z] = `EPSG:4326:${z}`;
      }
    } else {
      // 其他坐标系的通用处理
      const size = Math.sqrt(projectionExtent[2] - projectionExtent[0]) / 256;
      resolutions = new Array(19);
      matrixIds = new Array(19);
      
      for (let z = 0; z < 19; z++) {
        resolutions[z] = size / Math.pow(2, z);
        matrixIds[z] = z.toString();
      }
      
      origin = [projectionExtent[0], projectionExtent[3]];
    }

    console.log(`创建TileGrid - 坐标系: ${gridSetId}, 原点: [${origin[0]}, ${origin[1]}]`);
    console.log(`分辨率数量: ${resolutions.length}, 第一个分辨率: ${resolutions[0]}`);

    return new WMTSTileGrid({
      origin: origin,
      resolutions: resolutions,
      matrixIds: matrixIds
    });
  }

  // 获取图层范围并缩放到合适的位置
  private async fetchLayerExtentAndZoom(workspace: string, layerName: string): Promise<void> {
    try {
      console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);

      // 使用 geoserver 工具函数
      const { getLayerBbox } = await import('/@/utils/geoserver');
      const bboxData = await getLayerBbox(workspace, layerName);

      // 检查返回数据格式
      if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
        const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;

        // 检查获取的边界框是否有效
        if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
          console.warn('获取到的是全球范围边界框，使用默认范围');
          this.zoomToDefaultExtent();
          return;
        }

        console.log(`图层边界框: [${minx}, ${miny}, ${maxx}, ${maxy}]`);

        // 缩放到图层范围
        const extent = [minx, miny, maxx, maxy];
        this.map.value?.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          duration: 0
        });
        console.log(`已缩放至图层 ${workspace}:${layerName} 的范围`);
      } else {
        throw new Error('API返回的边界框数据格式不正确');
      }
    } catch (error) {
      console.error(`获取图层边界框失败: ${error}`);
      // 使用默认范围
      this.zoomToDefaultExtent();
    } finally {
      this.mapLoading.value = false;
    }
  }

  // 缩放到默认范围
  private zoomToDefaultExtent(): void {
    if (!this.map.value) return;
    
    try {
      // 使用中国南方的默认范围
      const defaultExtent = [106.0, 20.0, 110.0, 25.0]; // [minx, miny, maxx, maxy]
      
      this.map.value.getView().fit(defaultExtent, {
        padding: [50, 50, 50, 50],
        duration: 0
      });
      console.log(`已缩放至默认范围`);
    } catch (error) {
      console.error(`缩放至默认范围失败: ${error}`);
    }
  }

  // 加载WMS图层
  loadWMSLayer(layerConfig: any): void {
    if (!this.map.value) {
      console.error('地图实例不存在，无法加载WMS图层');
      return;
    }

    try {
      this.mapLoading.value = true;
      console.log('开始加载WMS图层:', layerConfig);

      // 验证必要参数
      if (!layerConfig.workspace || !layerConfig.layerName) {
        throw new Error('WMS图层配置缺少workspace或layerName');
      }

      // 构建WMS URL
      const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
      const port = import.meta.env.VITE_GEOSERVER_PORT || import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
      const wmsUrl = `http://${host}:${port}/geoserver/wms`;

      console.log(`WMS服务端点: ${wmsUrl}`);

      // 设置WMS参数
      const wmsParams: any = {
        'LAYERS': `${layerConfig.workspace}:${layerConfig.layerName}`,
        'TILED': true,
        'FORMAT': 'image/png',
        'TRANSPARENT': true,  // 启用透明度，这样可以看到底图
        'VERSION': '1.1.1',
        'SRS': 'EPSG:4326'
      };

      // 设置样式
      if (layerConfig.defaultStyle) {
        if (layerConfig.defaultStyle === 'Random') {
          // 使用随机样式
          const randomStyle = this.getRandomStyle();
          wmsParams.STYLES = randomStyle;
          console.log(`为WMS图层设置随机样式: ${randomStyle}`);
        } else {
          wmsParams.STYLES = layerConfig.defaultStyle;
          console.log(`为WMS图层设置指定样式: ${layerConfig.defaultStyle}`);
        }
      } else {
        // 默认使用随机样式
        const randomStyle = this.getRandomStyle();
        wmsParams.STYLES = randomStyle;
        console.log(`为WMS图层设置默认随机样式: ${randomStyle}`);
      }

      console.log('WMS参数:', wmsParams);

      // 创建WMS源
      const wmsSource = new TileWMS({
        url: wmsUrl,
        params: wmsParams,
        serverType: 'geoserver',
        crossOrigin: 'anonymous',
        transition: 0
      });

      // 添加事件处理
      let loadedTiles = 0;
      let errorTiles = 0;
      let totalTiles = 0;

      wmsSource.on('tileloadstart', (event: any) => {
        totalTiles++;
        console.log(`WMS图层开始加载瓦片 ${totalTiles}:`, event.tile?.src_);
      });

      wmsSource.on('tileloadend', (event: any) => {
        loadedTiles++;
        console.log(`WMS图层瓦片加载成功 ${loadedTiles}/${totalTiles}:`, event.tile?.src_);

        // 当有瓦片成功加载时，结束loading状态
        if (loadedTiles === 1) {
          this.mapLoading.value = false;
          console.log('WMS图层首个瓦片加载完成，结束loading状态');
        }
      });

      wmsSource.on('tileloaderror', (event: any) => {
        errorTiles++;
        console.error(`WMS图层瓦片加载失败 ${errorTiles}/${totalTiles}:`, event.tile?.src_);

        // 如果所有瓦片都失败了，也要结束loading状态
        if (errorTiles + loadedTiles >= totalTiles && totalTiles > 0) {
          this.mapLoading.value = false;
          console.log('WMS图层所有瓦片处理完成，结束loading状态');
        }
      });

      // 创建图层
      const wmsLayer = new TileLayer({
        source: wmsSource,
        visible: true,
        opacity: layerConfig.opacity !== undefined ? layerConfig.opacity : 0.7,  // 默认透明度0.7，可以看到底图
        zIndex: layerConfig.zIndex || 2  // WMS作为叠加层，zIndex为2
      });

      // 保存图层信息到图层实例
      (wmsLayer as any).layerId = layerConfig.id;
      (wmsLayer as any).workspace = layerConfig.workspace;
      (wmsLayer as any).layerName = layerConfig.layerName;
      (wmsLayer as any).displayName = layerConfig.name;  // 用于显示的名称，使用配置中的name
      (wmsLayer as any).layerType = 'WMS';

      // 添加到地图
      this.map.value.addLayer(wmsLayer);
      console.log('WMS图层添加成功，图层ID:', layerConfig.id);
      console.log('当前地图图层数量:', this.map.value.getLayers().getLength());

      // 触发图层变化事件
      this.triggerLayersChange();

      // 设置超时机制，防止一直loading
      setTimeout(() => {
        if (this.mapLoading.value) {
          console.warn('WMS图层加载超时，强制结束loading状态');
          this.mapLoading.value = false;
        }
      }, 10000); // 10秒超时

      // 临时：立即结束loading状态用于调试
      setTimeout(() => {
        console.log('调试：3秒后强制结束loading状态');
        this.mapLoading.value = false;
      }, 3000);

      // 暂时不获取图层范围，避免影响图层显示
      // this.fetchLayerExtentAndZoom(layerConfig.workspace, layerConfig.layerName);

    } catch (error) {
      console.error('WMS图层加载失败:', error);
      ElMessage.error('WMS图层加载失败');
      this.mapLoading.value = false;
    }
  }

  // 获取随机样式
  private getRandomStyle(): string {
    const styleNames = [
      "Blue", "Brown", "Cyan", "Gold", "Gray", "Green", "Lime",
      "Magenta", "Navy", "Olive", "Orange", "Pink", "Purple",
      "Red", "Teal", "White", "Yellow", "Coral", "Tomato"
    ];
    return styleNames[Math.floor(Math.random() * styleNames.length)];
  }

  // 手动停止loading状态
  stopLoading(): void {
    console.log('手动停止loading状态');
    this.mapLoading.value = false;
  }

  // 移除指定的图层
  removeLayer(layerId: string): void {
    if (!this.map.value) {
      console.error('地图实例不存在，无法移除图层');
      return;
    }

    const layers = this.map.value.getLayers().getArray();
    const layerToRemove = layers.find((layer: any) => (layer as any).layerId === layerId);

    if (layerToRemove) {
      this.map.value.removeLayer(layerToRemove);
      console.log(`图层 ${layerId} 已移除`);

      // 触发图层变化事件
      this.triggerLayersChange();
    } else {
      console.warn(`未找到图层 ${layerId}`);
    }
  }

  // 批量移除图层
  removeLayers(layerIds: string[]): void {
    layerIds.forEach(layerId => {
      this.removeLayer(layerId);
    });
  }

  // 获取图层列表信息
  getLayersInfo(): any[] {
    if (!this.map.value) {
      return [];
    }

    const layers = this.map.value.getLayers().getArray();
    return layers.map((layer: any, index: number) => {
      const source = layer.getSource();
      const zIndex = layer.getZIndex();
      const visible = layer.getVisible();
      const opacity = layer.getOpacity();

      let layerType = 'Unknown';
      let layerName = '';
      let layerId = '';

      if (source instanceof WMTS) {
        layerType = 'WMTS';
        // 优先使用保存的displayName，然后是layerName，最后从source中提取
        if ((layer as any).displayName) {
          layerName = (layer as any).displayName;
        } else if ((layer as any).layerName) {
          layerName = (layer as any).layerName;
        } else {
          const fullLayerName = source.getLayer() || 'WMTS图层';
          // 提取图层名，去掉工作空间前缀
          layerName = fullLayerName.includes(':') ? fullLayerName.split(':')[1] : fullLayerName;
        }
        layerId = (layer as any).layerId || `wmts_${index}`;
      } else if (source instanceof TileWMS) {
        layerType = 'WMS';
        // 优先使用保存的displayName，然后是layerName，最后从参数中提取
        if ((layer as any).displayName) {
          layerName = (layer as any).displayName;
        } else if ((layer as any).layerName) {
          layerName = (layer as any).layerName;
        } else {
          const params = source.getParams();
          const fullLayerName = params.LAYERS || 'WMS图层';
          // 提取图层名，去掉工作空间前缀
          layerName = fullLayerName.includes(':') ? fullLayerName.split(':')[1] : fullLayerName;
        }
        layerId = (layer as any).layerId || `wms_${index}`;
      }

      return {
        id: layerId,
        name: layerName,
        type: layerType,
        visible: visible,
        opacity: opacity,
        zIndex: zIndex,
        layer: layer
      };
    }).sort((a: any, b: any) => (b.zIndex || 0) - (a.zIndex || 0)); // 按zIndex降序排列
  }

  // 列出当前所有图层
  listLayers(): void {
    const layersInfo = this.getLayersInfo();
    console.log(`当前地图共有 ${layersInfo.length} 个图层:`);

    layersInfo.forEach((info, index) => {
      console.log(`  ${index + 1}. ${info.type} - ${info.name}`);
      console.log(`     zIndex: ${info.zIndex}, visible: ${info.visible}, opacity: ${info.opacity}`);
    });
  }

  // 切换图层可见性
  toggleLayerVisibility(layerId: string): void {
    const layersInfo = this.getLayersInfo();
    const layerInfo = layersInfo.find(info => info.id === layerId);

    if (layerInfo) {
      const newVisible = !layerInfo.visible;
      layerInfo.layer.setVisible(newVisible);
      console.log(`图层 ${layerInfo.name} 可见性切换为: ${newVisible}`);

      // 触发图层变化事件
      this.triggerLayersChange();
    }
  }

  // 设置图层zIndex
  setLayerZIndex(layerId: string, zIndex: number): void {
    const layersInfo = this.getLayersInfo();
    const layerInfo = layersInfo.find(info => info.id === layerId);

    if (layerInfo) {
      layerInfo.layer.setZIndex(zIndex);
      console.log(`图层 ${layerInfo.name} zIndex设置为: ${zIndex}`);
    }
  }

  // 批量更新图层zIndex（用于拖拽排序）
  updateLayersOrder(orderedLayerIds: string[]): void {
    orderedLayerIds.forEach((layerId, index) => {
      // zIndex从高到低：第一个图层zIndex最高
      const zIndex = orderedLayerIds.length - index;
      this.setLayerZIndex(layerId, zIndex);
    });

    // 触发图层变化事件
    this.triggerLayersChange();
  }

  // 图层变化回调函数
  private layersChangeCallback: (() => void) | null = null;

  // 设置图层变化监听器
  setLayersChangeListener(callback: () => void): void {
    this.layersChangeCallback = callback;
  }

  // 触发图层变化事件
  private triggerLayersChange(): void {
    if (this.layersChangeCallback) {
      this.layersChangeCallback();
    }
  }

  // 调整地图大小
  resizeMap(): void {
    if (this.map.value) {
      this.map.value.updateSize();
    }
  }

  // 清理地图
  cleanupMap(): void {
    if (this.map.value) {
      this.map.value.setTarget(undefined);
      this.map.value = null;
    }
  }
}
