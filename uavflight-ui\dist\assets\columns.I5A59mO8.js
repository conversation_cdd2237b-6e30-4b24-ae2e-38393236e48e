const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.B2FR0lVF.js","assets/index.BSP3cg_z.js","assets/vue.CnN__PXn.js","assets/index.DjeikzAe.css","assets/header.CDsBv0mY.js","assets/main.DP48xYwf.js","assets/columnsAside.BoGs_P1R.js","assets/columnsAside.DWQSN07A.css"])))=>i.map(i=>d[i]);
import{u as A,a as e,__tla as P}from"./index.BSP3cg_z.js";import{d as y,k as p,l as D,s as I,o as L,w as f,B as R,e as O,b as V,v as o,t,u as l,j as r,y as M}from"./vue.CnN__PXn.js";let m,S=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{let n;n=y({name:"layoutColumns"}),m=y({...n,setup(j){const h=r(()=>e(()=>import("./aside.B2FR0lVF.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),w=r(()=>e(()=>import("./header.CDsBv0mY.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([4,1,2,3]))),d=r(()=>e(()=>import("./main.DP48xYwf.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([5,1,2,3]))),E=r(()=>e(()=>import("./columnsAside.BoGs_P1R.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([6,1,2,3,7]))),s=p(""),_=p(),T=D(),b=A(),{themeConfig:v}=I(b),u=()=>{s.value.update(),_.value.layoutMainScrollbarRef.update()},c=()=>{M(()=>{setTimeout(()=>{u(),s.value.wrapRef.scrollTop=0,_.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return L(()=>{c()}),f(()=>T.path,()=>{c()}),f(v,()=>{u()},{deep:!0}),(a,C)=>{const k=R("el-scrollbar"),i=R("el-container");return V(),O(i,{class:"layout-container"},{default:o(()=>[t(l(E)),t(i,{class:"layout-columns-warp layout-container-view h100"},{default:o(()=>[t(l(h)),t(k,{ref_key:"layoutScrollbarRef",ref:s,class:"layout-backtop"},{default:o(()=>[t(l(w)),t(l(d),{ref_key:"layoutMainRef",ref:_},null,512)]),_:1},512)]),_:1})]),_:1})}}})});export{S as __tla,m as default};
