import{i as S}from"./echarts.DrVj8Jfx.js";import{B as Me,C as $e,D as je,F as He,G as Re,H as Ie,q as Ve,__tla as We}from"./index.BSP3cg_z.js";import{d as Ee,k as s,z as Pe,o as Ue,B as _,m as Xe,a as b,b as h,f as e,t as o,v as y,E as Je,F as ze,p as Ke,u as g,G as c,q as Le,D as Qe,g as De}from"./vue.CnN__PXn.js";import{b as Ye,__tla as Ze}from"./notice.PW6bF9Uh.js";import{g as ea,a as aa,b as ta,c as la,d as sa,__tla as ia}from"./bigScreen.DZ-cFbL_.js";let Fe,oa=Promise.all([(()=>{try{return We}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return ia}catch{}})()]).then(async()=>{let D,F,N,O,q,B,G,M,$,j,H,R,I,V,W,E,P,U,X,J,K,Q,Y,Z,ee,ae,te,le,se,ie,oe,ce,re,ne,de,ve,ye,ue,me,ge,pe,be,he,fe,xe,Se,_e;D={class:"home-container"},F={class:"notice-section"},N={class:"notice-header"},O={class:"notice-list"},q={class:"notice-left"},B=["onClick"],G={class:"notice-right"},M={class:"notice-time"},$={class:"notice-date"},j={key:1,class:"empty-notice"},H={class:"data-cards"},R={class:"data-card"},I={class:"icon-box blue"},V={class:"data-info"},W={class:"value"},E={class:"data-card"},P={class:"icon-box cyan"},U={class:"data-info"},X={class:"value"},J={class:"data-card"},K={class:"icon-box red"},Q={class:"data-info"},Y={class:"value"},Z={class:"data-card"},ee={class:"icon-box green"},ae={class:"data-info"},te={class:"value"},le={class:"data-card"},se={class:"icon-box orange"},ie={class:"data-info"},oe={class:"value"},ce={class:"data-card"},re={class:"icon-box purple"},ne={class:"data-info"},de={class:"value"},ve={class:"bottom-section"},ye={class:"visit-trend"},ue={class:"charts-section"},me={class:"chart-card"},ge={class:"chart-card"},pe={class:"notice-detail-header"},be={class:"notice-detail-title"},he={class:"notice-meta"},fe={class:"notice-type"},xe={class:"notice-time"},Se={class:"notice-detail-content"},_e=Ee({__name:"index",setup(ca){s(new Date),Pe();const Ne=t=>{if(!t)return"";try{const a=new Date,r=new Date(t);if(isNaN(r.getTime()))return t;const i=Math.floor((a.getTime()-r.getTime())/1e3);return i<60?`${i}\u79D2\u524D`:i<3600?`${Math.floor(i/60)}\u5206\u949F\u524D`:i<86400?`${Math.floor(i/3600)}\u5C0F\u65F6\u524D`:`${Math.floor(i/86400)}\u5929\u524D`}catch{return t}},we=s(),Ae=s(),w=s(),p=s([]),A=s(!1),C=s(!1),m=s(null),Ce=s("\u672C\u5E74"),T=s([]),k=s(!0),Te=s("\u672C\u5E74"),z=s(!0),v=s({resList:[],cityNames:[]}),u=s({zlll:0,yhs:0,sjsl:0,yxts:0,qqcs:0,fjsl:0}),f=s({dailyDateArray:[],dailyCountArray:[]}),L=s({dateArray:[],countArray:[]});Ue(()=>{A.value=!0,Ye({}).then(t=>{t&&t.code===0&&Array.isArray(t.data)?p.value=t.data.map(a=>{let r="",i="",l="",d="";a.type==="1"||a.type===1?(d="important",i="\u4E0A\u7EA7\u901A\u77E5"):(d="info",i="\u7CFB\u7EDF\u901A\u77E5"),a.level==="1"||a.level===1?(r="info",l="\u901A\u77E5"):a.level!=="2"&&a.level!==2||(r="important",l="\u91CD\u8981");const x=a.createTime||new Date().toISOString();return{id:a.id||Math.random().toString(36).substring(2),levelType:r,noticeType:i,noticeTypeClass:d,tag:l,title:a.title||"",content:a.context||"",time:a.time||"",createTime:x}}).slice(0,5):p.value=[],A.value=!1}).catch(t=>{p.value=[],A.value=!1}),(async()=>{k.value=!0;let t="year";Ce.value==="\u672C\u65E5"?t="today":Ce.value==="\u672C\u6708"&&(t="month");try{const a=await la({date:t});T.value=a.data,Oe()}catch{T.value=[]}finally{k.value=!1}})(),(async()=>{z.value=!0;let t="year";Te.value==="\u672C\u65E5"?t="today":Te.value==="\u672C\u6708"&&(t="month");try{const a=await sa({date:t});v.value=a.data,qe()}catch{v.value={resList:[],cityNames:[]}}finally{z.value=!1}})(),(async()=>{try{const t=await ea();u.value=t.data}catch{}})(),(async()=>{try{const t=await aa();f.value=t.data,ke()}catch{Be()}})(),(async()=>{try{const t=await ta();L.value=t.data,ke()}catch{}})()});const Oe=()=>{S(we.value).setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"horizontal",bottom:"bottom",textStyle:{color:"#303133"},formatter:function(t){return t},tooltip:{show:!0,formatter:function(t){return t.name}}},series:[{name:"\u4E8B\u4EF6\u7C7B\u578B",type:"pie",radius:["30%","60%"],center:["50%","40%"],avoidLabelOverlap:!0,itemStyle:{borderRadius:10,color:function(t){const a=["rgba(16, 116, 41, 1)","rgba(16, 116, 41, 0.9)","rgba(16, 116, 41, 0.8)","rgba(16, 116, 41, 0.7)","rgba(16, 116, 41, 0.6)","rgba(67, 160, 71, 1)","rgba(102, 187, 106, 1)","rgba(129, 199, 132, 1)","rgba(165, 214, 167, 1)","rgba(200, 230, 201, 1)"];return a[t.dataIndex%a.length]}},label:{show:!0,position:"outside",formatter:"{b}: {c} ({d}%)",fontSize:12,lineHeight:15,rich:{b:{fontSize:12,lineHeight:15,fontWeight:"bold"}},padding:[0,0,0,0],align:"center"},labelLine:{show:!0,length:15,length2:10},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},label:{show:!0}},data:T.value}]})},qe=()=>{const t=S(Ae.value),a=["#ff5757","#37A2FF","#67C23A","#E6A23C","#8B5CF6","#19D3DB","#F56C6C","#909399","#00CC99","#FF9900"];let r=[],i=[];v.value&&v.value.cityNames&&v.value.cityNames.length>0&&(i=v.value.cityNames.slice(-7).reverse().map(l=>{const d=l.match(/^\d+\s+(.+)/);return d?d[1]:l}),v.value.resList&&v.value.resList.length>0&&(r=v.value.resList.map((l,d)=>({name:l.name,type:"bar",data:l.data.slice(-7).reverse(),stack:"total",itemStyle:{color:a[d%a.length]},emphasis:{focus:"series"},barWidth:"40%"})))),t.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:r.map(l=>l.name),textStyle:{color:"#303133"},orient:"horizontal",bottom:0,itemGap:10,itemWidth:15},grid:{left:"1%",right:"1%",top:"5%",bottom:"15%",containLabel:!0},xAxis:{type:"category",data:i,axisLabel:{rotate:0,interval:0,formatter:function(l){return l},textStyle:{fontSize:12},rich:{value:{lineHeight:20}}}},yAxis:{type:"value"},series:r.length>0?r:[{name:"\u6682\u65E0\u6570\u636E",type:"bar",data:[],itemStyle:{color:"rgba(16, 116, 41, 0.5)"}}]}),setTimeout(()=>{t.resize()},100)},ke=()=>{const t=S(w.value),a=[...new Set([...f.value.dailyDateArray,...L.value.dateArray])].sort();t.setOption({tooltip:{trigger:"axis"},legend:{data:["\u4E8B\u4EF6\u6570","\u53D1\u5E03\u670D\u52A1\u6570"],right:"10%",top:"5%",textStyle:{color:"#666",fontSize:12}},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:a.length>0?a:f.value.dailyDateArray,axisLabel:{rotate:45}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}}},series:[{name:"\u4E8B\u4EF6\u6570",type:"line",smooth:!0,data:f.value.dailyCountArray,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(16, 116, 41, 0.2)"},{offset:1,color:"rgba(16, 116, 41, 0)"}]}},itemStyle:{color:"rgba(16, 116, 41, 1)"},lineStyle:{width:2,color:"rgba(16, 116, 41, 1)"},symbol:"circle",symbolSize:6},{name:"\u53D1\u5E03\u670D\u52A1\u6570",type:"line",smooth:!0,data:L.value.countArray,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.2)"},{offset:1,color:"rgba(24, 144, 255, 0)"}]}},itemStyle:{color:"rgba(24, 144, 255, 1)"},lineStyle:{width:2,color:"rgba(24, 144, 255, 1)"},symbol:"circle",symbolSize:6}]})},Be=()=>{S(w.value).setOption({tooltip:{trigger:"axis"},legend:{data:["\u4E8B\u4EF6\u6570","\u53D1\u5E03\u670D\u52A1\u6570"],right:"10%",top:"5%",textStyle:{color:"#666",fontSize:12}},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["2025-06-03","2025-06-02","2025-06-01","2025-05-31","2025-05-30","2025-05-29","2025-05-28","2025-05-27","2025-05-26","2025-05-25","2025-05-24","2025-05-23","2025-05-22","2025-05-21","2025-05-20"],axisLabel:{rotate:45}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}}},series:[{name:"\u4E8B\u4EF6\u6570",type:"line",smooth:!0,data:[10,15,12,14,16,18,20,450,380,220,70,80,85,50,45],areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(16, 116, 41, 0.2)"},{offset:1,color:"rgba(16, 116, 41, 0)"}]}},itemStyle:{color:"rgba(16, 116, 41, 1)"},lineStyle:{width:2,color:"rgba(16, 116, 41, 1)"},symbol:"circle",symbolSize:6},{name:"\u53D1\u5E03\u670D\u52A1\u6570",type:"line",smooth:!0,data:[5,8,6,7,9,11,13,25,22,18,12,14,16,10,8],areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.2)"},{offset:1,color:"rgba(24, 144, 255, 0)"}]}},itemStyle:{color:"rgba(24, 144, 255, 1)"},lineStyle:{width:2,color:"rgba(24, 144, 255, 1)"},symbol:"circle",symbolSize:6}]})};return(t,a)=>{const r=_("el-link"),i=_("el-empty"),l=_("el-icon"),d=_("el-dialog"),x=Xe("loading");return h(),b("div",D,[e("div",F,[e("div",N,[a[3]||(a[3]=e("div",{class:"tabs"},[e("span",{class:"tab-item active"}," \u901A\u77E5 ")],-1)),o(r,{type:"primary",class:"more",onClick:a[0]||(a[0]=n=>t.$router.push("/admin/notice/index"))},{default:y(()=>a[2]||(a[2]=[Je("\u66F4\u591A")])),_:1})]),e("div",O,[p.value.length>0?(h(!0),b(ze,{key:0},Ke(p.value,n=>(h(),b("div",{key:n.id,class:"notice-item"},[e("div",q,[e("div",{class:De(["notice-tag",n.noticeTypeClass])},c(n.noticeType),3),e("div",{class:De(["notice-tag",n.levelType])},c(n.tag),3),e("div",{class:"notice-content",onClick:ra=>(Ge=>{m.value=Ge,C.value=!0})(n)},c(n.title||n.content),9,B)]),e("div",G,[e("div",M,c(Ne(n.createTime)),1),e("div",$,c(n.createTime),1)])]))),128)):(h(),b("div",j,[o(i,{description:"\u6682\u65E0\u901A\u77E5"})]))])]),e("div",H,[e("div",R,[e("div",I,[o(l,null,{default:y(()=>[o(g(Me))]),_:1})]),e("div",V,[e("div",W,c(u.value.zlll),1),a[4]||(a[4]=e("div",{class:"label"},"\u603B\u6D4F\u89C8\u91CF",-1))])]),e("div",E,[e("div",P,[o(l,null,{default:y(()=>[o(g($e))]),_:1})]),e("div",U,[e("div",X,c(u.value.yhs),1),a[5]||(a[5]=e("div",{class:"label"},"\u7528\u6237\u6570",-1))])]),e("div",J,[e("div",K,[o(l,null,{default:y(()=>[o(g(je))]),_:1})]),e("div",Q,[e("div",Y,c(u.value.sjsl),1),a[6]||(a[6]=e("div",{class:"label"},"\u4E8B\u4EF6\u6570\u91CF",-1))])]),e("div",Z,[e("div",ee,[o(l,null,{default:y(()=>[o(g(He))]),_:1})]),e("div",ae,[e("div",te,c(u.value.yxts),1),a[7]||(a[7]=e("div",{class:"label"},"\u8FD0\u884C\u5929\u6570",-1))])]),e("div",le,[e("div",se,[o(l,null,{default:y(()=>[o(g(Re))]),_:1})]),e("div",ie,[e("div",oe,c(u.value.qqcs),1),a[8]||(a[8]=e("div",{class:"label"},"\u8BF7\u6C42\u6B21\u6570",-1))])]),e("div",ce,[e("div",re,[o(l,null,{default:y(()=>[o(g(Ie))]),_:1})]),e("div",ne,[e("div",de,c(u.value.fjsl),1),a[9]||(a[9]=e("div",{class:"label"},"\u9644\u4EF6\u6570\u91CF",-1))])])]),e("div",ve,[e("div",ye,[a[10]||(a[10]=e("div",{class:"card-header"},[e("span",{class:"title"},"\u6BCF\u5929\u4E8B\u4EF6\u6570\u91CF\u548C\u53D1\u5E03\u670D\u52A1\u6570")],-1)),e("div",{class:"chart-content",ref_key:"visitTrendChartRef",ref:w},null,512)])]),e("div",ue,[e("div",me,[a[11]||(a[11]=e("div",{class:"card-header"},[e("span",{class:"title"},"\u4E8B\u4EF6\u7C7B\u578B")],-1)),Le(e("div",{class:"chart-content",ref_key:"trendChartRef",ref:we,"element-loading-text":"\u52A0\u8F7D\u4E2D...","element-loading-background":"rgba(255, 255, 255, 0.5)"},null,512),[[x,k.value]])]),e("div",ge,[a[12]||(a[12]=e("div",{class:"card-header"},[e("span",{class:"title"},"\u4E8B\u4EF6\u6570\u91CF")],-1)),Le(e("div",{class:"chart-content",ref_key:"barChartRef",ref:Ae,"element-loading-text":"\u52A0\u8F7D\u4E2D...","element-loading-background":"rgba(255, 255, 255, 0.5)"},null,512),[[x,z.value]])])]),o(d,{modelValue:C.value,"onUpdate:modelValue":a[1]||(a[1]=n=>C.value=n),title:"\u901A\u77E5\u8BE6\u60C5",width:"600px","close-on-click-modal":!0,"show-close":!0},{default:y(()=>[m.value?(h(),b(ze,{key:0},[e("div",pe,[e("div",be,c(m.value.title||"\u901A\u77E5"),1),e("div",he,[e("span",fe,c(m.value.noticeType),1),e("span",xe,c(m.value.createTime),1)])]),e("div",Se,c(m.value.content),1)],64)):Qe("",!0)]),_:1},8,["modelValue"])])}}}),Fe=Ve(_e,[["__scopeId","data-v-1e3a1fb4"]])});export{oa as __tla,Fe as default};
