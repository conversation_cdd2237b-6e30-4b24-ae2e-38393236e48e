import{s as a,__tla as p}from"./index.BSP3cg_z.js";let e,d,m,r,l,o=Promise.all([(()=>{try{return p}catch{}})()]).then(async()=>{m=t=>a({url:"/admin/dept/tree",method:"get",params:t}),e=t=>a({url:"/admin/dept",method:"post",data:t}),r=t=>a({url:"/admin/dept/"+t,method:"get"}),d=t=>a({url:"/admin/dept/"+t,method:"delete"}),l=t=>a({url:"/admin/dept",method:"put",data:t})});export{o as __tla,e as a,d as b,m as d,r as g,l as p};
