import{aa as ge,a3 as ye,E as k,a8 as we,q as be,__tla as ke}from"./index.BSP3cg_z.js";import{d as $e,k as f,w as Z,c as O,o as Ce,B as d,m as Ve,e as x,b as z,v as l,f as v,t,u as ee,E as b,G as I,q as Se,D as R,a as Le}from"./vue.CnN__PXn.js";let ae,ze=Promise.all([(()=>{try{return ke}catch{}})()]).then(async()=>{let P,q,M,A,F,G,H,J,N,Y;P={class:"batch-download-content"},q={class:"search-section"},M={class:"statistics-section"},A={class:"selection-info"},F={class:"info-text"},G={class:"task-list-section"},H={class:"download-files"},J={key:2,class:"no-files"},N={class:"pagination-section"},Y={class:"dialog-footer"},ae=be($e({__name:"BatchDownloadDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(te,{emit:le}){const se=te,oe=le,$=f(!1),D=f(!1),T=f([]),p=f([]),C=f(""),V=f(""),E=f("desc"),m=f(1),S=f(20);Z(()=>se.modelValue,a=>{$.value=a,a&&W()}),Z(()=>$.value,a=>{oe("update:modelValue",a)});const ne=O(()=>T.value.filter(a=>L(a)&&a.status==="\u5B8C\u6210").length),K=O(()=>{let a=T.value;if(a=a.filter(e=>e.status==="\u5B8C\u6210"&&L(e)),C.value){const e=C.value.toLowerCase();a=a.filter(r=>r.name.toLowerCase().includes(e)||r.task_id.toLowerCase().includes(e))}return V.value&&(a=a.filter(e=>e.analysis_category===V.value)),a.sort((e,r)=>E.value==="desc"?r.timestamp-e.timestamp:e.timestamp-r.timestamp),a}),ue=O(()=>{const a=(m.value-1)*S.value,e=a+S.value;return K.value.slice(a,e)}),Q=()=>{$.value=!1,p.value=[],C.value="",V.value="",E.value="desc"},ie=()=>{m.value=1},de=()=>{m.value=1},re=()=>{m.value=1},ce=a=>{p.value=a},pe=a=>{S.value=a,m.value=1},_e=a=>{m.value=a},fe=a=>L(a),L=a=>a.output_files&&(a.output_files.ai_output_path||a.output_files.final_output_path),W=async()=>{D.value=!0;try{const a="**************",e="8091",r=`http://${a}:${e}/api/map/odm/tasks`,o=await fetch(r);if(!o.ok)throw new Error(`\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25: ${o.status}`);const c=await o.json();if(c.status!=="success"||!c.tasks)throw new Error("\u4E3B\u4EFB\u52A1\u5217\u8868\u6570\u636E\u683C\u5F0F\u9519\u8BEF");const h=[];for(const u of c.tasks)try{const g=`http://${a}:${e}/api/analysis/taskinfo/?id=${u.task_id}`,_=await fetch(g);if(_.ok){const i=await _.json();i.status==="success"&&i.data&&i.data.forEach(n=>{const j={arableLand:"\u8015\u5730\u5206\u6790",constructionLand:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790"}[n.analysis_category]||"\u5206\u6790",y=new Date(1e3*n.timestamp),B=y.getFullYear().toString()+(y.getMonth()+1).toString().padStart(2,"0")+y.getDate().toString().padStart(2,"0")+y.getHours().toString().padStart(2,"0")+y.getMinutes().toString().padStart(2,"0");h.push({task_id:n.task_id,name:`${j}_${B}`,status:n.status,createTime:n.datetime,analysis_category:n.analysis_category,timestamp:n.timestamp,output_files:n.output_files,parent_task_id:u.task_id})})}}catch{}T.value=h}catch(a){k.error(`\u52A0\u8F7D\u4EFB\u52A1\u6570\u636E\u5931\u8D25: ${a instanceof Error?a.message:"\u672A\u77E5\u9519\u8BEF"}`)}finally{D.value=!1}},ve=()=>{W()},me=async()=>{if(p.value.length!==0)try{await we.confirm(`\u786E\u5B9A\u8981\u4E0B\u8F7D\u9009\u4E2D\u7684 ${p.value.length} \u4E2A\u4EFB\u52A1\u5417\uFF1F`,"\u786E\u8BA4\u6279\u91CF\u4E0B\u8F7D",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"info"}),await X(p.value)}catch{}else k.warning("\u8BF7\u5148\u9009\u62E9\u8981\u4E0B\u8F7D\u7684\u4EFB\u52A1")},X=async a=>{try{const e=[];if(a.forEach(_=>{var i,n;(i=_.output_files)!=null&&i.ai_output_path&&e.push(_.output_files.ai_output_path),(n=_.output_files)!=null&&n.final_output_path&&e.push(_.output_files.final_output_path)}),e.length===0)return void k.error("\u6CA1\u6709\u627E\u5230\u53EF\u4E0B\u8F7D\u7684\u6587\u4EF6");k.info(`\u6B63\u5728\u51C6\u5907\u4E0B\u8F7D ${a.length} \u4E2A\u4EFB\u52A1\u7684\u6587\u4EF6...`);const r="http://**************:8091/api/analysis/download-data/",o=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_paths:e})});if(!o.ok)throw new Error(`\u4E0B\u8F7D\u8BF7\u6C42\u5931\u8D25: ${o.status} ${o.statusText}`);const c=await o.blob(),h=window.URL.createObjectURL(c),u=document.createElement("a");u.href=h;const g=a.length===1?`analysis_data_${a[0].task_id}_${new Date().getTime()}.zip`:`batch_analysis_data_${a.length}tasks_${new Date().getTime()}.zip`;u.download=g,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(h),k.success(`\u6210\u529F\u4E0B\u8F7D ${a.length} \u4E2A\u4EFB\u52A1\u7684\u6587\u4EF6`)}catch(e){k.error(`\u4E0B\u8F7D\u5931\u8D25: ${e instanceof Error?e.message:"\u672A\u77E5\u9519\u8BEF"}`)}};return Ce(()=>{}),(a,e)=>{const r=d("el-input"),o=d("el-col"),c=d("el-option"),h=d("el-select"),u=d("el-button"),g=d("el-row"),_=d("el-statistic"),i=d("el-table-column"),n=d("el-tag"),j=d("el-table"),y=d("el-pagination"),B=d("el-dialog"),he=Ve("loading");return z(),x(B,{modelValue:$.value,"onUpdate:modelValue":e[5]||(e[5]=s=>$.value=s),title:"\u6279\u91CF\u4E0B\u8F7D",width:"80%","close-on-click-modal":!1,onClose:Q},{footer:l(()=>[v("div",Y,[t(u,{onClick:Q},{default:l(()=>e[10]||(e[10]=[b("\u5173\u95ED")])),_:1}),t(u,{type:"primary",disabled:p.value.length===0,onClick:me},{default:l(()=>[b(" \u6279\u91CF\u4E0B\u8F7D ("+I(p.value.length)+") ",1)]),_:1},8,["disabled"])])]),default:l(()=>[v("div",P,[v("div",q,[t(g,{gutter:16},{default:l(()=>[t(o,{span:8},{default:l(()=>[t(r,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=s=>C.value=s),placeholder:"\u641C\u7D22\u4EFB\u52A1\u540D\u79F0\u6216ID","prefix-icon":ee(ge),clearable:"",onInput:ie},null,8,["modelValue","prefix-icon"])]),_:1}),t(o,{span:6},{default:l(()=>[t(h,{modelValue:V.value,"onUpdate:modelValue":e[1]||(e[1]=s=>V.value=s),placeholder:"\u7B5B\u9009\u5206\u6790\u7C7B\u578B",clearable:"",onChange:de},{default:l(()=>[t(c,{label:"\u5168\u90E8",value:""}),t(c,{label:"\u8015\u5730\u5206\u6790",value:"arableLand"}),t(c,{label:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790",value:"constructionLand"})]),_:1},8,["modelValue"])]),_:1}),t(o,{span:6},{default:l(()=>[t(h,{modelValue:E.value,"onUpdate:modelValue":e[2]||(e[2]=s=>E.value=s),placeholder:"\u65F6\u95F4\u6392\u5E8F",onChange:re},{default:l(()=>[t(c,{label:"\u6700\u65B0\u5728\u524D",value:"desc"}),t(c,{label:"\u6700\u65E7\u5728\u524D",value:"asc"})]),_:1},8,["modelValue"])]),_:1}),t(o,{span:4},{default:l(()=>[t(u,{type:"primary",icon:ee(ye),onClick:ve},{default:l(()=>e[6]||(e[6]=[b("\u5237\u65B0")])),_:1},8,["icon"])]),_:1})]),_:1})]),v("div",M,[t(g,{gutter:16},{default:l(()=>[t(o,{span:8},{default:l(()=>[t(_,{title:"\u53EF\u4E0B\u8F7D\u4EFB\u52A1\u6570",value:ne.value},null,8,["value"])]),_:1}),t(o,{span:8},{default:l(()=>[t(_,{title:"\u5DF2\u9009\u62E9",value:p.value.length},null,8,["value"])]),_:1}),t(o,{span:8},{default:l(()=>[v("div",A,[v("span",F,I(p.value.length>0?`\u5DF2\u9009\u62E9 ${p.value.length} \u4E2A\u4EFB\u52A1`:"\u8BF7\u9009\u62E9\u8981\u4E0B\u8F7D\u7684\u4EFB\u52A1"),1)])]),_:1})]),_:1})]),v("div",G,[Se((z(),x(j,{data:ue.value,onSelectionChange:ce,"max-height":"350",stripe:""},{default:l(()=>[t(i,{type:"selection",width:"55",selectable:fe}),t(i,{prop:"name",label:"\u4EFB\u52A1\u540D\u79F0","min-width":"250"}),t(i,{prop:"analysis_category",label:"\u5206\u6790\u7C7B\u578B",width:"120"},{default:l(({row:s})=>{return[b(I((w=s.analysis_category,{arableLand:"\u8015\u5730\u5206\u6790",constructionLand:"\u5EFA\u8BBE\u7528\u5730\u5206\u6790"}[w]||w)),1)];var w}),_:1}),t(i,{prop:"createTime",label:"\u521B\u5EFA\u65F6\u95F4",width:"180"}),t(i,{label:"\u53EF\u4E0B\u8F7D\u6587\u4EF6","min-width":"200"},{default:l(({row:s})=>{var w,U;return[v("div",H,[(w=s.output_files)!=null&&w.ai_output_path?(z(),x(n,{key:0,size:"small",type:"info",class:"file-tag"},{default:l(()=>e[7]||(e[7]=[b(" AI\u5206\u6790\u7ED3\u679C ")])),_:1})):R("",!0),(U=s.output_files)!=null&&U.final_output_path?(z(),x(n,{key:1,size:"small",type:"success",class:"file-tag"},{default:l(()=>e[8]||(e[8]=[b(" \u6700\u7EC8\u7ED3\u679C ")])),_:1})):R("",!0),L(s)?R("",!0):(z(),Le("span",J,"\u65E0\u53EF\u4E0B\u8F7D\u6587\u4EF6"))])]}),_:1}),t(i,{label:"\u64CD\u4F5C",width:"100"},{default:l(({row:s})=>[t(u,{size:"small",type:"primary",disabled:!L(s),onClick:w=>(async U=>{await X([U])})(s)},{default:l(()=>e[9]||(e[9]=[b(" \u4E0B\u8F7D ")])),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])),[[he,D.value]]),v("div",N,[t(y,{"current-page":m.value,"onUpdate:currentPage":e[3]||(e[3]=s=>m.value=s),"page-size":S.value,"onUpdate:pageSize":e[4]||(e[4]=s=>S.value=s),"page-sizes":[10,20,50,100],total:K.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:_e},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-d870b5af"]])});export{ze as __tla,ae as default};
