import{s as n,__tla as h}from"./index.BSP3cg_z.js";let r,i,l,u,d,o,c,p=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{l=function(t){return n({url:"/admin/client/page",method:"get",params:t})},r=function(t){return n({url:"/admin/client",method:"post",data:t})},u=function(t){return n({url:"/admin/client/"+t,method:"get"})},i=function(t){return n({url:"/admin/client",method:"delete",data:t})},d=function(t){return n({url:"/admin/client",method:"put",data:t})},o=function(){return n({url:"/admin/client/sync",method:"put"})},c=function(t,m,e,s){if(s)return e();var a;(a=m,n({url:"/admin/client/getClientDetailsById/"+a,method:"get"})).then(f=>{f.data!==null?e(new Error("\u7F16\u53F7\u5DF2\u7ECF\u5B58\u5728")):e()})}});export{p as __tla,r as a,i as d,l as f,u as g,d as p,o as r,c as v};
