<template>
  <el-dialog
    v-model="visible"
    title="排序和筛选"
    width="600px"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    custom-class="filter-dialog"
    @close="handleClose"
  >
    <div class="filter-content">
      <!-- 搜索区域 -->
      <div class="filter-section">
        <h4>搜索条件</h4>
        <el-form :model="filterForm" label-width="100px">
          <el-form-item label="任务ID">
            <el-input
              v-model="filterForm.searchQuery"
              placeholder="搜索任务ID"
              clearable
              prefix-icon="Search"
            />
          </el-form-item>
          <el-form-item label="完成时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :popper-options="{
                placement: 'bottom-start',
                strategy: 'fixed',
                modifiers: [
                  {
                    name: 'preventOverflow',
                    options: {
                      boundary: 'viewport',
                      padding: 16,
                      altBoundary: true
                    }
                  },
                  {
                    name: 'flip',
                    options: {
                      fallbackPlacements: ['top-start', 'bottom-end', 'top-end', 'auto']
                    }
                  },
                  {
                    name: 'offset',
                    options: {
                      offset: [0, 8]
                    }
                  }
                ]
              }"
              :teleported="true"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 排序区域 -->
      <div class="filter-section">
        <h4>排序设置</h4>
        <el-form :model="filterForm" label-width="100px">
          <el-form-item label="排序字段">
            <el-select v-model="filterForm.sortField" placeholder="选择排序字段" style="width: 100%">
              <el-option label="发布时间" value="endTime" />
              <el-option label="任务ID" value="id" />
              <el-option label="图层名称" value="layer_name" />
              <el-option label="主题" value="theme" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序方式">
            <el-select v-model="filterForm.sortOrder" placeholder="选择排序方式" style="width: 100%">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

// 接口定义
interface FilterForm {
  searchQuery: string;
  dateRange: [string, string] | null;
  sortField: string;
  sortOrder: string;
}

// Props
interface Props {
  modelValue: boolean;
  searchQuery: string;
  dateRange: [string, string] | null;
  sortField: string;
  sortOrder: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  searchQuery: '',
  dateRange: null,
  sortField: 'endTime',
  sortOrder: 'desc'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'confirm': [filters: FilterForm];
}>();

// 响应式数据
const visible = ref(props.modelValue);
const filterForm = ref<FilterForm>({
  searchQuery: props.searchQuery,
  dateRange: props.dateRange,
  sortField: props.sortField,
  sortOrder: props.sortOrder
});

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听外部数据变化，同步到表单
watch(() => [props.searchQuery, props.dateRange, props.sortField, props.sortOrder], () => {
  filterForm.value = {
    searchQuery: props.searchQuery,
    dateRange: props.dateRange,
    sortField: props.sortField,
    sortOrder: props.sortOrder
  };
});

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleReset = () => {
  filterForm.value = {
    searchQuery: '',
    dateRange: null,
    sortField: 'endTime',
    sortOrder: 'desc'
  };
};

const handleConfirm = () => {
  emit('confirm', { ...filterForm.value });
  visible.value = false;
};
</script>

<style lang="scss" scoped>
.filter-content {
  max-height: 50vh;
  overflow-y: auto;
  padding-bottom: 10px;

  .filter-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

<style lang="scss">
// 全局样式，强制设置弹窗位置
.filter-dialog {
  margin-top: 5vh !important;

  .el-dialog__body {
    max-height: 50vh;
    overflow-y: auto;
  }
}
</style>
