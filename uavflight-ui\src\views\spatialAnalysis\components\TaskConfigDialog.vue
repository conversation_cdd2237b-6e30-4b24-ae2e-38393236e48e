<template>
  <el-dialog
    v-model="visible"
    title="添加分析任务"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="taskConfigForm" label-width="120px" label-position="left">
      <el-form-item label="任务ID">
        <el-input
          :value="task?.id || ''"
          readonly
          disabled
          placeholder="任务ID"
        />
      </el-form-item>

      <el-form-item label="任务类型" required>
        <el-select
          v-model="taskConfigForm.taskType"
          placeholder="请选择任务类型"
          style="width: 100%"
          @change="handleTaskTypeChange"
        >
          <el-option
            v-for="option in taskTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模型类型" required>
        <el-select
          v-model="taskConfigForm.modelType"
          placeholder="请选择模型类型"
          style="width: 100%"
          :disabled="!taskConfigForm.taskType"
          @change="handleModelTypeChange"
        >
          <el-option
            v-for="option in modelTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="模型名称" required>
        <el-select
          v-model="taskConfigForm.modelName"
          placeholder="请选择模型名称"
          style="width: 100%"
          :disabled="!taskConfigForm.modelType"
        >
          <el-option
            v-for="option in modelNameOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="待对比对象" required>
        <el-select
          v-model="taskConfigForm.shpFile"
          placeholder="请选择待对比的对象"
          style="width: 100%"
          :disabled="!taskConfigForm.taskType"
        >
          <el-option
            v-for="option in shpFileOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="过滤面积" required>
        <el-input-number
          v-model="taskConfigForm.areaThreshold"
          :min="1"
          :max="10000"
          :step="10"
          style="width: 100%"
          placeholder="请输入过滤面积阈值"
        />
        <div class="form-item-tip">单位：平方米，用于过滤小于该面积的区域</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';

interface Task {
  id: string;
}

interface WeightModel {
  name: string;
  path: string;
}

interface WeightTaskType {
  default: string;
  models: {
    [modelType: string]: WeightModel[];
  };
  display_name: string;
  default_area: number;
  shp_files: string[];
}

interface WeightInfo {
  [key: string]: WeightTaskType;
}

interface Props {
  modelValue: boolean;
  task: Task | null;
  weightInfo: WeightInfo;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'confirm': [config: any];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const taskConfigForm = ref({
  taskType: '',
  modelType: '',
  modelName: '',
  shpFile: '',
  areaThreshold: 400
});

// 计算属性
const taskTypeOptions = computed(() => {
  return Object.keys(props.weightInfo).map(key => ({
    value: key,
    label: props.weightInfo[key].display_name
  }));
});

const modelTypeOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !props.weightInfo[taskConfigForm.value.taskType]) {
    return [];
  }
  
  const models = props.weightInfo[taskConfigForm.value.taskType].models;
  return Object.keys(models).map(key => ({
    value: key,
    label: key
  }));
});

const modelNameOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !taskConfigForm.value.modelType) {
    return [];
  }
  
  const taskTypeInfo = props.weightInfo[taskConfigForm.value.taskType];
  const models = taskTypeInfo.models[taskConfigForm.value.modelType] || [];
  
  return models.map(model => ({
    value: model.name,
    label: model.name,
    path: model.path
  }));
});

const shpFileOptions = computed(() => {
  if (!taskConfigForm.value.taskType || !props.weightInfo[taskConfigForm.value.taskType]) {
    return [];
  }
  
  const shpFiles = props.weightInfo[taskConfigForm.value.taskType].shp_files;
  return shpFiles.map(file => ({
    value: file,
    label: file.split(/[/\\]/).pop()?.replace(/\.[^/.]+$/, '') || file
  }));
});

// 事件处理
const handleTaskTypeChange = () => {
  if (taskConfigForm.value.taskType && props.weightInfo[taskConfigForm.value.taskType]) {
    const taskTypeInfo = props.weightInfo[taskConfigForm.value.taskType];
    const defaultModel = taskTypeInfo.default;

    // 解析默认模型
    const [modelType, modelName] = defaultModel.split('/');

    taskConfigForm.value.modelType = modelType;
    taskConfigForm.value.modelName = modelName;

    // 默认选择第一个SHP文件
    if (taskTypeInfo.shp_files.length > 0) {
      taskConfigForm.value.shpFile = taskTypeInfo.shp_files[0];
    }

    // 设置默认过滤面积
    taskConfigForm.value.areaThreshold = taskTypeInfo.default_area || 400;
  } else {
    // 清空其他选项
    taskConfigForm.value.modelType = '';
    taskConfigForm.value.modelName = '';
    taskConfigForm.value.shpFile = '';
    taskConfigForm.value.areaThreshold = 400;
  }
};

const handleModelTypeChange = () => {
  taskConfigForm.value.modelName = '';
};

const handleConfirm = () => {
  // 验证表单
  if (!taskConfigForm.value.taskType) {
    ElMessage.error('请选择任务类型');
    return;
  }

  if (!taskConfigForm.value.modelType) {
    ElMessage.error('请选择模型类型');
    return;
  }

  if (!taskConfigForm.value.modelName) {
    ElMessage.error('请选择模型名称');
    return;
  }

  if (!taskConfigForm.value.shpFile) {
    ElMessage.error('请选择待对比的对象');
    return;
  }

  // 获取选中模型的路径
  const selectedModel = modelNameOptions.value.find(model => model.value === taskConfigForm.value.modelName);
  const modelPath = selectedModel?.path || '';

  // 生成任务名称
  const displayName = props.weightInfo[taskConfigForm.value.taskType]?.display_name || '分析';
  const now = new Date();
  const timeStr = now.getFullYear().toString() + 
                  (now.getMonth() + 1).toString().padStart(2, '0') + 
                  now.getDate().toString().padStart(2, '0') + 
                  now.getHours().toString().padStart(2, '0') + 
                  now.getMinutes().toString().padStart(2, '0');
  const taskName = `${displayName}_${timeStr}`;
  
  // 生成影像路径
  const imagePath = props.task?.id ? `D:/Drone_Project/nginxData/ODM/Output/${props.task.id}/${props.task.id}_out.tif` : '';

  // 构建任务配置对象
  const taskConfig = {
    image: imagePath,
    model: modelPath,
    old_data_path: taskConfigForm.value.shpFile,
    area_threshold: taskConfigForm.value.areaThreshold,
    model_type: taskConfigForm.value.modelType,
    id: props.task?.id || ''
  };

  // 发送确认事件
  emit('confirm', {
    name: taskName,
    type: displayName,
    config: taskConfig,
    formData: { ...taskConfigForm.value }
  });

  handleClose();
};

const handleClose = () => {
  // 重置表单
  taskConfigForm.value = {
    taskType: '',
    modelType: '',
    modelName: '',
    shpFile: '',
    areaThreshold: 400
  };
  
  visible.value = false;
};

// 监听对话框打开，重置表单
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    taskConfigForm.value = {
      taskType: '',
      modelType: '',
      modelName: '',
      shpFile: '',
      areaThreshold: 400
    };
  }
});
</script>

<style scoped lang="scss">
.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
